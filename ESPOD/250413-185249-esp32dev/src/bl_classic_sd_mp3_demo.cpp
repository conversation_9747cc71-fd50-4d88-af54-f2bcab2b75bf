/*
This piece of code is for demonstration of ability to
play music from SD card to Bluetooth device.
This is not the final code and is just for demonstration purposes.
Final code will be integrating such logic within state machine.
*/
#include <Arduino.h>
#include "AudioTools.h"
#include "AudioTools/AudioLibs/A2DPStream.h"
#include "AudioTools/Disk/AudioSourceSDFAT.h"
#include "AudioTools/AudioCodecs/CodecMP3Helix.h"
#include "config/pins.h"

const int cs = SD_CS_PIN;
const int buffer_size = 2 * 1024;
const char *startFilePath = "/";
const char *ext = "mp3";
AudioSourceSDFAT source(startFilePath, ext, cs);
MP3DecoderHelix decoder;
// Setup of synchronized buffer
BufferRTOS<uint8_t> buffer(0);
QueueStream<uint8_t> out(buffer); // convert Buffer to Stream
AudioPlayer player(source, out, decoder);

BluetoothA2DPSource a2dp_source;

// const char *file_name = "/TuSukhKartaTuDukhHarta.mp3";
// const int sd_ss_pin = 5;

// Provide data to A2DP
int32_t get_data(uint8_t *data, int32_t bytes)
{
  size_t result_bytes = buffer.readArray(data, bytes);
  // LOGI("get_data_channels %d -> %d of (%d)", bytes, result_bytes , buffer.available());
  return result_bytes;
}

// Scan callback
bool isValid(const char *ssid, esp_bd_addr_t address, int rssi)
{
  Serial.print("Available SSID: ");
  Serial.println(ssid);
  return strcmp(ssid, "Airdopes 141") == 0;
}

void connection_state_changed(esp_a2d_connection_state_t state, void *ptr)
{
  Serial.println(a2dp_source.to_str(state));
}

void demo_setup()
{
  Serial.begin(115200);
  AudioToolsLogger.begin(Serial, AudioToolsLogLevel::Info);
  delay(1000);

  buffer.resize(buffer_size);

  // start QueueStream when 95% full
  out.begin(95);

  // setup player
  player.setDelayIfOutputFull(0);
  player.setVolume(1);
  player.begin();

  Serial.println("Starting A2DP...");

  a2dp_source.set_ssid_callback(isValid);
  a2dp_source.set_auto_reconnect(false);
  a2dp_source.set_on_connection_state_changed(connection_state_changed);
  a2dp_source.set_data_callback(get_data);
  a2dp_source.set_volume(100);
  a2dp_source.start("LEXON MINO L");
}

void demo_loop()
{
  // decode data to buffer
  player.copy();
}
