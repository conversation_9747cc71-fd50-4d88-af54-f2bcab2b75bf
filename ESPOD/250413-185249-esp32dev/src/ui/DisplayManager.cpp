#include "DisplayManager.h"

// Global display manager instance
DisplayManager displayManager;

DisplayManager::DisplayManager() {
    display = nullptr;
    initialized = false;
    display_on = true;
    lastState = OFF;
    lastUpdate = 0;
    animationTimer = 0;
    animationFrame = 0;
}

DisplayManager::~DisplayManager() {
    if (display) {
        delete display;
    }
}

bool DisplayManager::init() {
    Serial.println("Initializing OLED display...");
    
    // Initialize I2C with custom pins
    Wire.begin(OLED_SDA_PIN, OLED_SCL_PIN);
    
    // Create display object
    display = new Adafruit_SSD1306(SCREEN_WIDTH, SCREEN_HEIGHT, &Wire, OLED_RESET);
    
    if (!display) {
        Serial.println("Failed to create display object");
        return false;
    }
    
    // Initialize display
    if (!display->begin(SSD1306_SWITCHCAPVCC, SCREEN_ADDRESS)) {
        Serial.println("SSD1306 allocation failed");
        delete display;
        display = nullptr;
        return false;
    }
    
    // Clear display and show startup message
    display->clearDisplay();
    display->setTextSize(1);
    display->setTextColor(SSD1306_WHITE);
    display->setCursor(0, 0);
    display->println("ESPOD Audio Player");
    display->println("Initializing...");
    display->display();
    
    initialized = true;
    Serial.println("OLED display initialized successfully");
    return true;
}

void DisplayManager::render() {
    if (!initialized || !display_on) {
        return;
    }
    
    unsigned long currentTime = millis();
    
    // Update display at regular intervals
    if (currentTime - lastUpdate < UPDATE_INTERVAL) {
        return;
    }
    
    lastUpdate = currentTime;
    
    // Render based on current device state
    switch (currentState) {
        case OFF:
            showOff();
            break;
            
        case INITIALIZING:
            showInitializing();
            break;
            
        case SEARCHING_TWS:
            showSearchingTWS();
            break;
            
        case CONNECTING:
            showConnecting();
            break;
            
        case CONNECTED:
            showConnected(audioManager.getConnectedDeviceName(), audioManager.getFileCount());
            break;
            
        case BROWSING_FILES:
            showBrowsingFiles(audioManager.getCurrentFile(),
                            audioManager.getCurrentFileIndex() + 1,
                            audioManager.getFileCount());
            break;

        case PLAYING:
            showPlayingState(audioManager.getCurrentFile(),
                           audioManager.getCurrentPosition(),
                           audioManager.getTotalDuration(),
                           audioManager.getVolume());
            break;

        case PAUSED:
            showPausedState(audioManager.getCurrentFile(),
                          audioManager.getCurrentPosition(),
                          audioManager.getTotalDuration(),
                          audioManager.getVolume());
            break;

        case STOPPED:
            showStoppedState(audioManager.getCurrentFile(), audioManager.getVolume());
            break;
    }
    
    // Show busy operation overlay if device is busy
    if (isBusy()) {
        showBusyOperation(getBusyOperation());
    }
    
    lastState = currentState;
}

void DisplayManager::forceUpdate() {
    lastUpdate = 0;
    render();
}

void DisplayManager::turnOn() {
    if (initialized) {
        display_on = true;
        display->ssd1306_command(SSD1306_DISPLAYON);
    }
}

void DisplayManager::turnOff() {
    if (initialized) {
        display_on = false;
        display->ssd1306_command(SSD1306_DISPLAYOFF);
    }
}

void DisplayManager::setBrightness(uint8_t brightness) {
    if (initialized) {
        display->ssd1306_command(SSD1306_SETCONTRAST);
        display->ssd1306_command(brightness);
    }
}

void DisplayManager::clearDisplay() {
    if (initialized) {
        display->clearDisplay();
    }
}

void DisplayManager::showInitializing() {
    clearDisplay();
    
    drawCenteredText("ESPOD", 10, 2);
    drawCenteredText("Audio Player", 30, 1);
    
    drawLoadingAnimation(SCREEN_WIDTH/2 - 10, 45);
    drawCenteredText("Initializing...", 55, 1);
    
    display->display();
}

void DisplayManager::showSearchingTWS() {
    clearDisplay();
    
    drawCenteredText("Searching for", 15, 1);
    drawCenteredText("TWS Devices", 25, 1);
    
    drawConnectingAnimation(SCREEN_WIDTH/2 - 15, 40);
    
    display->display();
}

void DisplayManager::showConnecting() {
    clearDisplay();
    
    drawCenteredText("Connecting...", 20, 1);
    drawConnectingAnimation(SCREEN_WIDTH/2 - 15, 35);
    
    display->display();
}

void DisplayManager::showConnected(const String& deviceName, int fileCount) {
    clearDisplay();
    
    display->setTextSize(1);
    display->setCursor(0, 0);
    display->println("Connected to:");
    
    String truncatedName = truncateString(deviceName, 21);
    display->println(truncatedName);
    
    display->println();
    display->print("Files found: ");
    display->println(fileCount);
    
    if (fileCount > 0) {
        display->println();
        drawCenteredText("Ready to browse", 50, 1);
    }
    
    display->display();
}

void DisplayManager::showBrowsingFiles(const String& currentFile, int currentIndex, int totalFiles) {
    clearDisplay();
    
    display->setTextSize(1);
    display->setCursor(0, 0);
    display->print("Browse (");
    display->print(currentIndex);
    display->print("/");
    display->print(totalFiles);
    display->println(")");
    
    display->println();
    
    // Show current file name (truncated if needed)
    String truncatedFile = truncateString(currentFile, 21);
    display->println(truncatedFile);
    
    // Show navigation hints
    display->println();
    display->println("UP/DOWN: Navigate");
    display->println("CENTER: Play");
    
    display->display();
}

void DisplayManager::showPlayingState(const String& filename, int currentPos, int totalDuration, int volume) {
    clearDisplay();
    
    // File name at top (truncated)
    display->setTextSize(1);
    display->setCursor(0, 0);
    String truncatedFile = truncateString(filename, 21);
    display->println(truncatedFile);
    
    // Time display
    display->println();
    String timeStr = formatTime(currentPos) + " / " + formatTime(totalDuration);
    drawCenteredText(timeStr, 18, 1);
    
    // Progress bar
    if (totalDuration > 0) {
        drawProgressBar(5, 30, SCREEN_WIDTH - 10, 8, currentPos, totalDuration);
    }
    
    // Volume bar
    display->setCursor(0, 45);
    display->print("Vol:");
    drawVolumeBar(25, 45, 60, 6, volume);
    display->setCursor(90, 45);
    display->print(volume);
    display->print("%");
    
    // Play indicator
    drawCenteredText("PLAYING", 55, 1);
    
    display->display();
}

void DisplayManager::showPausedState(const String& filename, int currentPos, int totalDuration, int volume) {
    clearDisplay();
    
    // File name at top (truncated)
    display->setTextSize(1);
    display->setCursor(0, 0);
    String truncatedFile = truncateString(filename, 21);
    display->println(truncatedFile);
    
    // Time display
    display->println();
    String timeStr = formatTime(currentPos) + " / " + formatTime(totalDuration);
    drawCenteredText(timeStr, 18, 1);
    
    // Progress bar
    if (totalDuration > 0) {
        drawProgressBar(5, 30, SCREEN_WIDTH - 10, 8, currentPos, totalDuration);
    }
    
    // Volume bar
    display->setCursor(0, 45);
    display->print("Vol:");
    drawVolumeBar(25, 45, 60, 6, volume);
    display->setCursor(90, 45);
    display->print(volume);
    display->print("%");
    
    // Pause indicator (blinking)
    if ((millis() / 500) % 2) {
        drawCenteredText("PAUSED", 55, 1);
    }
    
    display->display();
}

void DisplayManager::showStoppedState(const String& filename, int volume) {
    clearDisplay();

    // File name at top (truncated)
    display->setTextSize(1);
    display->setCursor(0, 0);
    String truncatedFile = truncateString(filename, 21);
    display->println(truncatedFile);

    display->println();
    drawCenteredText("STOPPED", 25, 1);

    // Volume bar
    display->setCursor(0, 45);
    display->print("Vol:");
    drawVolumeBar(25, 45, 60, 6, volume);
    display->setCursor(90, 45);
    display->print(volume);
    display->print("%");

    display->display();
}

void DisplayManager::showBusyOperation(BusyOperation operation) {
    // Draw overlay for busy operations
    display->fillRect(10, 20, SCREEN_WIDTH - 20, 24, SSD1306_BLACK);
    display->drawRect(10, 20, SCREEN_WIDTH - 20, 24, SSD1306_WHITE);

    String busyText = "";
    switch (operation) {
        case BUSY_NEXT_SONG:
            busyText = "Next Song...";
            break;
        case BUSY_PREV_SONG:
            busyText = "Prev Song...";
            break;
        case BUSY_PLAY:
            busyText = "Playing...";
            break;
        case BUSY_PAUSE:
            busyText = "Pausing...";
            break;
        case BUSY_STOP:
            busyText = "Stopping...";
            break;
        case BUSY_CALCULATE_DURATION:
            busyText = "Loading...";
            break;
        default:
            busyText = "Busy...";
            break;
    }

    display->setTextSize(1);
    display->setTextColor(SSD1306_WHITE);
    int textWidth = busyText.length() * 6;
    int x = (SCREEN_WIDTH - textWidth) / 2;
    display->setCursor(x, 30);
    display->print(busyText);

    display->display();
}

void DisplayManager::showOff() {
    if (initialized) {
        display->clearDisplay();
        display->display();
    }
}

// Helper methods
void DisplayManager::drawProgressBar(int x, int y, int width, int height, int progress, int total) {
    if (total <= 0) return;

    // Draw border
    display->drawRect(x, y, width, height, SSD1306_WHITE);

    // Calculate fill width
    int fillWidth = (progress * (width - 2)) / total;
    if (fillWidth > width - 2) fillWidth = width - 2;

    // Draw fill
    if (fillWidth > 0) {
        display->fillRect(x + 1, y + 1, fillWidth, height - 2, SSD1306_WHITE);
    }
}

void DisplayManager::drawVolumeBar(int x, int y, int width, int height, int volume) {
    // Draw border
    display->drawRect(x, y, width, height, SSD1306_WHITE);

    // Calculate fill width (volume is 0-100)
    int fillWidth = (volume * (width - 2)) / 100;
    if (fillWidth > width - 2) fillWidth = width - 2;

    // Draw fill
    if (fillWidth > 0) {
        display->fillRect(x + 1, y + 1, fillWidth, height - 2, SSD1306_WHITE);
    }
}

void DisplayManager::drawCenteredText(const String& text, int y, int textSize) {
    display->setTextSize(textSize);
    display->setTextColor(SSD1306_WHITE);

    int textWidth = text.length() * 6 * textSize;
    int x = (SCREEN_WIDTH - textWidth) / 2;
    if (x < 0) x = 0;

    display->setCursor(x, y);
    display->print(text);
}

void DisplayManager::drawScrollingText(const String& text, int x, int y, int maxWidth, int textSize) {
    // For now, just truncate. Could implement scrolling later
    display->setTextSize(textSize);
    display->setTextColor(SSD1306_WHITE);
    display->setCursor(x, y);

    int maxChars = maxWidth / (6 * textSize);
    String truncated = truncateString(text, maxChars);
    display->print(truncated);
}

String DisplayManager::formatTime(int seconds) {
    int minutes = seconds / 60;
    int secs = seconds % 60;

    String result = "";
    if (minutes < 10) result += "0";
    result += String(minutes);
    result += ":";
    if (secs < 10) result += "0";
    result += String(secs);

    return result;
}

String DisplayManager::truncateString(const String& str, int maxChars) {
    if (str.length() <= maxChars) {
        return str;
    }

    return str.substring(0, maxChars - 3) + "...";
}

void DisplayManager::drawLoadingAnimation(int x, int y) {
    unsigned long currentTime = millis();
    if (currentTime - animationTimer > 200) {
        animationTimer = currentTime;
        animationFrame = (animationFrame + 1) % 4;
    }

    // Draw spinning dots
    for (int i = 0; i < 4; i++) {
        int dotX = x + i * 5;
        if (i == animationFrame) {
            display->fillCircle(dotX, y, 2, SSD1306_WHITE);
        } else {
            display->drawCircle(dotX, y, 2, SSD1306_WHITE);
        }
    }
}

void DisplayManager::drawConnectingAnimation(int x, int y) {
    unsigned long currentTime = millis();
    if (currentTime - animationTimer > 300) {
        animationTimer = currentTime;
        animationFrame = (animationFrame + 1) % 3;
    }

    // Draw expanding circles
    for (int i = 0; i <= animationFrame; i++) {
        int radius = 5 + i * 5;
        display->drawCircle(x + 15, y + 10, radius, SSD1306_WHITE);
    }

    // Draw center dot
    display->fillCircle(x + 15, y + 10, 3, SSD1306_WHITE);
}
