#ifndef BREATHER_H
#define BREATHER_H

#include <Arduino.h>
#include "../config/pins.h"

#define LED_PIN ONBOARD_LED_PIN  // Onboard LED
#define LED_BRIGHTNESS 10  // Brightness (0-255, lower = dimmer)
#define LEDC_CHANNEL 0     // PWM channel (0-15 for ESP32)
#define LEDC_FREQ 5000     // PWM frequency in Hz
#define LEDC_RES 8         // PWM resolution in bits

void breathe();
void Breather(void *pvParameters);
void initBreather();

#endif
