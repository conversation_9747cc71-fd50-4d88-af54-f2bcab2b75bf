#ifndef PINS_H
#define PINS_H

#include <Arduino.h>

// =============================================================================
// WeAct ESP32 Core Board Pin Configuration
// =============================================================================

// =============================================================================
// SD CARD PINS (SPI)
// =============================================================================
#define SPI_MOSI_PIN    GPIO_NUM_21
#define SPI_MISO_PIN    GPIO_NUM_19
#define SPI_SCK_PIN     GPIO_NUM_18
#define SD_CS_PIN       GPIO_NUM_32

// =============================================================================
// OLED DISPLAY PINS (I2C)
// =============================================================================
#define I2C_SDA_PIN     GPIO_NUM_27
#define I2C_SCL_PIN     GPIO_NUM_12
#define OLED_I2C_ADDR   0x3C

// =============================================================================
// BUTTON PINS (Updated per your preferences)
// =============================================================================
#define BTN_UP_PIN      GPIO_NUM_17
#define BTN_RIGHT_PIN   GPIO_NUM_4
#define BTN_DOWN_PIN    GPIO_NUM_25
#define BTN_LEFT_PIN    GPIO_NUM_14
#define BTN_CENTER_PIN  GPIO_NUM_2   // Changed from GPIO_NUM_0 for safer boot behavior
#define BTN_SET_PIN     GPIO_NUM_15
#define BTN_RST_PIN     GPIO_NUM_33

// =============================================================================
// BATTERY MONITORING
// =============================================================================
#define BATTERY_PIN     GPIO_NUM_34  // VDET1 (input only, ADC)

// =============================================================================
// RGB LED PIN
// =============================================================================
#define RGB_LED_PIN     GPIO_NUM_13

// =============================================================================
// ONBOARD LED
// =============================================================================
#define ONBOARD_LED_PIN GPIO_NUM_22

// =============================================================================
// DISPLAY CONFIGURATION
// =============================================================================
#define SCREEN_WIDTH    128
#define SCREEN_HEIGHT   32
#define OLED_RESET      -1

#endif // PINS_H
