#ifndef DEVICE_STATE_H
#define DEVICE_STATE_H

enum DeviceState {
  OFF,
  INITIALIZING,
  SEARCHING_TWS,
  CONNECTING,
  CONNECTED,
  BROWSING_FILES,
  PLAYING,
  PAUSED,
  STOPPED,
  <PERSON>TA_MODE,
  OTA_UPDATING
};

// Busy operation types for global device state
enum BusyOperation {
  BUSY_NONE,
  BUSY_NEXT_SONG,
  BUSY_PREV_SONG,
  BUSY_PLAY,
  BUSY_PAUSE,
  BUSY_STOP,
  BUSY_CALCULATE_DURATION,
  BUSY_OTA_INIT,
  BUSY_OTA_UPDATE
};

extern volatile DeviceState currentState;

// Global busy state management
extern volatile bool deviceBusy;
extern volatile BusyOperation deviceBusyFor;

// Global busy state functions
bool isBusy();
BusyOperation getBusyOperation();
void setBusy(BusyOperation operation);
void clearBusy();

#endif
