#include <Arduino.h>
#include "animations/Breather.h"
#include "audio/AudioManager.h"
#include "ui/Buttons.h"
#include "ui/DisplayManager.h"
#include "core/DeviceState.h"
#include "utils/sleeper.h"
#include "network/OTAManager.h"
#include "config/WiFiConfig.h"
#include "config/pins.h"

// currentState is now defined in DeviceState.cpp

TaskHandle_t audioTaskHandle;

// Forward declarations
void onButtonPress(button_id_t button);
void onLongPress(button_id_t button);
void onConnectionChanged(bool connected, const char* device_name);
void onPlaybackChanged(AudioState state, const char* filename);

void renderInitializing();
void renderSearchingTWS();
void renderConnecting();
void renderConnectedState();
void renderBrowsingFiles();
void renderPlayingState();
void renderPausedState();
void renderStoppedState();
void renderFMBrowsing();
void renderFMPlaying();
void renderFMPaused();
void renderOTAMode();
void renderOTAUpdating();

// OTA callback functions
void onOTAStart();
void onOTAEnd();
void onOTAProgress(unsigned int progress, unsigned int total);
void onOTAError(ota_error_t error);

// AudioManager FreeRTOS Task for Core 0
void audioTask(void *pvParameters) {
  if (audioManager.init(SD_CS_PIN, "ESPOD")) {
    Serial.println("AudioManager initialized successfully");
    audioManager.setConnectionCallback(onConnectionChanged);
    audioManager.setPlaybackCallback(onPlaybackChanged);
    currentState = SEARCHING_TWS;
  } else {
    Serial.println("Failed to initialize AudioManager");
    currentState = OFF;
  }

  while (true) {
    audioManager.loop();
    vTaskDelay(10 / portTICK_PERIOD_MS); // Small delay to yield Core 0
  }
}

void setup() {
  Serial.begin(115200);
  Serial.println("\n\n=== ESPOD Bluetooth A2DP MP3 Player ===\n");
  delay(1000); // Give Serial time

  // Initialize display first
  if (!displayManager.init()) {
    Serial.println("Failed to initialize display");
  }

  modifyJoystickForNormalUse();
  initBreather();
  initButtons(onButtonPress, onLongPress);

  // OTA will be initialized only when SET button is long pressed
  // No WiFi connection at startup - saves power and boot time

  // Start the audio task on Core 0
  xTaskCreatePinnedToCore(
    audioTask,           // Function to run
    "AudioTask",         // Name
    8192,                // Stack size
    NULL,                // Parameter
    1,                   // Priority
    &audioTaskHandle,    // Task handle
    1                    // Core 0
  );
}

void loop() {
  // Handle OTA updates only if OTA is active
  #if ENABLE_OTA
  if (otaManager.isWiFiConnected()) {
    otaManager.handle();

    // If OTA is in progress, don't do anything else
    if (otaManager.isOTAInProgress()) {
      if (currentState != OTA_UPDATING) {
        currentState = OTA_UPDATING;
      }
      delay(10);
      return;
    }
  }
  #endif

  if(isBusy()) {
    // Serial.printf("[MAIN] Device is busy: %d\n", getBusyOperation());
    delay(100);
    return;
  }

  // Update display based on current state
  displayManager.render();

  // Handle state transitions (keep existing logic)
  switch (currentState) {
    case OFF:
      sleepWell();
      break;

    case INITIALIZING:
      renderInitializing();
      break;

    case SEARCHING_TWS:
      renderSearchingTWS();
      break;

    case CONNECTING:
      renderConnecting();
      break;

    case CONNECTED:
      renderConnectedState();
      break;

    case BROWSING_FILES:
      renderBrowsingFiles();
      break;

    case PLAYING:
      renderPlayingState();
      break;

    case PAUSED:
      renderPausedState();
      break;

    case STOPPED:
      renderStoppedState();
      break;

    case OTA_MODE:
      renderOTAMode();
      break;

    case OTA_UPDATING:
      renderOTAUpdating();
      break;

    default:
      Serial.print("Unknown state: ");
      Serial.println(currentState);
      currentState = SEARCHING_TWS;
  }

  delay(100); // Keep loop responsive
}

// --- Renders ---
void renderInitializing() {
  // Display manager handles the visual rendering
  // Just add a small delay for state transition
  delay(1000);
}

void renderSearchingTWS() {
  // Start discovery if not already started
  static bool discoveryStarted = false;
  if (!discoveryStarted) {
    Serial.println("Starting TWS device discovery...");
    audioManager.startDiscovery();
    discoveryStarted = true;
  }
  delay(500);
}

void renderConnecting() {
  // Display manager handles the visual rendering
  delay(500);
}

void renderConnectedState() {
  Serial.println("Connected to: " + audioManager.getConnectedDeviceName());
  Serial.printf("Files available: %d\n", audioManager.getFileCount());

  if (!audioManager.isConnected()) {
    Serial.println("Connection lost!");
    currentState = SEARCHING_TWS;
  } else if (audioManager.getFileCount() > 0) {
    currentState = BROWSING_FILES;
  }

  delay(2000);
}

void renderBrowsingFiles() {
  Serial.printf("Browsing files (%d/%d): %s\n",
                audioManager.getCurrentFileIndex() + 1,
                audioManager.getFileCount(),
                audioManager.getCurrentFile().c_str());
  delay(1000);
}

void renderPlayingState() {
  String currentFile = audioManager.getCurrentFile();
  int currentPosition = audioManager.getCurrentPosition(); // Now using AudioTimer for accurate timing
  int totalDuration = audioManager.getTotalDuration();     // Duration from MP3 metadata

  // Format time as MM:SS
  int currentMin = currentPosition / 60;
  int currentSec = currentPosition % 60;
  int totalMin = totalDuration / 60;
  int totalSec = totalDuration % 60;

  Serial.printf("Playing: %s [%02d:%02d/%02d:%02d]\n",
                currentFile.c_str(),
                currentMin, currentSec,
                totalMin, totalSec);

  if (!audioManager.isConnected()) {
    Serial.println("Connection lost!");
    currentState = SEARCHING_TWS;
  }

  delay(1000);
}

void renderPausedState() {
  Serial.printf("Paused: %s\n", audioManager.getCurrentFile().c_str());

  if (!audioManager.isConnected()) {
    Serial.println("Connection lost!");
    currentState = SEARCHING_TWS;
  }

  delay(1000);
}

void renderStoppedState() {
  Serial.printf("Stopped: %s\n", audioManager.getCurrentFile().c_str());

  if (!audioManager.isConnected()) {
    Serial.println("Connection lost!");
    currentState = SEARCHING_TWS;
  }

  delay(1000);
}

void renderOTAMode() {
  Serial.println("=== OTA MODE ACTIVE ===");
  Serial.print("IP Address: ");
  Serial.println(otaManager.getIPAddress());
  Serial.println("Ready for firmware upload!");
  Serial.println("Upload command: ~/.platformio/penv/bin/pio run --target upload --upload-port " + otaManager.getIPAddress());
  Serial.println("Press any button to exit OTA mode");
  delay(3000);
}

void renderOTAUpdating() {
  Serial.println("OTA Update in progress...");
  delay(500);
}

// --- Button Handlers ---
void onButtonPress(button_id_t button) {
  Serial.print("Button Pressed: ");
  Serial.println(button);

  switch (currentState) {
    case OFF:
      break;

    case INITIALIZING:
      break;

    case SEARCHING_TWS:
      if (button == BTN_CENTER) {
        audioManager.connectToDevice("TWS");
        currentState = CONNECTING;
      }
      break;

    case CONNECTING:
      break;

    case CONNECTED:
      if (button == BTN_CENTER) {
        currentState = BROWSING_FILES;
      }
      break;

    case BROWSING_FILES:
      if (button == BTN_UP) {
        audioManager.requestPreviousTrack();
      } else if (button == BTN_DOWN) {
        audioManager.requestNextTrack();
      } else if (button == BTN_CENTER) {
        audioManager.requestPlay();
        currentState = PLAYING;
      }
      break;

    case PLAYING:
      if (button == BTN_UP) {
        int vol = audioManager.getVolume();
        audioManager.setVolume(min(100, vol + 10));
      } else if (button == BTN_DOWN) {
        int vol = audioManager.getVolume();
        audioManager.setVolume(max(0, vol - 10));
      } else if (button == BTN_LEFT) {
        audioManager.requestPreviousTrack();
      } else if (button == BTN_RIGHT) {
        audioManager.requestNextTrack();
      } else if (button == BTN_CENTER) {
        audioManager.requestPause();
        currentState = PAUSED;
      }
      break;

    case PAUSED:
    case STOPPED:
      if (button == BTN_UP) {
        int vol = audioManager.getVolume();
        audioManager.setVolume(min(100, vol + 10));
      } else if (button == BTN_DOWN) {
        int vol = audioManager.getVolume();
        audioManager.setVolume(max(0, vol - 10));
      } else if (button == BTN_LEFT) {
        audioManager.requestPreviousTrack();
      } else if (button == BTN_RIGHT) {
        audioManager.requestNextTrack();
      } else if (button == BTN_CENTER) {
        audioManager.requestPlay();
        currentState = PLAYING;
      }
      break;

    case OTA_MODE:
      // Any button press exits OTA mode and disconnects WiFi
      Serial.println("Exiting OTA mode...");
      #if ENABLE_OTA
      otaManager.disconnect();
      Serial.println("WiFi disconnected to save power");
      #endif
      currentState = SEARCHING_TWS;
      break;

    case OTA_UPDATING:
      // No button handling during OTA update
      break;

    default:
      Serial.println("Unknown state for button press");
  }
}

void onLongPress(button_id_t button) {
  Serial.printf("Long press detected on button: %d\n", button);

  if (button == BTN_RST) {
    if (currentState == OFF) {
      Serial.println("Waking up device...");
      currentState = SEARCHING_TWS;
    } else {
      Serial.println("Turning off device...");
      if (audioManager.isConnected()) {
        audioManager.disconnect();
      }
      currentState = OFF;
    }
  }

  #if ENABLE_OTA
  // Long press SET button to initialize and enter OTA mode
  if (button == BTN_SET && currentState != OTA_MODE && currentState != OTA_UPDATING) {
    Serial.println("SET button long press - Initializing OTA...");

    // Initialize WiFi and OTA
    if (otaManager.init(WIFI_SSID, WIFI_PASSWORD, OTA_PASSWORD, OTA_HOSTNAME)) {
      Serial.println("OTA initialized successfully");
      otaManager.setOnStartCallback(onOTAStart);
      otaManager.setOnEndCallback(onOTAEnd);
      otaManager.setOnProgressCallback(onOTAProgress);
      otaManager.setOnErrorCallback(onOTAError);
      currentState = OTA_MODE;
      Serial.print("OTA Ready! IP Address: ");
      Serial.println(otaManager.getIPAddress());
    } else {
      Serial.println("OTA initialization failed - check WiFi credentials");
    }
  }
  #endif
}

// --- Audio Manager callbacks ---
void onConnectionChanged(bool connected, const char* device_name) {
  if (connected) {
    Serial.printf("Connected to: %s\n", device_name);
    currentState = CONNECTED;
  } else {
    Serial.println("Disconnected from device");
    currentState = SEARCHING_TWS;
  }
}

void onPlaybackChanged(AudioState state, const char* filename) {
  Serial.printf("Playback state changed: %d, file: %s\n", state, filename);

  switch (state) {
    case AUDIO_PLAYING:
      currentState = PLAYING;
      break;
    case AUDIO_PAUSED:
      currentState = PAUSED;
      break;
    case AUDIO_STOPPED:
      currentState = STOPPED;
      break;
    case AUDIO_CONNECTING:
      currentState = CONNECTING;
      break;
    case AUDIO_DISCONNECTED:
      currentState = SEARCHING_TWS;
      break;
  }
}

// --- OTA Callback Functions ---
void onOTAStart() {
  Serial.println("[OTA] Update started");
  currentState = OTA_UPDATING;
  setBusy(BUSY_OTA_UPDATE);
}

void onOTAEnd() {
  Serial.println("[OTA] Update completed");
  clearBusy();
  // Device will restart automatically
}

void onOTAProgress(unsigned int progress, unsigned int total) {
  // Progress is already printed in OTAManager
  // Could update display here if needed
}

void onOTAError(ota_error_t error) {
  Serial.printf("[OTA] Update failed with error: %u\n", error);
  clearBusy();
  currentState = SEARCHING_TWS; // Return to normal operation
}
