#pragma once

#include <Arduino.h>
#include "AudioTools.h"
#include "AudioTools/Disk/AudioSourceSDFAT.h"
#include "AudioTools/AudioCodecs/CodecMP3Helix.h"
#include "AudioTools/AudioCodecs/CodecCopy.h"
#include "AudioTools/AudioLibs/A2DPStream.h"
#include "AudioTools/CoreAudio/AudioTimer.h"

#include "CustomMP3Parser.h" // <-- include your new helper
#include "../core/DeviceState.h" // For BusyOperation enum and global busy functions
#include "../config/pins.h" // For pin definitions



// Audio playback states
enum AudioState
{
  AUDIO_STOPPED,
  AUDIO_PLAYING,
  AUDIO_PAUSED,
  AUDIO_CONNECTING,
  AUDIO_DISCONNECTED
};

// BusyOperation enum is now in DeviceState.h

class MyAudioSource : public audio_tools::AudioSourceSDFAT
{
public:
  using AudioSourceSDFAT::AudioSourceSDFAT; // inherit constructor

  uint32_t fileSize()
  {
    return file.size();
  }

  int getMP3Duration() {
    const char* path = toStr();
    if (path == nullptr || strlen(path) == 0) {
      Serial.println("[DURATION] Invalid file path!");
      return 180;
    }

    AudioFile tempFile;
    if (!tempFile.open(path, O_RDONLY)) {
      Serial.printf("[DURATION] Failed to open file: %s\n", path);
      return 180;
    }

    const size_t chunkSize = 512;
    uint8_t tmp[chunkSize];
    int bitrate = 0;

    while (tempFile.available()) {
      int len = tempFile.readBytes((char*)tmp, chunkSize);

      for (int i = 0; i < len - 4; i++) {
        bitrate = CustomMP3Parser::parseBitrateFromFrame(&tmp[i]);
        if (bitrate > 0) {
          // Serial.printf("[DURATION] Found valid MP3 frame! Bitrate: %d bps\n", bitrate);
          break;
        }
      }

      if (bitrate > 0) {
        break;
      }

      // Step back half a chunk to handle frame crossing
      tempFile.seek(tempFile.position() - (chunkSize / 2));
    }

    size_t fileSize = tempFile.size();
    tempFile.close();

    if (bitrate <= 0) {
      Serial.println("[DURATION] Failed to find valid bitrate, returning default 180");
      return 180;
    }

    int duration = (fileSize * 8) / bitrate;
    // Serial.printf("[DURATION] Calculated duration: %d seconds\n", duration);

    return duration > 0 ? duration : 180;
  }
};



// Audio manager class for handling SD card MP3 playback and Bluetooth A2DP streaming
class AudioManager
{
public:
  AudioManager();
  ~AudioManager();

  // Initialize audio system
  bool init(int sd_cs_pin = SD_CS_PIN, const char *device_name = "ESPOD");

  // Bluetooth device discovery and connection
  bool startDiscovery();
  bool connectToDevice(const char *device_name);
  bool isConnected();
  String getConnectedDeviceName();
  bool disconnect();



  // Audio playback controls
  bool play();
  bool pause();
  bool stop();
  bool nextTrack();
  bool previousTrack();
  bool setVolume(int volume); // 0-100
  int getVolume();



  // File management
  bool setCurrentFile(const char *filename);
  String getCurrentFile();
  bool hasNextFile();
  bool hasPreviousFile();
  int getFileCount();
  int getCurrentFileIndex();

  // State management
  AudioState getState();
  bool isPlaying();
  bool isPaused();
  // isBusy() and getBusyOperation() are now global functions in DeviceState.h

  // Non-blocking operations (set flags for processing in loop)
  bool requestNextTrack();
  bool requestPreviousTrack();
  bool requestPlay();
  bool requestPause();
  bool requestStop();



  // Main loop function - must be called regularly
  void loop();

  // Callbacks for state changes
  void setConnectionCallback(void (*callback)(bool connected, const char *device_name));
  void setPlaybackCallback(void (*callback)(AudioState state, const char *filename));

  int getCurrentPosition(); // Get current playback position in seconds
  int getTotalDuration();   // Get total duration of current file in seconds

private:
  // Audio components
  MyAudioSource *source;
  MP3DecoderHelix *decoder;
  BufferRTOS<uint8_t> *buffer;
  QueueStream<uint8_t> *out;
  AudioPlayer *player;  // For SD card MP3 playback
  BluetoothA2DPSource *a2dp_source;



  // State variables
  AudioState current_state;
  String connected_device_name;
  String current_file;
  int current_volume;
  int sd_cs_pin;

  // Busy state is now managed globally in DeviceState

  // File management
  int file_count;
  int current_file_index;

  // Playback timing
  unsigned long playback_start_time;
  int current_position; // Current position in seconds
  int total_duration;   // Total duration in seconds

  // AudioTimer for accurate timing
  audio_tools::TimerAlarmRepeating position_timer;
  volatile bool timer_active;

  // Callbacks
  void (*connection_callback)(bool connected, const char *device_name);
  void (*playback_callback)(AudioState state, const char *filename);

  // Internal methods
  void updateFileList();
  bool initializeAudioComponents();
  void cleanupAudioComponents();
  bool calculateMP3Duration(const char *filename);
  int calculateMP3DurationInternal(const char *filename); // Internal version without mutex
  static int32_t getData(uint8_t *data, int32_t bytes);
  static bool deviceScanCallback(const char *ssid, esp_bd_addr_t address, int rssi);
  static void connectionStateChanged(esp_a2d_connection_state_t state, void *ptr);

  // FM radio methods
  bool initializeFMRadio();
  void cleanupFMRadio();
  bool switchToSDCard();
  bool switchToFMRadio();

  // Busy operation processing (called from loop)
  void processBusyOperations();
  void executeNextTrack();
  void executePreviousTrack();
  void executePlay();
  void executePause();
  void executeStop();

  // Timer callback for position tracking
  static void positionTimerCallback(void *obj);
  void startPositionTimer();
  void stopPositionTimer();

  // Static instance for callbacks
  static AudioManager *instance;
};

// Global audio manager instance
extern AudioManager audioManager;
