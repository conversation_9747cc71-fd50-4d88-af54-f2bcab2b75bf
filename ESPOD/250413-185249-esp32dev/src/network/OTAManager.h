#ifndef OTA_MANAGER_H
#define OTA_MANAGER_H

#include <WiFi.h>
#include <ArduinoOTA.h>

class OTAManager {
private:
    bool initialized;
    bool otaInProgress;
    String deviceName;
    String password;
    
    // Callback function pointers
    void (*onStartCallback)();
    void (*onEndCallback)();
    void (*onProgressCallback)(unsigned int progress, unsigned int total);
    void (*onErrorCallback)(ota_error_t error);

public:
    OTAManager();
    
    // Initialize OTA with WiFi credentials
    bool init(const char* ssid, const char* wifiPassword, const char* otaPassword = "", const char* hostname = "ESPOD");
    
    // Initialize OTA with existing WiFi connection
    bool initWithExistingWiFi(const char* otaPassword = "", const char* hostname = "ESPOD");
    
    // Handle OTA updates (call in loop)
    void handle();
    
    // Check if OTA is in progress
    bool isOTAInProgress() const;
    
    // Check if WiFi is connected
    bool isWiFiConnected() const;
    
    // Get WiFi status
    String getWiFiStatus() const;
    
    // Get IP address
    String getIPAddress() const;
    
    // Set callback functions
    void setOnStartCallback(void (*callback)());
    void setOnEndCallback(void (*callback)());
    void setOnProgressCallback(void (*callback)(unsigned int progress, unsigned int total));
    void setOnErrorCallback(void (*callback)(ota_error_t error));
    
    // Utility functions
    void disconnect();
    void restart();
};

extern OTAManager otaManager;

#endif
