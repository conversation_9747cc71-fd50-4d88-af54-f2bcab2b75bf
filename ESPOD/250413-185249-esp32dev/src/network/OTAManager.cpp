#include "OTAManager.h"
#include <WiFi.h>
#include <ArduinoOTA.h>

// Global instance
OTAManager otaManager;

OTAManager::OTAManager() : 
    initialized(false), 
    otaInProgress(false),
    deviceName("ESPOD"),
    password(""),
    onStartCallback(nullptr),
    onEndCallback(nullptr),
    onProgressCallback(nullptr),
    onErrorCallback(nullptr) {
}

bool OTAManager::init(const char* ssid, const char* wifiPassword, const char* otaPassword, const char* hostname) {
    if (initialized) {
        Serial.println("[OTA] Already initialized");
        return true;
    }
    
    deviceName = String(hostname);
    password = String(otaPassword);
    
    Serial.println("[OTA] Connecting to WiFi...");
    WiFi.mode(WIFI_STA);
    WiFi.begin(ssid, wifiPassword);
    
    // Wait for connection with timeout
    int attempts = 0;
    while (WiFi.status() != WL_CONNECTED && attempts < 20) {
        delay(500);
        Serial.print(".");
        attempts++;
    }
    
    if (WiFi.status() != WL_CONNECTED) {
        Serial.println("\n[OTA] WiFi connection failed");
        return false;
    }
    
    Serial.println("\n[OTA] WiFi connected");
    Serial.print("[OTA] IP address: ");
    Serial.println(WiFi.localIP());
    
    return initWithExistingWiFi(otaPassword, hostname);
}

bool OTAManager::initWithExistingWiFi(const char* otaPassword, const char* hostname) {
    if (initialized) {
        Serial.println("[OTA] Already initialized");
        return true;
    }
    
    if (WiFi.status() != WL_CONNECTED) {
        Serial.println("[OTA] WiFi not connected");
        return false;
    }
    
    deviceName = String(hostname);
    password = String(otaPassword);
    
    // Configure ArduinoOTA
    ArduinoOTA.setHostname(hostname);
    
    if (strlen(otaPassword) > 0) {
        ArduinoOTA.setPassword(otaPassword);
        Serial.println("[OTA] Password protection enabled");
    }
    
    // Set up OTA callbacks
    ArduinoOTA.onStart([this]() {
        String type;
        if (ArduinoOTA.getCommand() == U_FLASH) {
            type = "sketch";
        } else { // U_SPIFFS
            type = "filesystem";
        }
        
        Serial.println("[OTA] Start updating " + type);
        otaInProgress = true;
        
        if (onStartCallback) {
            onStartCallback();
        }
    });
    
    ArduinoOTA.onEnd([this]() {
        Serial.println("\n[OTA] Update complete");
        otaInProgress = false;
        
        if (onEndCallback) {
            onEndCallback();
        }
    });
    
    ArduinoOTA.onProgress([this](unsigned int progress, unsigned int total) {
        unsigned int percent = (progress / (total / 100));
        Serial.printf("[OTA] Progress: %u%%\r", percent);
        
        if (onProgressCallback) {
            onProgressCallback(progress, total);
        }
    });
    
    ArduinoOTA.onError([this](ota_error_t error) {
        Serial.printf("[OTA] Error[%u]: ", error);
        if (error == OTA_AUTH_ERROR) {
            Serial.println("Auth Failed");
        } else if (error == OTA_BEGIN_ERROR) {
            Serial.println("Begin Failed");
        } else if (error == OTA_CONNECT_ERROR) {
            Serial.println("Connect Failed");
        } else if (error == OTA_RECEIVE_ERROR) {
            Serial.println("Receive Failed");
        } else if (error == OTA_END_ERROR) {
            Serial.println("End Failed");
        }
        
        otaInProgress = false;
        
        if (onErrorCallback) {
            onErrorCallback(error);
        }
    });
    
    ArduinoOTA.begin();
    Serial.println("[OTA] Ready");
    Serial.print("[OTA] IP address: ");
    Serial.println(WiFi.localIP());
    
    initialized = true;
    return true;
}

void OTAManager::handle() {
    if (!initialized) {
        return;
    }
    
    ArduinoOTA.handle();
}

bool OTAManager::isOTAInProgress() const {
    return otaInProgress;
}

bool OTAManager::isWiFiConnected() const {
    return WiFi.status() == WL_CONNECTED;
}

String OTAManager::getWiFiStatus() const {
    switch (WiFi.status()) {
        case WL_CONNECTED:
            return "Connected";
        case WL_NO_SSID_AVAIL:
            return "SSID not available";
        case WL_CONNECT_FAILED:
            return "Connection failed";
        case WL_CONNECTION_LOST:
            return "Connection lost";
        case WL_DISCONNECTED:
            return "Disconnected";
        default:
            return "Unknown";
    }
}

String OTAManager::getIPAddress() const {
    if (isWiFiConnected()) {
        return WiFi.localIP().toString();
    }
    return "Not connected";
}

void OTAManager::setOnStartCallback(void (*callback)()) {
    onStartCallback = callback;
}

void OTAManager::setOnEndCallback(void (*callback)()) {
    onEndCallback = callback;
}

void OTAManager::setOnProgressCallback(void (*callback)(unsigned int progress, unsigned int total)) {
    onProgressCallback = callback;
}

void OTAManager::setOnErrorCallback(void (*callback)(ota_error_t error)) {
    onErrorCallback = callback;
}

void OTAManager::disconnect() {
    if (initialized) {
        WiFi.disconnect();
        Serial.println("[OTA] WiFi disconnected");
    }
}

void OTAManager::restart() {
    Serial.println("[OTA] Restarting device...");
    delay(1000);
    ESP.restart();
}
