/*
 * Simple OTA Example for ESPOD
 * 
 * This example shows how to use the OTA functionality independently
 * from the main ESPOD application.
 * 
 * To use this example:
 * 1. Update WiFiConfig.h with your WiFi credentials
 * 2. Replace main.cpp with this file temporarily
 * 3. Build and upload via USB
 * 4. After first upload, you can use OTA for subsequent updates
 */

#include <Arduino.h>
#include "network/OTAManager.h"
#include "config/WiFiConfig.h"

void setup() {
    Serial.begin(115200);
    Serial.println("\n=== ESPOD OTA Example ===");
    
    // Initialize OTA
    if (otaManager.init(WIFI_SSID, WIFI_PASSWORD, OTA_PASSWORD, OTA_HOSTNAME)) {
        Serial.println("OTA initialized successfully!");
        Serial.print("Device IP: ");
        Serial.println(otaManager.getIPAddress());
        Serial.println("Ready for OTA updates!");
        
        // Set up callbacks (optional)
        otaManager.setOnStartCallback([]() {
            Serial.println("OTA Update Started!");
        });
        
        otaManager.setOnEndCallback([]() {
            Serial.println("OTA Update Completed!");
        });
        
        otaManager.setOnProgressCallback([](unsigned int progress, unsigned int total) {
            Serial.printf("Progress: %u%%\r", (progress / (total / 100)));
        });
        
        otaManager.setOnErrorCallback([](ota_error_t error) {
            Serial.printf("OTA Error: %u\n", error);
        });
    } else {
        Serial.println("OTA initialization failed!");
    }
}

void loop() {
    // Handle OTA updates
    otaManager.handle();
    
    // Your application code here
    static unsigned long lastPrint = 0;
    if (millis() - lastPrint > 5000) {
        Serial.println("Device running... Ready for OTA updates");
        Serial.print("WiFi Status: ");
        Serial.println(otaManager.getWiFiStatus());
        Serial.print("IP Address: ");
        Serial.println(otaManager.getIPAddress());
        lastPrint = millis();
    }
    
    delay(100);
}
