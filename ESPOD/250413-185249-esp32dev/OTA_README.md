# ESPOD OTA (Over-The-Air) Update Guide

## Overview

The ESPOD project now includes simple OTA update functionality that allows you to upload new firmware wirelessly without connecting a USB cable.

## Setup

### 1. Configure WiFi Credentials

Edit the file `src/config/WiFiConfig.h` and update the following:

```cpp
#define WIFI_SSID "YOUR_WIFI_SSID"        // Your WiFi network name
#define WIFI_PASSWORD "YOUR_WIFI_PASSWORD" // Your WiFi password
#define OTA_PASSWORD "espod123"            // Change to a secure password
```

### 2. Enable/Disable OTA

OTA is enabled by default. To disable it, set:

```cpp
#define ENABLE_OTA false
```

## Usage

### Entering OTA Mode

1. **Long press the SET button (GPIO 15)** to initialize WiFi and enter OTA mode
2. <PERSON>ce will connect to WiFi and display "OTA MODE ACTIVE" with the IP address
3. W<PERSON><PERSON><PERSON> only connects when OTA is needed (saves power and boot time)
4. Press any button to exit OTA mode and disconnect WiFi

### Uploading Firmware via OTA

#### Method 1: Using PlatformIO (Recommended)

1. <PERSON> press SET button to enter OTA mode and get the device IP address
2. In your terminal, run:
   ```bash
   ~/.platformio/penv/bin/pio run --target upload --upload-port <DEVICE_IP_ADDRESS>
   ```
   Replace `<DEVICE_IP_ADDRESS>` with the IP shown on the device

#### Method 2: Using Arduino IDE

1. In Arduino IDE, go to Tools > Port
2. Select the network port that shows your device (e.g., "ESPOD at *************")
3. Upload normally using Ctrl+U (Cmd+U on Mac)

#### Method 3: Using espota.py directly

```bash
python ~/.platformio/packages/framework-arduinoespressif32/tools/espota.py \
  -i <DEVICE_IP> -p 3232 --auth=espod123 -f .pio/build/esp32dev/firmware.bin
```

## Security Notes

- Change the default OTA password in `WiFiConfig.h`
- OTA is only available when the device is connected to your WiFi network
- The device will restart automatically after a successful OTA update

## Troubleshooting

### Device Not Found

- Ensure the device is connected to the same WiFi network as your computer
- Check that the IP address is correct
- Make sure the device is powered on and OTA is enabled

### Upload Fails

- Verify the OTA password is correct
- Check that no firewall is blocking the connection
- Ensure the device has enough free memory for the update

### WiFi Connection Issues

- Double-check WiFi credentials in `WiFiConfig.h`
- Ensure your WiFi network is 2.4GHz (ESP32 doesn't support 5GHz)
- Check signal strength - device should be close to the router

## Technical Details

- OTA uses port 3232 (default Arduino OTA port)
- The device hostname is "ESPOD" by default
- OTA updates are handled on the main loop with high priority
- During OTA updates, all other device functions are paused
- The device automatically restarts after successful updates

## LED/Display Indicators

- Normal operation: Standard ESPOD display
- OTA Mode: Shows "OTA Mode - Waiting for updates..." with IP address
- OTA Updating: Shows "OTA Update in progress..."
