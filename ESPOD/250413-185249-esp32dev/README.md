# ESPOD - ESP32 Bluetooth A2DP MP3 Player

A feature-rich ESP32-based audio player with Bluetooth A2DP source capability, SD card MP3 playback, and OLED display interface.

## 🔌 Hardware Pinout & Connections

### ESP32 DevKit Board

- **Board**: ESP32 DevKit (30-pin)
- **Framework**: Arduino
- **Platform**: Espressif32

### 📟 OLED Display (SSD1306 128x32)

```
OLED Pin    ESP32 Pin    Function
VCC         3.3V         Power
GND         GND          Ground
SDA         GPIO 27      I2C Data
SCL         GPIO 12      I2C Clock
```

- **I2C Address**: 0x3C
- **Resolution**: 128x32 pixels
- **Interface**: I2C

### 🎮 7-Button Joystick/Control Pad

```
Button      ESP32 Pin    Function
UP          GPIO 17      Navigate up/Volume up
DOWN        GPIO 25      Navigate down/Volume down
LEFT        GPIO 14      Previous track
RIGHT       GPIO 4       Next track
CENTER      GPIO 2       Play/Pause/Select
SET         GPIO 15      OTA Mode (long press)
RST         GPIO 33      Power on/off (long press)
```

- **Input Type**: Digital with internal pull-up resistors
- **Logic**: Active LOW (button press = LOW)
- **Long Press**: 2 seconds for power functions

### 💾 SD Card Module (SPI)

```
SD Pin      ESP32 Pin    Function
VCC         3.3V         Power
GND         GND          Ground
MISO        GPIO 19      SPI Data In
MOSI        GPIO 21      SPI Data Out
SCK         GPIO 18      SPI Clock
CS          GPIO 32      Chip Select
```

- **Interface**: SPI
- **Supported**: FAT16/FAT32 filesystems
- **Audio Format**: MP3 files

## ⚠️ Hardware Limitations & Constraints

<!-- ### GPIO Pin Limitations
- **Strapping Pins**: GPIO 0, 2, 5, 12, 15 have boot-time constraints
- **Input Only**: GPIO 34, 35, 36, 39 (not used in this design)
- **ADC2 Conflict**: GPIO 0, 2, 4, 12-15, 25-27 cannot be used when WiFi is active
- **Touch Pins**: GPIO 0, 2, 4, 12-15, 27, 32, 33 (some used for buttons) -->

### I2C Bus Configuration

- **OLED Display**: Uses I2C bus (GPIO 21/22) with address 0x3C
- **Pull-up Resistors**: External 4.7kΩ pull-ups recommended for reliable operation

### Power Considerations

- **USB Power**: 5V via USB, regulated to 3.3V on-board
- **Current Draw**: ~200-300mA during operation
- **Deep Sleep**: Supported via RTC GPIO wake-up on button press
- **Battery**: Not included in current design

### Audio Limitations

- **Bluetooth A2DP**: Source mode only (transmits audio to headphones/speakers)
- **MP3 Decoder**: Helix decoder with limited format support
- **Sample Rate**: 44.1kHz standard, limited high-resolution support

### Memory Constraints

- **Flash**: Uses huge_app.csv partition scheme for large audio libraries
- **RAM**: ~320KB available, shared between audio buffers and system
- **PSRAM**: Not utilized in current design
- **SD Card**: Required for MP3 storage (not internal flash)

### Bluetooth Limitations

- **Profile**: A2DP Source only (no sink/receiver mode)
- **Codec**: SBC codec support (no aptX or LDAC)
- **Range**: ~10 meters typical
- **Pairing**: Manual device discovery and connection

## 🔧 Pin Assignment Summary

**Note**: All pin assignments are centrally defined in `src/config/pins.h` to ensure consistency across the codebase and prevent configuration conflicts.

| Function    | GPIO | Notes                     |
| ----------- | ---- | ------------------------- |
| OLED SDA    | 27   | I2C Data                  |
| OLED SCL    | 12   | I2C Clock                 |
| SD CS       | 32   | SPI Chip Select           |
| SD MOSI     | 21   | SPI Data Out              |
| SD MISO     | 19   | SPI Data In               |
| SD SCK      | 18   | SPI Clock                 |
| BTN UP      | 17   | Pull-up enabled           |
| BTN DOWN    | 25   | Pull-up enabled           |
| BTN LEFT    | 14   | Pull-up enabled           |
| BTN RIGHT   | 4    | Pull-up enabled           |
| BTN CENTER  | 2    | Pull-up enabled           |
| BTN SET     | 15   | OTA Mode (long press)     |
| BTN RST     | 33   | Pull-up enabled, RTC wake |
| RGB LED     | 13   | RGB LED output            |
| ONBOARD LED | 22   | Onboard LED (breathing)   |
| BATTERY     | 34   | ADC input only            |

## 🏗️ Code Architecture

### Centralized Pin Configuration

All hardware pin assignments are centrally managed in `src/config/pins.h` to:

- **Prevent Conflicts**: Single source of truth for all pin assignments
- **Easy Maintenance**: Change pins in one place, affects entire codebase
- **Consistency**: All modules use the same pin definitions
- **Documentation**: Clear mapping between hardware and software

### Modular Design

- **Audio Management**: `src/audio/` - MP3 playback, Bluetooth A2DP
- **UI Components**: `src/ui/` - Display management, button handling
- **Core Logic**: `src/core/` - Device state management
- **Utilities**: `src/utils/` - Sleep management, helper functions
- **Configuration**: `src/config/` - Hardware pin definitions

## 🚀 Getting Started

### Prerequisites

- PlatformIO IDE or Arduino IDE with ESP32 support
- ESP32 DevKit board
- Components as listed in pinout section
- Micro SD card with MP3 files

### Installation

1. Clone this repository
2. Open in PlatformIO
3. Connect hardware according to pinout diagram
4. Build and upload firmware
5. Insert SD card with MP3 files
6. Power on and pair with Bluetooth device

### Usage

- **Power On/Off**: Long press RST button (2 seconds)
- **Navigation**: Use directional buttons to browse files
- **Play/Pause**: CENTER button
- **Volume**: UP/DOWN buttons during playback
- **Track Control**: LEFT/RIGHT buttons for previous/next track
- **OTA Mode**: Long press SET button to connect WiFi and enter OTA update mode

## 🌐 OTA (Over-The-Air) Updates

The ESPOD supports wireless firmware updates via WiFi:

### Setup

1. Configure WiFi credentials in `src/config/WiFiConfig.h`
2. Build and upload firmware via USB (first time only)
3. WiFi connects only when OTA is needed (saves power)

### Usage

1. **Enter OTA Mode**: Long press SET button (GPIO 15) - device will connect to WiFi
2. **Get IP Address**: Device displays IP address in serial monitor and on screen
3. **Upload Firmware**: Use PlatformIO command:
   ```bash
   ~/.platformio/penv/bin/pio run --target upload --upload-port <DEVICE_IP>
   ```
4. **Exit OTA Mode**: Press any button to disconnect WiFi and return to normal operation

For detailed OTA instructions, see `OTA_README.md`.

## 📚 Dependencies

### PlatformIO Libraries

```ini
lib_deps =
    https://github.com/pschatzmann/arduino-libhelix
    https://github.com/greiman/SdFat
    https://github.com/pschatzmann/ESP32-A2DP
    https://github.com/pschatzmann/arduino-audio-tools
    adafruit/Adafruit SSD1306@^2.5.7
    adafruit/Adafruit GFX Library@^1.11.5
```

## 🔍 Troubleshooting

### Common Issues

1. **No Display**: Check I2C connections and address (0x3C)
2. **No SD Card**: Verify SPI wiring and card format (FAT32)
3. **No Bluetooth**: Ensure A2DP device is in pairing mode
4. **Button Issues**: Verify pull-up resistors and GPIO assignments

### Debug Tips

- Monitor serial output at 115200 baud
- Check I2C scanner for device detection
- Verify SD card file system and MP3 format
- Test individual components separately

## 📄 License

This project is open source. See individual library licenses for dependencies.
