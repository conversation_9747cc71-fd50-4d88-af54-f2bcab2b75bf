# 📸 Visual Guide & Screenshots

## Web Interface Screenshots

### Main Dashboard

_Access at: `http://[ESP32_IP_ADDRESS]`_

The main dashboard provides a comprehensive overview of the thermal monitoring system:

```
🌡️ Child Fever Monitor
Distance: 4ft | Compensation: ****°F | Status: Connected

┌─ Current Status ─────────────────────────────────────────┐
│ ❤️ Normal - Temperature within expected range            │
└─────────────────────────────────────────────────────────┘

┌─ Temperature Statistics ─────────────────────────────────┐
│ Current Body Temp │ Baseline Temp │ Temp Diff │ Duration │
│      98.6°F       │     98.2°F    │  +0.4°F   │  25 min  │
└─────────────────────────────────────────────────────────┘
```

### Fever Alert State

When fever is detected, the interface transforms:

```
🚨 FEVER DETECTED! 🚨
Temperature: 101.2°F | Duration: 15 minutes

┌─ Alert Status ──────────────────────────────────────────┐
│ 🔥 FEVER DETECTED (15 min) - Immediate attention needed │
└─────────────────────────────────────────────────────────┘
```

### Temperature Trend Chart

Real-time graphing shows temperature patterns over time:

```
Temperature Trend
┌─────────────────────────────────────────────────────────┐
│ 102°F ┌─────────────────────────────────────────────┐   │
│       │     ╭─╮ ← Fever spike                       │   │
│ 100°F │   ╭─╯ ╰─╮ ← Fever threshold line            │   │
│       │ ╭─╯     ╰─╮                                 │   │
│  98°F │╱         ╰─ ← Baseline temperature          │   │
│       └─────────────────────────────────────────────┘   │
│         10:00   10:30   11:00   11:30   12:00           │
└─────────────────────────────────────────────────────────┘
```

### Thermal Visualization

8x8 thermal heatmap with temperature scale:

```
Thermal View                    Temperature Scale
┌─────────────────┐            ┌──┐
│ ● ● ● ● ● ● ● ● │            │██│ 102°F (Red)
│ ● ● ● ● ● ● ● ● │            │██│
│ ● ● ● ◉ ● ● ● ● │ ← Hotspot  │██│ 100°F (Yellow)
│ ● ● ● ● ● ● ● ● │            │██│
│ ● ● ● ● ● ● ● ● │            │██│ 98°F (Green)
│ ● ● ● ● ● ● ● ● │            │██│
│ ● ● ● ● ● ● ● ● │            │██│ 96°F (Blue)
│ ● ● ● ● ● ● ● ● │            └──┘
└─────────────────┘
◉ = Maximum temperature point (101.2°F)
```

## Settings Panel Interface

### Detection Settings Tab

Configure fever detection sensitivity:

```
┌─ Fever Detection Settings ──────────────────────────────┐
│                                                         │
│ Fever Threshold (above baseline)                        │
│ ●────●────●────●────● [2.0°F]                          │
│ 1.0°F              5.0°F                                │
│                                                         │
│ Medical Fever Threshold                                 │
│ ●────●────●────●────● [100.4°F]                        │
│ 99.5°F           101.5°F                                │
│                                                         │
│ Sustained Elevation Time                                │
│ ●────●────●────●────● [10 min]                         │
│ 5 min              30 min                               │
└─────────────────────────────────────────────────────────┘
```

### Calibration Settings Tab

Environmental adjustment controls:

```
┌─ Calibration Settings ──────────────────────────────────┐
│                                                         │
│ Distance Compensation (4-foot distance)                 │
│ ●────●────●────●────● [****°F]                         │
│ +2.0°F             +8.0°F                               │
│                                                         │
│ Ambient Temperature                                     │
│ ●────●────●────●────● [72.0°F]                         │
│ 65°F               80°F                                 │
│                                                         │
│ Baseline Learning Duration                              │
│ ●────●────●────●────● [5 min]                          │
│ 3 min              15 min                               │
└─────────────────────────────────────────────────────────┘
```

### Alert Settings Tab

Notification configuration:

```
┌─ Alert Settings ────────────────────────────────────────┐
│                                                         │
│ ☑ Browser Notifications                                 │
│ ☑ Sound Alerts                                          │
│ ☑ Serial Console Logging                                │
│                                                         │
│ Alert Repeat Interval                                   │
│ ●────●────●────●────● [5 min]                          │
│ 1 min              15 min                               │
│                                                         │
│ Alert Volume                                            │
│ ●────●────●────●────● [80%]                            │
│ 0%                100%                                  │
└─────────────────────────────────────────────────────────┘
```

## System Controls Interface

```
┌─ System Controls ───────────────────────────────────────┐
│                                                         │
│ [🔄 Reset Baseline] [🎯 Calibrate Now]                  │
│                                                         │
│ [🔔 Test Alert]     [💾 Download Data]                  │
│                                                         │
│ [💾 Save Settings]  [↩️ Reset to Defaults]              │
│                                                         │
│ [📤 Export Config]  [📥 Import Config]                  │
└─────────────────────────────────────────────────────────┘
```

## OLED Display States

### Normal Operation Display

```
┌─────────────────┐
│Normal 98.6F     │ ← Status and temperature
│^98.6F           │ ← Symbol (^=normal, !=fever)
│                 │
│ ● ● ● ● ● ● ● ● │ ← 8x8 thermal grid
│ ● ● ● ● ● ● ● ● │   (● = normal, ◉ = hotspot)
│ ● ● ● ◉ ● ● ● ● │
│ ● ● ● ● ● ● ● ● │
└─────────────────┘
```

### Fever Alert Display

```
┌─────────────────┐
│FEVER! 101.2F    │ ← Alert status
│!101.2F          │ ← Fever symbol (!)
│                 │
│ ● ● ● ● ● ● ● ● │ ← Thermal visualization
│ ● ● ● ● ● ● ● ● │   shows heat pattern
│ ● ● ● ◉ ● ● ● ● │
│ ● ● ● ● ● ● ● ● │
└─────────────────┘
```

### Learning Baseline Display

```
┌─────────────────┐
│Learning...      │ ← Learning status
│^97.8F           │ ← Current reading
│                 │
│ ● ● ● ● ● ● ● ● │ ← Live thermal data
│ ● ● ● ● ● ● ● ● │   during learning
│ ● ● ● ◉ ● ● ● ● │
│ ● ● ● ● ● ● ● ● │
└─────────────────┘
```

## Browser Notifications

### Normal Notification

```
┌─ Fever Monitor Notification ────────────────────────────┐
│ 🌡️ System Ready                                         │
│ Fever monitoring system initialized successfully!       │
│                                           [Dismiss] [×] │
└─────────────────────────────────────────────────────────┘
```

### Fever Alert Notification

```
┌─ URGENT: Fever Detected ────────────────────────────────┐
│ 🚨 FEVER DETECTED!                                      │
│ Child's temperature: 101.2°F                           │
│ (****°F above baseline)                                │
│                                           [Dismiss] [×] │
└─────────────────────────────────────────────────────────┘
```

## Serial Console Output Examples

### Startup Sequence

```
Configuring static IP...
Connecting to WiFi...
.....
WiFi connected!
Static IP Address: *************
Gateway: ***********
Subnet: *************
Initializing OLED display...
OLED found at address 0x3C
Initializing fever detection system...
Distance: 4 feet - Compensation: ****°F
Learning baseline temperature...
Ambient temperature: 72.3°F
HTTP server started with comprehensive fever monitoring endpoints
Setup complete
```

### Normal Operation

```
Learning baseline... 30/150 readings (Range: 97.2-98.4°F)
Learning baseline... 60/150 readings (Range: 97.1-98.6°F)
Learning baseline... 90/150 readings (Range: 97.0-98.8°F)
Learning baseline... 120/150 readings (Range: 97.0-98.9°F)
Learning baseline... 150/150 readings (Range: 97.0-98.9°F)
Baseline established: 98.2°F (Range: 97.0-98.9°F)
Body Temp: 98.6°F | Baseline: 98.2°F | Status: Normal
Body Temp: 98.4°F | Baseline: 98.2°F | Status: Normal
```

### Fever Detection

```
Body Temp: 100.8°F | Baseline: 98.2°F | Status: Elevated (****°F)
Temperature elevated above 99°F - monitoring...
FEVER DETECTED - Medical: NO | Baseline: YES | Sustained: NO
🚨 FEVER DETECTED! 🚨
Body Temp: 101.2°F | Baseline: 98.2°F | Status: FEVER DETECTED (2 min)
```

### Error States

```
Could not find a valid AMG88xx sensor. Check wiring!
⚠️ WARNING: Baseline outside normal range! Check sensor positioning.
SSD1306 allocation failed on both addresses
WiFi connection lost - attempting reconnection...
```

## Mobile Interface Adaptation

The web interface automatically adapts to mobile devices:

```
Mobile View (Portrait):
┌─────────────────┐
│ 🌡️ Child Monitor │
│ 4ft | ****°F    │
├─────────────────┤
│ Current: 98.6°F │
│ Baseline: 98.2°F│
│ Diff: +0.4°F    │
│ Duration: 25min │
├─────────────────┤
│ [Thermal View]  │
│ ● ● ● ● ● ● ● ● │
│ ● ● ● ◉ ● ● ● ● │
│ ● ● ● ● ● ● ● ● │
├─────────────────┤
│ [Settings] [⚙️]  │
│ [Controls] [🎮]  │
└─────────────────┘
```

## Data Export Format Preview

When downloading data, you receive a JSON file:

```json
{
  "timestamp": "2024-01-15T10:30:00.000Z",
  "tempHistory": [
    { "time": "10:00:00", "temp": 98.2, "baseline": 98.2 },
    { "time": "10:00:30", "temp": 98.4, "baseline": 98.2 },
    { "time": "10:01:00", "temp": 98.6, "baseline": 98.2 }
  ],
  "settings": {
    "feverThreshold": 2.0,
    "medicalThreshold": 100.4,
    "distanceComp": 4.5
  },
  "systemInfo": {
    "monitoringDuration": 120,
    "totalReadings": 240,
    "feverEvents": 1
  }
}
```

---

**Note**: These are text-based representations of the actual web interface. The real interface includes:

- Interactive controls and sliders
- Real-time color-coded thermal heatmaps
- Animated temperature charts
- Responsive design for all screen sizes
- Professional styling with Bootstrap framework

To see the actual interface, upload the code to your ESP32-C3 and access the web dashboard at the device's IP address.
