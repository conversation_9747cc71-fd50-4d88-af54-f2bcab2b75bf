#!/usr/bin/env python3
"""
PlatformIO post-upload script to automatically upload SPIFFS after firmware
This runs automatically after every 'pio run --target upload'
Ensures firmware uploads successfully before updating web files
"""

Import("env")
import os
import time

# Configuration
SUPPORTED_PROTOCOLS = ["espota"]  # Add other protocols if needed
DATA_DIR_NAME = "data"  # Name of the directory containing SPIFFS files

def post_upload_spiffs(source, target, env):
    """
    Upload SPIFFS filesystem after successful firmware upload

    Args:
        source: Source files (unused but required by PlatformIO)
        target: Target files (unused but required by PlatformIO)
        env: PlatformIO environment object
    """
    print("=" * 60)
    print("🚀 POST-UPLOAD: Automatically uploading SPIFFS filesystem...")
    print("=" * 60)

    # Check if we're using a supported upload protocol
    upload_protocol = env.get("UPLOAD_PROTOCOL", "")
    if upload_protocol not in SUPPORTED_PROTOCOLS:
        print(f"ℹ️  Upload protocol is '{upload_protocol}', not in supported protocols: {SUPPORTED_PROTOCOLS}")
        print("ℹ️  Skipping automatic SPIFFS upload")
        return

    # Check if SPIFFS data directory exists
    project_dir = env.get("PROJECT_DIR")
    data_dir = os.path.join(project_dir, DATA_DIR_NAME)
    if not os.path.exists(data_dir):
        print(f"ℹ️  No '{DATA_DIR_NAME}' directory found, skipping SPIFFS upload")
        return

    # Check if data directory has files
    try:
        data_files = os.listdir(data_dir)
        if not data_files:
            print(f"ℹ️  '{DATA_DIR_NAME}' directory is empty, skipping SPIFFS upload")
            return
        print(f"📁 Found {len(data_files)} items in '{DATA_DIR_NAME}' directory")
    except OSError as e:
        print(f"❌ Error reading '{DATA_DIR_NAME}' directory: {e}")
        return

    print("� Starting SPIFFS upload...")
    print("⏳ Waiting 5 seconds for ESP32 to be ready after firmware upload...")

    # Add delay to ensure ESP32 is ready for SPIFFS upload after firmware update
    time.sleep(5)

    print("🚀 Proceeding with SPIFFS upload...")

    # Upload SPIFFS filesystem using PlatformIO's internal command execution
    # This approach is more compatible with the build system

    try:
        print("📤 Executing SPIFFS upload command...")
        # Use env.Execute with proper shell command to avoid subprocess interference
        cmd = f'cd "{project_dir}" && "{env.subst("$PYTHONEXE")}" -m platformio run --target uploadfs'
        result = env.Execute(cmd)

        if result != 0:
            print("❌ SPIFFS upload failed!")
            print("💡 You can manually upload SPIFFS with: pio run --target uploadfs")
            print("⚠️  Firmware upload was successful, but web files may be outdated")
            return  # Don't exit, just warn - firmware is already uploaded

        print("✅ SPIFFS upload successful!")
        print("🎉 Both firmware and SPIFFS uploaded successfully!")

    except Exception as e:
        print(f"❌ SPIFFS upload failed with exception: {e}")
        print("� You can manually upload SPIFFS with: pio run --target uploadfs")
        print("⚠️  Firmware upload was successful, but web files may be outdated")
        return  # Don't exit, just warn

    print("=" * 60)

# Register the post-upload hook to upload SPIFFS after firmware
env.AddPostAction("upload", post_upload_spiffs)
