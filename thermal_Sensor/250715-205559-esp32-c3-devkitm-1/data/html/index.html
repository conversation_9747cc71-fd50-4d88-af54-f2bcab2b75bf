<!DOCTYPE html>
<html>
  <head>
    <title>Child Fever Monitor - 4ft Distance</title>
    <meta name="viewport" content="width=device-width, initial-scale=1" />

    <!-- External CDN Libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/heatmap.js/2.0.2/heatmap.min.js"></script>
    <script>
      // Fallback for heatmap.js if CDN fails
      if (typeof h337 === "undefined") {
        console.warn("Primary heatmap.js CDN failed, trying backup...");
        document.write(
          '<script src="https://cdn.jsdelivr.net/npm/heatmap.js@2.0.2/build/heatmap.min.js"><\/script>'
        );
      }
    </script>
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      rel="stylesheet"
    />
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <!-- Local Stylesheets -->
    <link rel="stylesheet" href="/css/styles.css" />
  </head>
  <body>
    <div class="main-container">
      <!-- Header Section -->
      <div class="header-section text-center">
        <h1 class="mb-3">
          <i class="fas fa-thermometer-half text-primary"></i> Child Fever
          Monitor
        </h1>
        <p class="mb-0 text-light">
          <i class="fas fa-ruler"></i> Distance: 4 feet |
          <i class="fas fa-adjust"></i> Compensation: +<span
            id="compensationDisplay"
            >4.5</span
          >°F | <i class="fas fa-wifi"></i>
          <span id="connectionStatus">Connected</span>
        </p>
        <p class="mb-0 text-light">
          <i class="fas fa-clock"></i> Device Time:
          <span id="deviceTime">--:--:--</span> |
          <i class="fas fa-calendar"></i>
          <span id="deviceDate">----------</span> |
          <span id="timeStatus" class="badge badge-secondary">Not Set</span>
        </p>
      </div>

      <!-- Fever Alert -->
      <div
        id="feverAlert"
        class="fever-alert text-center"
        style="display: none"
      >
        <i class="fas fa-exclamation-triangle fa-2x mb-2"></i><br />
        🚨 FEVER DETECTED! 🚨<br />
        <span id="feverDetails"></span>
      </div>

      <!-- Status Display -->
      <div id="statusDisplay" class="status-card normal-status text-center">
        <h4>
          <i class="fas fa-heartbeat"></i>
          <span id="currentStatus">Loading...</span>
        </h4>
      </div>

      <!-- Stats Grid -->
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon text-danger">
            <i class="fas fa-thermometer-full"></i>
          </div>
          <div class="stat-label">Current Body Temperature</div>
          <div class="stat-value text-danger" id="currentTemp">--</div>
          <div class="stat-unit">°F</div>
        </div>
        <div class="stat-card">
          <div class="stat-icon text-success">
            <i class="fas fa-chart-line"></i>
          </div>
          <div class="stat-label">Baseline Temperature</div>
          <div class="stat-value text-success" id="baselineTemp">--</div>
          <div class="stat-unit">°F</div>
        </div>
        <div class="stat-card">
          <div class="stat-icon text-warning">
            <i class="fas fa-arrows-alt-v"></i>
          </div>
          <div class="stat-label">Temperature Difference</div>
          <div class="stat-value text-warning" id="tempDiff">--</div>
          <div class="stat-unit">°F</div>
        </div>
        <div class="stat-card">
          <div class="stat-icon text-info"><i class="fas fa-clock"></i></div>
          <div class="stat-label">Monitoring Duration</div>
          <div class="stat-value text-info" id="duration">--</div>
          <div class="stat-unit">minutes</div>
        </div>
        <div class="stat-card">
          <div class="stat-icon text-primary"><i class="fas fa-magic"></i></div>
          <div class="stat-label">Active Compensation</div>
          <div class="stat-value text-primary" id="activeCompensation">--</div>
          <div class="stat-unit">°F</div>
        </div>
      </div>

      <!-- Temperature Chart -->
      <div class="temp-chart-container">
        <h4 class="mb-4">
          <i class="fas fa-chart-area text-primary"></i> Temperature Trend
        </h4>
        <canvas id="tempChart"></canvas>
      </div>

      <!-- Thermal Visualization -->
      <div class="thermal-section">
        <div>
          <h4 class="mb-4 text-center">
            <i class="fas fa-eye text-primary"></i> Thermal View
          </h4>
          <div id="heatmapContainer">
            <div id="marker"></div>
          </div>
        </div>
        <div>
          <h5 class="mb-3">Temperature Scale</h5>
          <canvas id="colorbar" width="50" height="300"></canvas>
        </div>
      </div>

      <!-- Settings Panel -->
      <div class="settings-panel">
        <h4 class="mb-4">
          <i class="fas fa-cogs text-primary"></i> Fever Detection Settings
        </h4>

        <!-- Navigation Tabs -->
        <ul class="nav nav-tabs mb-4" id="settingsTabs" role="tablist">
          <li class="nav-item" role="presentation">
            <button
              class="nav-link active"
              id="auto-calibrate-tab"
              data-bs-toggle="tab"
              data-bs-target="#auto-calibrate"
              type="button"
            >
              <i class="fas fa-magic"></i> Quick Setup
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              id="fever-detection-tab"
              data-bs-toggle="tab"
              data-bs-target="#fever-detection"
              type="button"
            >
              <i class="fas fa-thermometer-half"></i> Fever Detection
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              id="display-audio-tab"
              data-bs-toggle="tab"
              data-bs-target="#display-audio"
              type="button"
            >
              <i class="fas fa-desktop"></i> Display & Audio
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              id="system-advanced-tab"
              data-bs-toggle="tab"
              data-bs-target="#system-advanced"
              type="button"
            >
              <i class="fas fa-cogs"></i> System & Advanced
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button
              class="nav-link"
              id="time-tab"
              data-bs-toggle="tab"
              data-bs-target="#time-settings"
              type="button"
            >
              <i class="fas fa-clock"></i> Time & Schedule
            </button>
          </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="settingsTabContent">
          <!-- Auto Calibrate Settings -->
          <div
            class="tab-pane fade show active"
            id="auto-calibrate"
            role="tabpanel"
          >
            <div class="row">
              <div class="col-md-12">
                <div class="alert alert-info mb-4">
                  <i class="fas fa-info-circle"></i>
                  <strong>Auto Calibration:</strong> Enter your child's current
                  body temperature (measured with a thermometer) and the ambient
                  room temperature. The system will automatically set the
                  distance compensation and baseline minimum temperature.
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-md-6">
                <div class="mb-4">
                  <label class="form-label fw-bold">
                    <i class="fas fa-child text-primary"></i> Child's Current
                    Body Temperature
                  </label>
                  <div class="input-group">
                    <input
                      type="number"
                      class="form-control"
                      id="childBodyTemp"
                      min="95.0"
                      max="105.0"
                      step="0.1"
                      value="98.6"
                      placeholder="98.6"
                    />
                    <span class="input-group-text">°F</span>
                  </div>
                  <small class="text-muted">
                    Measure with a thermometer (oral, ear, or forehead)
                  </small>
                </div>

                <div class="mb-4">
                  <label class="form-label fw-bold">
                    <i class="fas fa-thermometer-quarter text-success"></i>
                    Current Ambient Room Temperature
                  </label>
                  <div class="card bg-light">
                    <div class="card-body py-2">
                      <div
                        class="d-flex justify-content-between align-items-center"
                      >
                        <span
                          class="fw-bold text-success fs-5"
                          id="autoCalibAmbientTemp"
                          >--°F</span
                        >
                        <span
                          class="badge bg-success"
                          id="autoCalibAmbientSource"
                          >AHT10 Sensor</span
                        >
                      </div>
                    </div>
                  </div>
                  <small class="text-muted">
                    <i class="fas fa-info-circle"></i> Automatically detected
                    from AHT10 sensor
                  </small>
                </div>
              </div>

              <div class="col-md-6">
                <div class="mb-4">
                  <h6 class="fw-bold text-primary">
                    <i class="fas fa-calculator"></i> Calculated Settings
                  </h6>
                  <div class="card bg-light">
                    <div class="card-body">
                      <div class="mb-2">
                        <strong>Distance Compensation:</strong>
                        <span
                          id="calculatedDistanceCompensation"
                          class="text-success"
                          >+4.5°F</span
                        >
                      </div>
                      <div class="mb-2">
                        <small class="text-muted">
                          <i class="fas fa-info-circle"></i>
                          Auto calibration calculates the compensation needed to
                          convert thermal sensor readings to actual body
                          temperature.
                        </small>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="mb-4">
                  <h6 class="fw-bold text-warning">
                    <i class="fas fa-exclamation-triangle"></i> Important Notes
                  </h6>
                  <ul class="small text-muted">
                    <li>
                      Ensure your child is present and positioned normally
                    </li>
                    <li>
                      Take body temperature measurement just before calibration
                    </li>
                    <li>Room temperature should be stable</li>
                    <li>
                      Baseline minimum will be set to 1°F below body temperature
                    </li>
                    <li>Distance compensation will be auto-calculated</li>
                  </ul>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-md-12">
                <div class="text-center">
                  <button
                    class="btn btn-primary btn-lg me-3"
                    id="performAutoCalibration"
                    onclick="performAutoCalibration()"
                  >
                    <i class="fas fa-magic"></i> Calculate Auto Calibration
                  </button>
                  <button
                    class="btn btn-secondary btn-lg"
                    id="previewCalculations"
                    onclick="previewCalculations()"
                  >
                    <i class="fas fa-eye"></i> Preview Calculations
                  </button>
                </div>

                <div
                  id="calibrationStatus"
                  class="mt-3 text-center"
                  style="display: none"
                >
                  <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <span id="calibrationMessage"
                      >Auto calibration completed successfully!</span
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Fever Detection Settings -->
          <div class="tab-pane fade" id="fever-detection" role="tabpanel">
            <div class="row">
              <div class="col-md-6">
                <h5 class="mb-3 text-primary">
                  <i class="fas fa-thermometer-half"></i> Temperature Thresholds
                </h5>

                <div class="mb-4">
                  <label class="form-label fw-bold">
                    <i class="fas fa-thermometer-half text-danger"></i> Fever
                    Threshold (above baseline)
                  </label>
                  <div class="d-flex align-items-center">
                    <input
                      type="range"
                      class="form-range me-3"
                      id="feverThreshold"
                      min="1"
                      max="5"
                      step="0.1"
                      value="2.0"
                    />
                    <span class="badge bg-danger fs-6" id="feverThresholdValue"
                      >2.0°F</span
                    >
                  </div>
                  <small class="text-muted"
                    >How many degrees above baseline triggers fever alert</small
                  >
                </div>

                <div class="mb-4">
                  <label class="form-label fw-bold">
                    <i class="fas fa-hospital text-info"></i> Medical Fever
                    Threshold
                  </label>
                  <div class="d-flex align-items-center">
                    <input
                      type="range"
                      class="form-range me-3"
                      id="medicalThreshold"
                      min="99.5"
                      max="101.5"
                      step="0.1"
                      value="100.4"
                    />
                    <span class="badge bg-info fs-6" id="medicalThresholdValue"
                      >100.4°F</span
                    >
                  </div>
                  <small class="text-muted"
                    >Absolute temperature that always triggers fever
                    alert</small
                  >
                </div>

                <div class="mb-4">
                  <label class="form-label fw-bold">
                    <i class="fas fa-clock text-warning"></i> Sustained
                    Elevation Time
                  </label>
                  <div class="d-flex align-items-center">
                    <input
                      type="range"
                      class="form-range me-3"
                      id="sustainedTime"
                      min="5"
                      max="30"
                      step="1"
                      value="10"
                    />
                    <span class="badge bg-warning fs-6" id="sustainedTimeValue"
                      >10 min</span
                    >
                  </div>
                  <small class="text-muted"
                    >Time above 99°F before triggering sustained fever
                    alert</small
                  >
                </div>

                <div class="mb-4">
                  <label class="form-label fw-bold">
                    <i class="fas fa-user-check text-info"></i> Baseline Min
                    Threshold
                  </label>
                  <div class="d-flex align-items-center">
                    <input
                      type="range"
                      class="form-range me-3"
                      id="baselineMin"
                      min="85"
                      max="99"
                      step="0.5"
                      value="90.0"
                    />
                    <span class="badge bg-info fs-6" id="baselineMinValue"
                      >90.0°F</span
                    >
                  </div>
                  <small class="text-muted"
                    >Minimum temperature to detect presence and start baseline
                    learning</small
                  >
                </div>
              </div>

              <div class="col-md-6">
                <h5 class="mb-3 text-success">
                  <i class="fas fa-magic"></i> Smart Compensation
                </h5>

                <!-- Dynamic Compensation Settings -->
                <div class="mb-4">
                  <div class="card border-info">
                    <div class="card-header bg-info text-white">
                      <h6 class="mb-0">
                        <i class="fas fa-magic"></i> Dynamic Compensation
                      </h6>
                    </div>
                    <div class="card-body">
                      <div class="mb-3">
                        <div class="form-check form-switch">
                          <input
                            class="form-check-input"
                            type="checkbox"
                            id="dynamicCompensation"
                            checked
                          />
                          <label
                            class="form-check-label fw-bold"
                            for="dynamicCompensation"
                          >
                            Enable Dynamic Compensation
                          </label>
                        </div>
                        <small class="text-muted">
                          Automatically adjust compensation based on real-time
                          ambient temperature from AHT10
                        </small>
                      </div>

                      <div class="mb-3" id="sensitivityContainer">
                        <label class="form-label fw-bold">
                          <i class="fas fa-sliders-h text-warning"></i>
                          Adjustment Sensitivity
                        </label>
                        <div class="d-flex align-items-center">
                          <input
                            type="range"
                            class="form-range me-3"
                            id="compensationSensitivity"
                            min="0"
                            max="100"
                            step="5"
                            value="30"
                          />
                          <span
                            class="badge bg-warning fs-6"
                            id="sensitivityValue"
                            >30%</span
                          >
                        </div>
                        <small class="text-muted">
                          How aggressively to adjust compensation (0% = no
                          adjustment, 100% = full physics-based adjustment)
                        </small>
                      </div>

                      <div class="alert alert-light py-2 mb-0">
                        <i class="fas fa-info-circle text-info"></i>
                        <strong>Current Compensation:</strong>
                        <span id="currentCompensationDisplay">+4.5°F</span>
                        <span class="text-muted"
                          >(Base + Dynamic Adjustment)</span
                        >
                      </div>
                    </div>
                  </div>
                </div>

                <div class="mb-4">
                  <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Audio Alerts:</strong> All fever alert sounds are
                    handled by the built-in ESP32 buzzer. Configure buzzer
                    volume settings in the Display & Audio tab.
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Display & Audio Settings -->
          <div class="tab-pane fade" id="display-audio" role="tabpanel">
            <div class="row">
              <div class="col-md-6">
                <h5 class="mb-3 text-secondary">
                  <i class="fas fa-desktop"></i> Display Settings
                </h5>

                <div class="mb-4">
                  <label class="form-label fw-bold">
                    <i class="fas fa-sun text-warning"></i> OLED Display
                    Brightness
                  </label>
                  <div class="d-flex align-items-center">
                    <input
                      type="range"
                      class="form-range me-3"
                      id="displayBrightness"
                      min="10"
                      max="255"
                      step="5"
                      value="255"
                    />
                    <span
                      class="badge bg-warning fs-6"
                      id="displayBrightnessValue"
                      >255</span
                    >
                  </div>
                  <small class="text-muted"
                    >Adjust OLED display brightness (10=dim, 255=bright)</small
                  >
                </div>

                <div class="mb-4">
                  <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Night Mode:</strong> Display automatically dims
                    during night hours when night mode is enabled in Time &
                    Schedule settings.
                  </div>
                </div>
              </div>

              <div class="col-md-6">
                <h5 class="mb-3 text-info">
                  <i class="fas fa-volume-up"></i> Audio Settings
                </h5>

                <div class="mb-4">
                  <label class="form-label fw-bold">
                    <i class="fas fa-sun text-warning"></i> Day Volume
                  </label>
                  <div class="d-flex align-items-center">
                    <input
                      type="range"
                      class="form-range me-3"
                      id="dayVolume"
                      min="0"
                      max="100"
                      step="5"
                      value="100"
                    />
                    <span class="badge bg-warning fs-6" id="dayVolumeValue"
                      >100%</span
                    >
                  </div>
                  <small class="text-muted"
                    >Volume level during day time (non-fever sounds)</small
                  >
                </div>

                <div class="mb-4">
                  <label class="form-label fw-bold">
                    <i class="fas fa-moon text-primary"></i> Night Volume
                  </label>
                  <div class="d-flex align-items-center">
                    <input
                      type="range"
                      class="form-range me-3"
                      id="nightVolume"
                      min="0"
                      max="100"
                      step="5"
                      value="25"
                    />
                    <span class="badge bg-primary fs-6" id="nightVolumeValue"
                      >25%</span
                    >
                  </div>
                  <small class="text-muted"
                    >Volume level during night time (non-fever sounds)</small
                  >
                </div>

                <div class="mb-4">
                  <label class="form-label fw-bold">
                    <i class="fas fa-exclamation-triangle text-danger"></i>
                    Fever Alert Volume
                  </label>
                  <div class="d-flex align-items-center">
                    <input
                      type="range"
                      class="form-range me-3"
                      id="feverVolume"
                      min="0"
                      max="100"
                      step="5"
                      value="75"
                    />
                    <span class="badge bg-danger fs-6" id="feverVolumeValue"
                      >75%</span
                    >
                  </div>
                  <small class="text-muted"
                    >Volume level for fever alerts (day and night)</small
                  >
                </div>

                <div class="mb-4">
                  <div class="alert alert-warning">
                    <i class="fas fa-info-circle"></i>
                    <strong>Audio System:</strong> All sounds are generated by
                    the built-in ESP32 buzzer. Volume levels automatically
                    adjust based on time of day.
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- System & Advanced Settings -->
          <div class="tab-pane fade" id="system-advanced" role="tabpanel">
            <div class="row">
              <div class="col-md-6">
                <h5 class="mb-3 text-warning">
                  <i class="fas fa-cogs"></i> System Performance
                </h5>

                <div class="mb-4">
                  <label class="form-label fw-bold">
                    <i class="fas fa-tachometer-alt text-danger"></i> Sensor
                    Read Interval
                  </label>
                  <div class="d-flex align-items-center">
                    <input
                      type="range"
                      class="form-range me-3"
                      id="readInterval"
                      min="100"
                      max="1000"
                      step="50"
                      value="100"
                    />
                    <span class="badge bg-danger fs-6" id="readIntervalValue"
                      >100ms</span
                    >
                  </div>
                  <small class="text-muted"
                    >Minimum time between sensor readings (hardware
                    protection)</small
                  >
                </div>

                <div class="alert alert-info mb-4">
                  <i class="fas fa-info-circle"></i>
                  <strong>System Performance:</strong> Baseline learning
                  parameters are automatically configured for best performance.
                </div>
              </div>

              <div class="col-md-6">
                <h5 class="mb-3 text-secondary">
                  <i class="fas fa-wrench"></i> System Settings
                </h5>

                <div class="mb-4">
                  <div class="form-check form-switch">
                    <input
                      class="form-check-input"
                      type="checkbox"
                      id="debugMode"
                    />
                    <label class="form-check-label fw-bold" for="debugMode">
                      <i class="fas fa-bug text-warning"></i> Debug Mode
                    </label>
                  </div>
                  <small class="text-muted"
                    >Enable detailed debugging information</small
                  >
                </div>

                <div class="mb-4">
                  <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Data Logging:</strong> Temperature data is
                    automatically logged every 30 seconds to the serial console
                    and stored in a 100-sample rolling history for optimal
                    performance.
                  </div>
                </div>

                <div class="mb-4">
                  <div class="alert alert-warning">
                    <i class="fas fa-info-circle"></i>
                    <strong>Advanced Settings:</strong> These settings are for
                    experienced users. Incorrect values may affect system
                    performance or accuracy.
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Time & Schedule Settings -->
          <div
            class="tab-pane fade"
            id="time-settings"
            role="tabpanel"
            aria-labelledby="time-tab"
          >
            <div class="row">
              <div class="col-md-6">
                <h5 class="mb-3 text-primary">
                  <i class="fas fa-clock"></i> Time Synchronization
                </h5>

                <div class="mb-4">
                  <label class="form-label fw-bold">
                    <i class="fas fa-server"></i> NTP Server
                  </label>
                  <input
                    type="text"
                    class="form-control"
                    id="ntpServer"
                    placeholder="pool.ntp.org"
                  />
                  <small class="text-muted"
                    >Network Time Protocol server for automatic time sync</small
                  >
                </div>

                <div class="mb-4">
                  <label class="form-label fw-bold">
                    <i class="fas fa-globe"></i> Timezone
                  </label>
                  <select class="form-select" id="timezone">
                    <option value="EST5EDT">Eastern Time (EST/EDT)</option>
                    <option value="CST6CDT">Central Time (CST/CDT)</option>
                    <option value="MST7MDT">Mountain Time (MST/MDT)</option>
                    <option value="PST8PDT">Pacific Time (PST/PDT)</option>
                    <option value="AKST9AKDT">Alaska Time (AKST/AKDT)</option>
                    <option value="HST10">Hawaii Time (HST)</option>
                    <option value="IST-5:30">India Standard Time (IST)</option>
                  </select>
                  <small class="text-muted">Select your local timezone</small>
                </div>

                <div class="mb-4">
                  <div class="form-check form-switch">
                    <input
                      class="form-check-input"
                      type="checkbox"
                      id="autoTimeSync"
                    />
                    <label class="form-check-label fw-bold" for="autoTimeSync">
                      <i class="fas fa-sync text-primary"></i> Auto Time Sync
                    </label>
                  </div>
                  <small class="text-muted"
                    >Automatically synchronize time with NTP server</small
                  >
                </div>

                <div class="mb-4">
                  <button class="btn btn-primary" onclick="syncTimeNow()">
                    <i class="fas fa-sync-alt"></i> Sync Time Now
                  </button>
                  <button class="btn btn-secondary" onclick="setTimeManually()">
                    <i class="fas fa-edit"></i> Set Time Manually
                  </button>
                </div>
              </div>

              <div class="col-md-6">
                <h5 class="mb-3 text-warning">
                  <i class="fas fa-moon"></i> Night Mode Schedule
                </h5>

                <div class="mb-4">
                  <div class="form-check form-switch">
                    <input
                      class="form-check-input"
                      type="checkbox"
                      id="nightModeEnabled"
                    />
                    <label
                      class="form-check-label fw-bold"
                      for="nightModeEnabled"
                    >
                      <i class="fas fa-moon text-warning"></i> Enable Night Mode
                    </label>
                  </div>
                  <small class="text-muted"
                    >Automatically adjust display during night hours</small
                  >
                </div>

                <div class="mb-4">
                  <label class="form-label fw-bold">
                    <i class="fas fa-clock"></i> Night Start Time
                  </label>
                  <div class="row">
                    <div class="col-6">
                      <select class="form-select" id="nightStartHour">
                        <option value="20">8 PM</option>
                        <option value="21">9 PM</option>
                        <option value="22">10 PM</option>
                        <option value="23" selected>11 PM</option>
                        <option value="0">12 AM</option>
                      </select>
                    </div>
                    <div class="col-6">
                      <select class="form-select" id="nightStartMinute">
                        <option value="0" selected>00</option>
                        <option value="15">15</option>
                        <option value="30">30</option>
                        <option value="45">45</option>
                      </select>
                    </div>
                  </div>
                </div>

                <div class="mb-4">
                  <label class="form-label fw-bold">
                    <i class="fas fa-sun"></i> Night End Time
                  </label>
                  <div class="row">
                    <div class="col-6">
                      <select class="form-select" id="nightEndHour">
                        <option value="6">6 AM</option>
                        <option value="7">7 AM</option>
                        <option value="8" selected>8 AM</option>
                        <option value="9">9 AM</option>
                        <option value="10">10 AM</option>
                      </select>
                    </div>
                    <div class="col-6">
                      <select class="form-select" id="nightEndMinute">
                        <option value="0" selected>00</option>
                        <option value="15">15</option>
                        <option value="30">30</option>
                        <option value="45">45</option>
                      </select>
                    </div>
                  </div>
                </div>

                <div class="mb-4">
                  <div class="form-check form-switch">
                    <input
                      class="form-check-input"
                      type="checkbox"
                      id="displayOffAtNight"
                    />
                    <label
                      class="form-check-label fw-bold"
                      for="displayOffAtNight"
                    >
                      <i class="fas fa-eye-slash text-secondary"></i> Turn Off
                      Display at Night
                    </label>
                  </div>
                  <small class="text-muted"
                    >Turn off OLED display during night hours only when no
                    presence is detected</small
                  >
                </div>

                <div class="alert alert-info">
                  <i class="fas fa-info-circle"></i>
                  <strong>Night Mode:</strong> When enabled, the display will
                  turn off during night hours only when no presence is detected.
                  The display will automatically turn on when presence is
                  detected, even during night mode.
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="text-center mt-4">
          <button class="btn btn-success btn-custom" onclick="saveSettings()">
            <i class="fas fa-save"></i> Save Settings
          </button>
          <button class="btn btn-warning btn-custom" onclick="resetSettings()">
            <i class="fas fa-undo"></i> Reset to Defaults
          </button>
          <button class="btn btn-info btn-custom" onclick="exportSettings()">
            <i class="fas fa-download"></i> Export Config
          </button>
          <button
            class="btn btn-secondary btn-custom"
            onclick="importSettings()"
          >
            <i class="fas fa-upload"></i> Import Config
          </button>
        </div>
      </div>

      <!-- Control Buttons -->
      <div class="controls-section text-center">
        <h4 class="mb-4">
          <i class="fas fa-gamepad text-primary"></i> System Controls
        </h4>
        <button class="btn btn-primary btn-custom" onclick="resetBaseline()">
          <i class="fas fa-refresh"></i> Reset Baseline
        </button>
        <button class="btn btn-warning btn-custom" onclick="calibrateNow()">
          <i class="fas fa-crosshairs"></i> Calibrate Now
        </button>
        <button class="btn btn-danger btn-custom" onclick="testAlert()">
          <i class="fas fa-bell"></i> Test Alert
        </button>
        <button
          class="btn btn-success btn-custom"
          onclick="testBackgroundNotification()"
        >
          <i class="fas fa-check-circle"></i> Test Visual Alerts
        </button>
        <button class="btn btn-info btn-custom" onclick="downloadData()">
          <i class="fas fa-download"></i> Download Data
        </button>
      </div>
    </div>

    <!-- Modular JavaScript - Load in dependency order -->
    <!-- Core utilities and configuration -->
    <script src="/js/modules/utils.js"></script>
    <script src="/js/modules/config.js"></script>
    <script src="/js/modules/ui-constraints.js"></script>

    <!-- UI and visualization modules -->
    <script src="/js/modules/notifications.js"></script>
    <!-- Service worker module removed - using simple beep only -->
    <script src="/js/modules/heatmap.js"></script>
    <script src="/js/modules/chart.js"></script>
    <script src="/js/modules/calibration.js"></script>
    <script src="/js/modules/time.js"></script>

    <!-- Data and API module -->
    <script src="/js/modules/api.js"></script>

    <!-- Main application entry point -->
    <script src="/js/fever-monitor-app.js"></script>
  </body>
</html>
