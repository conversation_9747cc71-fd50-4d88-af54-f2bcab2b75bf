/* Child Fever Monitor - Main Stylesheet */

:root {
  --primary-color: #6366f1;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --dark-bg: #0f172a;
  --card-bg: #1e293b;
  --border-color: #334155;
}

body {
  background: linear-gradient(135deg, var(--dark-bg) 0%, #1e293b 100%);
  color: #f8fafc;
  font-family: "Inter", sans-serif;
  min-height: 100vh;
}

.main-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
}

.header-section {
  background: var(--card-bg);
  border-radius: 16px;
  padding: 30px;
  margin-bottom: 30px;
  border: 1px solid var(--border-color);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.fever-alert {
  background: linear-gradient(135deg, var(--danger-color), #dc2626);
  padding: 20px;
  margin: 20px 0;
  border-radius: 12px;
  font-size: 18px;
  font-weight: 600;
  animation: pulse 2s infinite;
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
}

.status-card {
  background: var(--card-bg);
  padding: 20px;
  margin: 15px 0;
  border-radius: 12px;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.status-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.normal-status {
  border-left: 4px solid var(--success-color);
}

.elevated-status {
  border-left: 4px solid var(--warning-color);
}

.fever-status {
  border-left: 4px solid var(--danger-color);
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin: 30px 0;
}

.stat-card {
  background: var(--card-bg);
  padding: 25px;
  border-radius: 16px;
  border: 1px solid var(--border-color);
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.3);
}

.stat-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(
    90deg,
    var(--primary-color),
    var(--success-color)
  );
}

.stat-icon {
  font-size: 2.5rem;
  margin-bottom: 15px;
  opacity: 0.8;
}

.stat-label {
  font-size: 0.9rem;
  color: #94a3b8;
  margin-bottom: 8px;
  font-weight: 500;
}

.stat-value {
  font-size: 2.2rem;
  font-weight: 700;
  margin-bottom: 5px;
}

.stat-unit {
  font-size: 1rem;
  color: #64748b;
  font-weight: 400;
}

.thermal-section {
  background: var(--card-bg);
  padding: 30px;
  border-radius: 16px;
  margin: 30px 0;
  border: 1px solid var(--border-color);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 30px;
}

#heatmapContainer {
  width: 256px;
  height: 256px;
  position: relative;
  display: inline-block;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  background: #1e293b; /* Fallback background to see container */
  border: 1px solid #555; /* Simpler border like working version */
}

/* Ensure heatmap canvas is visible - simplified to match working version */
#heatmapContainer canvas {
  display: block !important;
}

#marker {
  position: absolute;
  width: 50px;
  height: 50px;
  border: 3px solid #fff;
  border-radius: 50%;
  pointer-events: none;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 14px;
  font-weight: 600;
  background: rgba(0, 0, 0, 0.5);
  z-index: 10;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
}

canvas {
  border: 1px solid var(--border-color);
  border-radius: 8px;
}

.controls-section {
  background: var(--card-bg);
  padding: 30px;
  border-radius: 16px;
  margin: 30px 0;
  border: 1px solid var(--border-color);
}

.btn-custom {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
  margin: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.btn-custom:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.settings-panel {
  background: var(--card-bg);
  border-radius: 16px;
  padding: 30px;
  margin: 30px 0;
  border: 1px solid var(--border-color);
}

.temp-chart-container {
  background: var(--card-bg);
  border-radius: 16px;
  padding: 30px;
  margin: 30px 0;
  border: 1px solid var(--border-color);
  height: 400px;
}

.form-range::-webkit-slider-thumb {
  background: var(--primary-color);
}

.form-range::-moz-range-thumb {
  background: var(--primary-color);
  border: none;
}

.nav-tabs .nav-link {
  background: transparent;
  border: 1px solid var(--border-color);
  color: #94a3b8;
  border-radius: 8px 8px 0 0;
}

.nav-tabs .nav-link.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.tab-content {
  background: var(--card-bg);
  border-radius: 0 0 12px 12px;
  padding: 25px;
}
