/**
 * Utility Functions Module
 * Common helper functions used throughout the application
 */

class Utils {
  /**
   * Convert Celsius to Fahrenheit
   * @param {number} celsius - Temperature in Celsius
   * @returns {number} Temperature in Fahrenheit
   */
  static toFahrenheit(celsius) {
    return (celsius * 9) / 5 + 32;
  }

  /**
   * Convert Fahrenheit to Celsius
   * @param {number} fahrenheit - Temperature in Fahrenheit
   * @returns {number} Temperature in Celsius
   */
  static toCelsius(fahrenheit) {
    return ((fahrenheit - 32) * 5) / 9;
  }

  /**
   * Animate a value change in a DOM element
   * @param {string} elementId - ID of the element to animate
   * @param {string} newValue - New value to display
   * @param {Object} options - Animation options
   */
  static animateValue(elementId, newValue, options = {}) {
    const element = document.getElementById(elementId);
    if (!element || element.textContent === newValue) return;

    const {
      scale = 1.1,
      color = "#fbbf24",
      duration = 150,
      resetColor = "",
    } = options;

    element.style.transform = `scale(${scale})`;
    element.style.color = color;
    element.style.transition = `all ${duration}ms ease`;

    setTimeout(() => {
      element.textContent = newValue;
      element.style.transform = "scale(1)";
      element.style.color = resetColor;
    }, duration);
  }

  /**
   * Format temperature with proper units and precision
   * @param {number} temp - Temperature value
   * @param {string} unit - Temperature unit ('F' or 'C')
   * @param {number} precision - Decimal places
   * @returns {string} Formatted temperature string
   */
  static formatTemperature(temp, unit = "F", precision = 1) {
    if (isNaN(temp)) return "N/A";
    return `${temp.toFixed(precision)}°${unit}`;
  }

  /**
   * Format duration in minutes to human readable format
   * @param {number} minutes - Duration in minutes
   * @returns {string} Formatted duration string
   */
  static formatDuration(minutes) {
    if (minutes < 60) {
      return `${minutes} min`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return `${hours}h ${remainingMinutes}m`;
  }

  /**
   * Debounce function calls
   * @param {Function} func - Function to debounce
   * @param {number} wait - Wait time in milliseconds
   * @param {boolean} immediate - Execute immediately on first call
   * @returns {Function} Debounced function
   */
  static debounce(func, wait, immediate = false) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        timeout = null;
        if (!immediate) func(...args);
      };
      const callNow = immediate && !timeout;
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
      if (callNow) func(...args);
    };
  }

  /**
   * Throttle function calls
   * @param {Function} func - Function to throttle
   * @param {number} limit - Time limit in milliseconds
   * @returns {Function} Throttled function
   */
  static throttle(func, limit) {
    let inThrottle;
    return function (...args) {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => (inThrottle = false), limit);
      }
    };
  }

  /**
   * Validate temperature data using centralized constraints
   * @param {*} value - Value to validate
   * @returns {boolean} True if valid temperature
   */
  static isValidTemperature(value) {
    if (typeof value !== "number" || isNaN(value) || !isFinite(value)) {
      return false;
    }

    // Use centralized validation constraints if available
    if (window.FeverMonitor && window.FeverMonitor.ConfigManager) {
      const validation = window.FeverMonitor.ConfigManager.validateValue(
        "generalTemp",
        value
      );
      return validation.isValid;
    }

    // Fallback to hardcoded range if ConfigManager not available
    return value > -100 && value < 200;
  }

  /**
   * Validate array of temperature data
   * @param {Array} data - Array of temperature values
   * @param {number} expectedLength - Expected array length
   * @returns {boolean} True if valid temperature array
   */
  static isValidTemperatureArray(data, expectedLength = null) {
    if (!Array.isArray(data)) return false;
    if (expectedLength !== null && data.length !== expectedLength) return false;
    return data.every((temp) => this.isValidTemperature(temp));
  }

  /**
   * Calculate moving average
   * @param {Array} data - Array of numbers
   * @param {number} windowSize - Size of moving window
   * @returns {Array} Array of moving averages
   */
  static movingAverage(data, windowSize) {
    if (!Array.isArray(data) || windowSize <= 0) return [];

    const result = [];
    for (let i = 0; i < data.length; i++) {
      const start = Math.max(0, i - windowSize + 1);
      const window = data.slice(start, i + 1);
      const average = window.reduce((sum, val) => sum + val, 0) / window.length;
      result.push(average);
    }
    return result;
  }

  /**
   * Generate timestamp string
   * @param {Date} date - Date object (defaults to now)
   * @param {boolean} includeSeconds - Include seconds in timestamp
   * @returns {string} Formatted timestamp
   */
  static getTimestamp(date = new Date(), includeSeconds = true) {
    const options = {
      hour: "2-digit",
      minute: "2-digit",
      hour12: false,
    };

    if (includeSeconds) {
      options.second = "2-digit";
    }

    return date.toLocaleTimeString([], options);
  }

  /**
   * Generate filename-safe timestamp
   * @param {Date} date - Date object (defaults to now)
   * @returns {string} Filename-safe timestamp
   */
  static getFilenameTimestamp(date = new Date()) {
    return date.toISOString().replace(/[:.]/g, "-").split("T")[0];
  }

  /**
   * Deep clone an object
   * @param {Object} obj - Object to clone
   * @returns {Object} Cloned object
   */
  static deepClone(obj) {
    if (obj === null || typeof obj !== "object") return obj;
    if (obj instanceof Date) return new Date(obj.getTime());
    if (obj instanceof Array) return obj.map((item) => this.deepClone(item));
    if (typeof obj === "object") {
      const clonedObj = {};
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          clonedObj[key] = this.deepClone(obj[key]);
        }
      }
      return clonedObj;
    }
  }

  /**
   * Check if browser supports required features
   * @returns {Object} Feature support status
   */
  static checkBrowserSupport() {
    return {
      notifications: "Notification" in window,
      audioContext: !!(window.AudioContext || window.webkitAudioContext),
      canvas: !!document.createElement("canvas").getContext,
      fetch: "fetch" in window,
      localStorage: "localStorage" in window,
      webGL: !!document.createElement("canvas").getContext("webgl"),
    };
  }

  /**
   * Safe JSON parse with error handling
   * @param {string} jsonString - JSON string to parse
   * @param {*} defaultValue - Default value if parsing fails
   * @returns {*} Parsed object or default value
   */
  static safeJSONParse(jsonString, defaultValue = null) {
    try {
      return JSON.parse(jsonString);
    } catch (error) {
      console.error("JSON parse error:", error);
      return defaultValue;
    }
  }

  /**
   * Create a download link for data
   * @param {*} data - Data to download
   * @param {string} filename - Filename for download
   * @param {string} mimeType - MIME type of the data
   */
  static downloadData(data, filename, mimeType = "application/json") {
    const blob = new Blob(
      [typeof data === "string" ? data : JSON.stringify(data, null, 2)],
      {
        type: mimeType,
      }
    );
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = filename;
    a.click();
    URL.revokeObjectURL(url);
  }

  /**
   * Log with timestamp and level
   * @param {string} level - Log level (info, warn, error)
   * @param {string} message - Log message
   * @param {*} data - Additional data to log
   */
  static log(level, message, data = null) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}`;

    switch (level.toLowerCase()) {
      case "error":
        console.error(logMessage, data);
        break;
      case "warn":
        console.warn(logMessage, data);
        break;
      default:
        console.log(logMessage, data);
    }
  }
}

// Export for use in other modules
window.FeverMonitor = window.FeverMonitor || {};
window.FeverMonitor.Utils = Utils;
