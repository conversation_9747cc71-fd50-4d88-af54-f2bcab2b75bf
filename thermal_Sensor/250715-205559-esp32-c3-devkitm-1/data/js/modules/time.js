/**
 * Time Management Module
 * Handles time synchronization, display, and night mode scheduling
 */

window.FeverMonitor = window.FeverMonitor || {};

window.FeverMonitor.Time = (function () {
  "use strict";

  // Private variables
  let timeStatus = {
    timeSet: false,
    status: "Not Set",
    currentTime: "--:--:--",
    currentDate: "----------",
    timeSinceSync: 0,
    nightMode: false,
    ntpServer: "pool.ntp.org",
    timezone: "EST5EDT",
    autoSync: true,
    nightModeEnabled: true,
    nightStart: "23:00",
    nightEnd: "08:00",
    displayOffAtNight: true,
  };

  let updateInterval = null;
  let isUpdating = false;

  // Private methods
  function updateTimeDisplay() {
    if (isUpdating) return;
    isUpdating = true;

    fetch("/time-status")
      .then((response) => response.json())
      .then((data) => {
        timeStatus = { ...timeStatus, ...data };

        // Update header display
        const deviceTimeEl = document.getElementById("deviceTime");
        const deviceDateEl = document.getElementById("deviceDate");
        const timeStatusEl = document.getElementById("timeStatus");

        if (deviceTimeEl) deviceTimeEl.textContent = timeStatus.currentTime;
        if (deviceDateEl) deviceDateEl.textContent = timeStatus.currentDate;

        if (timeStatusEl) {
          timeStatusEl.textContent = timeStatus.status;
          timeStatusEl.className = `badge ${getStatusBadgeClass(
            timeStatus.status
          )}`;
        }

        // Update settings form if visible
        updateSettingsForm();

        // Show night mode indicator
        updateNightModeIndicator();
      })
      .catch((error) => {
        console.error("Error fetching time status:", error);
        if (window.FeverMonitor.Notifications) {
          window.FeverMonitor.Notifications.showError(
            "Failed to update time status"
          );
        }
      })
      .finally(() => {
        isUpdating = false;
      });
  }

  function getStatusBadgeClass(status) {
    switch (status.toLowerCase()) {
      case "synced":
        return "badge-success";
      case "syncing":
        return "badge-warning";
      case "sync failed":
        return "badge-danger";
      default:
        return "badge-secondary";
    }
  }

  function updateSettingsForm() {
    // Update time settings form fields
    const ntpServerEl = document.getElementById("ntpServer");
    const timezoneEl = document.getElementById("timezone");
    const autoSyncEl = document.getElementById("autoTimeSync");
    const nightModeEl = document.getElementById("nightModeEnabled");
    const displayOffEl = document.getElementById("displayOffAtNight");

    console.log("Updating time settings form with:", timeStatus);

    if (ntpServerEl) {
      const ntpValue = timeStatus.ntpServer || "pool.ntp.org";
      if (ntpValue.trim() !== "") {
        ntpServerEl.value = ntpValue;
        console.log("Set NTP server to:", ntpServerEl.value);
      } else {
        console.warn(
          "Empty NTP server value, keeping current:",
          ntpServerEl.value
        );
      }
    } else {
      console.warn("NTP server element not found");
    }

    if (timezoneEl) {
      const timezoneValue = timeStatus.timezone || "EST5EDT";
      if (timezoneValue.trim() !== "") {
        timezoneEl.value = timezoneValue;
        console.log("Set timezone to:", timezoneEl.value);
      } else {
        console.warn(
          "Empty timezone value, keeping current:",
          timezoneEl.value
        );
      }
    } else {
      console.warn("Timezone element not found");
    }

    if (autoSyncEl) {
      autoSyncEl.checked = timeStatus.autoSync || false;
      console.log("Set auto sync to:", autoSyncEl.checked);
    }

    if (nightModeEl) {
      nightModeEl.checked = timeStatus.nightModeEnabled || false;
      console.log("Set night mode to:", nightModeEl.checked);
    }

    if (displayOffEl) {
      displayOffEl.checked = timeStatus.displayOffAtNight || false;
      console.log("Set display off to:", displayOffEl.checked);
    }

    // Parse and set night start/end times
    if (timeStatus.nightStart) {
      const [startHour, startMinute] = timeStatus.nightStart.split(":");
      const nightStartHourEl = document.getElementById("nightStartHour");
      const nightStartMinuteEl = document.getElementById("nightStartMinute");
      if (nightStartHourEl && startHour !== undefined) {
        nightStartHourEl.value = parseInt(startHour);
        console.log("Set night start hour to:", nightStartHourEl.value);
      }
      if (nightStartMinuteEl && startMinute !== undefined) {
        nightStartMinuteEl.value = parseInt(startMinute);
        console.log("Set night start minute to:", nightStartMinuteEl.value);
      }
    }

    if (timeStatus.nightEnd) {
      const [endHour, endMinute] = timeStatus.nightEnd.split(":");
      const nightEndHourEl = document.getElementById("nightEndHour");
      const nightEndMinuteEl = document.getElementById("nightEndMinute");
      if (nightEndHourEl && endHour !== undefined) {
        nightEndHourEl.value = parseInt(endHour);
        console.log("Set night end hour to:", nightEndHourEl.value);
      }
      if (nightEndMinuteEl && endMinute !== undefined) {
        nightEndMinuteEl.value = parseInt(endMinute);
        console.log("Set night end minute to:", nightEndMinuteEl.value);
      }
    }
  }

  function updateNightModeIndicator() {
    // Add night mode indicator to header if active
    const headerEl = document.querySelector(".header-section");
    if (!headerEl) return;

    let nightIndicator = document.getElementById("nightModeIndicator");

    if (timeStatus.nightMode && timeStatus.nightModeEnabled) {
      if (!nightIndicator) {
        nightIndicator = document.createElement("div");
        nightIndicator.id = "nightModeIndicator";
        nightIndicator.className = "alert alert-info mt-2";
        nightIndicator.innerHTML =
          '<i class="fas fa-moon"></i> Night Mode Active - Display off only when no presence detected';
        headerEl.appendChild(nightIndicator);
      }
    } else {
      if (nightIndicator) {
        nightIndicator.remove();
      }
    }
  }

  function syncTimeNow() {
    if (window.FeverMonitor.Notifications) {
      window.FeverMonitor.Notifications.showInfo("Synchronizing time...");
    }

    fetch("/sync-time", { method: "POST" })
      .then((response) => response.text())
      .then((message) => {
        if (window.FeverMonitor.Notifications) {
          window.FeverMonitor.Notifications.showSuccess(message);
        }
        // Update time display immediately
        setTimeout(updateTimeDisplay, 1000);
      })
      .catch((error) => {
        console.error("Error syncing time:", error);
        if (window.FeverMonitor.Notifications) {
          window.FeverMonitor.Notifications.showError("Failed to sync time");
        }
      });
  }

  function setTimeManually() {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1;
    const day = now.getDate();
    const hour = now.getHours();
    const minute = now.getMinutes();
    const second = now.getSeconds();

    const timeData = {
      year: year,
      month: month,
      day: day,
      hour: hour,
      minute: minute,
      second: second,
    };

    if (window.FeverMonitor.Notifications) {
      window.FeverMonitor.Notifications.showInfo("Setting time manually...");
    }

    fetch("/set-time", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(timeData),
    })
      .then((response) => response.text())
      .then((message) => {
        if (window.FeverMonitor.Notifications) {
          window.FeverMonitor.Notifications.showSuccess(message);
        }
        // Update time display immediately
        setTimeout(updateTimeDisplay, 1000);
      })
      .catch((error) => {
        console.error("Error setting time:", error);
        if (window.FeverMonitor.Notifications) {
          window.FeverMonitor.Notifications.showError("Failed to set time");
        }
      });
  }

  function saveTimeSettings() {
    const settings = {
      ntpServer: document.getElementById("ntpServer")?.value || "pool.ntp.org",
      timezone: document.getElementById("timezone")?.value || "EST5EDT",
      autoSync: document.getElementById("autoTimeSync")?.checked || false,
      nightModeEnabled:
        document.getElementById("nightModeEnabled")?.checked || false,
      nightStartHour:
        parseInt(document.getElementById("nightStartHour")?.value) || 23,
      nightStartMinute:
        parseInt(document.getElementById("nightStartMinute")?.value) || 0,
      nightEndHour:
        parseInt(document.getElementById("nightEndHour")?.value) || 8,
      nightEndMinute:
        parseInt(document.getElementById("nightEndMinute")?.value) || 0,
      displayOffAtNight:
        document.getElementById("displayOffAtNight")?.checked || false,
    };

    console.log("Saving time settings:", settings);

    // Store the current form values to preserve them after save
    const formValues = {
      ntpServer: settings.ntpServer,
      timezone: settings.timezone,
      autoSync: settings.autoSync,
      nightModeEnabled: settings.nightModeEnabled,
      nightStartHour: settings.nightStartHour,
      nightStartMinute: settings.nightStartMinute,
      nightEndHour: settings.nightEndHour,
      nightEndMinute: settings.nightEndMinute,
      displayOffAtNight: settings.displayOffAtNight,
    };

    return fetch("/save-time-settings", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(settings),
    })
      .then((response) => response.text())
      .then((message) => {
        if (window.FeverMonitor.Notifications) {
          window.FeverMonitor.Notifications.showSuccess(
            "Time settings saved successfully"
          );
        }

        // Update the timeStatus object with the saved values to prevent form clearing
        timeStatus.ntpServer = formValues.ntpServer;
        timeStatus.timezone = formValues.timezone;
        timeStatus.autoSync = formValues.autoSync;
        timeStatus.nightModeEnabled = formValues.nightModeEnabled;
        timeStatus.nightStart = `${formValues.nightStartHour}:${formValues.nightStartMinute}`;
        timeStatus.nightEnd = `${formValues.nightEndHour}:${formValues.nightEndMinute}`;
        timeStatus.displayOffAtNight = formValues.displayOffAtNight;

        console.log("Updated timeStatus with saved values:", timeStatus);

        // Update time display and refresh form with preserved values
        setTimeout(() => {
          updateTimeDisplay();
          // Ensure form is updated with the correct values after a delay
          setTimeout(() => {
            updateSettingsForm();
          }, 1000);
        }, 500);
        return true;
      })
      .catch((error) => {
        console.error("Error saving time settings:", error);
        if (window.FeverMonitor.Notifications) {
          window.FeverMonitor.Notifications.showError(
            "Failed to save time settings"
          );
        }
        return false;
      });
  }

  // Public API
  return {
    init: function () {
      console.log("Time module initialized");

      // Start time status updates
      updateTimeDisplay();
      updateInterval = setInterval(updateTimeDisplay, 30000); // Update every 30 seconds

      // Make functions globally available for onclick handlers
      window.syncTimeNow = syncTimeNow;
      window.setTimeManually = setTimeManually;
      window.updateTimeSettingsForm = updateSettingsForm;

      return true;
    },

    destroy: function () {
      if (updateInterval) {
        clearInterval(updateInterval);
        updateInterval = null;
      }
    },

    getTimeStatus: function () {
      return { ...timeStatus };
    },

    syncTime: syncTimeNow,
    setTimeManually: setTimeManually,
    saveTimeSettings: saveTimeSettings,
    updateDisplay: updateTimeDisplay,
  };
})();
