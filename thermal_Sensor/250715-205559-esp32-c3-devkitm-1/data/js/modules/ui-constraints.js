/**
 * UI Constraints Module
 * Dynamically applies centralized UI constraints to HTML form elements
 */

class UIConstraintsManager {
  constructor() {
    this.isInitialized = false;
  }

  /**
   * Initialize UI constraints by applying them to DOM elements
   */
  initialize() {
    if (this.isInitialized) {
      console.warn('UIConstraintsManager already initialized');
      return true;
    }

    try {
      this.applyConstraintsToDOM();
      this.isInitialized = true;
      console.log('✅ UI constraints applied to DOM elements');
      return true;
    } catch (error) {
      console.error('❌ Failed to initialize UI constraints:', error);
      return false;
    }
  }

  /**
   * Apply UI constraints to all relevant DOM elements
   */
  applyConstraintsToDOM() {
    if (!window.FeverMonitor || !window.FeverMonitor.ConfigManager) {
      throw new Error('ConfigManager not available');
    }

    const constraints = window.FeverMonitor.ConfigManager.getAllUIConstraints();
    let appliedCount = 0;
    let skippedCount = 0;

    Object.keys(constraints).forEach(settingKey => {
      const element = document.getElementById(settingKey);
      if (element && element.type === 'range') {
        const constraint = constraints[settingKey];
        
        // Apply constraints to the range input
        element.min = constraint.min;
        element.max = constraint.max;
        element.step = constraint.step;
        
        appliedCount++;
        console.log(`Applied constraints to ${settingKey}:`, constraint);
      } else {
        skippedCount++;
        if (element) {
          console.log(`Skipped ${settingKey}: not a range input (type: ${element.type})`);
        } else {
          console.log(`Skipped ${settingKey}: element not found`);
        }
      }
    });

    console.log(`UI constraints applied: ${appliedCount} elements updated, ${skippedCount} skipped`);
  }

  /**
   * Update constraints for a specific element
   * @param {string} settingKey - The setting key
   * @param {Object} newConstraints - New constraints object with min, max, step
   */
  updateElementConstraints(settingKey, newConstraints) {
    const element = document.getElementById(settingKey);
    if (!element || element.type !== 'range') {
      console.warn(`Cannot update constraints for ${settingKey}: element not found or not a range input`);
      return false;
    }

    if (newConstraints.min !== undefined) element.min = newConstraints.min;
    if (newConstraints.max !== undefined) element.max = newConstraints.max;
    if (newConstraints.step !== undefined) element.step = newConstraints.step;

    console.log(`Updated constraints for ${settingKey}:`, newConstraints);
    return true;
  }

  /**
   * Validate that all range inputs have proper constraints applied
   * @returns {Object} Validation report
   */
  validateConstraintsApplied() {
    if (!window.FeverMonitor || !window.FeverMonitor.ConfigManager) {
      return { isValid: false, message: 'ConfigManager not available' };
    }

    const constraints = window.FeverMonitor.ConfigManager.getAllUIConstraints();
    const report = {
      isValid: true,
      totalElements: 0,
      validElements: 0,
      invalidElements: [],
      missingElements: []
    };

    Object.keys(constraints).forEach(settingKey => {
      const element = document.getElementById(settingKey);
      report.totalElements++;

      if (!element) {
        report.missingElements.push(settingKey);
        report.isValid = false;
        return;
      }

      if (element.type !== 'range') {
        // Skip non-range elements
        return;
      }

      const constraint = constraints[settingKey];
      const elementMin = parseFloat(element.min);
      const elementMax = parseFloat(element.max);
      const elementStep = parseFloat(element.step);

      if (elementMin !== constraint.min || 
          elementMax !== constraint.max || 
          elementStep !== constraint.step) {
        report.invalidElements.push({
          settingKey,
          expected: constraint,
          actual: { min: elementMin, max: elementMax, step: elementStep }
        });
        report.isValid = false;
      } else {
        report.validElements++;
      }
    });

    return report;
  }

  /**
   * Get current constraints applied to a specific element
   * @param {string} settingKey - The setting key
   * @returns {Object|null} Current constraints or null if element not found
   */
  getElementConstraints(settingKey) {
    const element = document.getElementById(settingKey);
    if (!element || element.type !== 'range') {
      return null;
    }

    return {
      min: parseFloat(element.min),
      max: parseFloat(element.max),
      step: parseFloat(element.step)
    };
  }
}

// Create and export singleton instance
window.FeverMonitor = window.FeverMonitor || {};
window.FeverMonitor.UIConstraintsManager = new UIConstraintsManager();
