/**
 * Heatmap Visualization Module
 * Handles thermal sensor data visualization using heatmap.js
 */

class HeatmapManager {
  constructor() {
    this.heatmapInstance = null;
    this.markerElement = null;
    this.colorbarElement = null;
    this.colorbarContext = null;
    this.isInitialized = false;

    // Heatmap configuration
    this.config = {
      size: 8, // 8x8 thermal sensor grid
      containerSize: 256, // Container size in pixels (matches working version)
      radius: null, // Will be calculated
      blur: 0.85, // Match working version
      gradient: {
        ".2": "blue",
        ".4": "cyan",
        ".6": "lime",
        ".8": "yellow",
        1: "red",
      },
    };

    this.calculateRadius();
  }

  /**
   * Calculate heatmap radius based on container size
   */
  calculateRadius() {
    // Match working version: C / size * 1.5 = 256 / 8 * 1.5 = 48
    this.config.radius = (this.config.containerSize / this.config.size) * 1.5;
  }

  /**
   * Initialize the heatmap visualization
   * @returns {boolean} Success status
   */
  initialize() {
    try {
      // Check if heatmap.js library is loaded
      if (typeof h337 === "undefined") {
        console.error("heatmap.js library not loaded! Check CDN connection.");
        window.FeverMonitor.NotificationManager.showSystemStatus(
          "error",
          "Heatmap Error",
          "heatmap.js library failed to load. Check internet connection."
        );
        return false;
      }

      // Find heatmap container
      const heatmapContainer = document.getElementById("heatmapContainer");
      if (!heatmapContainer) {
        console.error("Heatmap container not found!");
        window.FeverMonitor.NotificationManager.showSystemStatus(
          "error",
          "Heatmap Error",
          "Heatmap container element not found in DOM."
        );
        return false;
      }

      console.log("Heatmap container found:", heatmapContainer);
      console.log(
        "Container dimensions:",
        heatmapContainer.offsetWidth,
        "x",
        heatmapContainer.offsetHeight
      );

      // Create heatmap instance
      this.heatmapInstance = h337.create({
        container: heatmapContainer,
        radius: this.config.radius,
        blur: this.config.blur,
        gradient: this.config.gradient,
      });

      console.log("Heatmap instance created:", this.heatmapInstance);

      // Initialize marker and colorbar elements
      if (!this.initializeElements()) {
        return false;
      }

      this.isInitialized = true;
      console.log("Heatmap initialization successful!");

      window.FeverMonitor.NotificationManager.showSystemStatus(
        "ready",
        "Heatmap Ready",
        "Thermal visualization initialized successfully."
      );

      return true;
    } catch (error) {
      console.error("Error initializing heatmap:", error);
      window.FeverMonitor.NotificationManager.showSystemStatus(
        "error",
        "Heatmap Error",
        "Failed to initialize heatmap: " + error.message
      );
      return false;
    }
  }

  /**
   * Initialize marker and colorbar elements
   * @returns {boolean} Success status
   */
  initializeElements() {
    // Find marker element
    this.markerElement = document.getElementById("marker");
    if (!this.markerElement) {
      console.error("Marker element not found!");
      window.FeverMonitor.NotificationManager.showSystemStatus(
        "error",
        "Heatmap Error",
        "Marker element not found in DOM."
      );
      return false;
    }

    // Find colorbar element
    this.colorbarElement = document.getElementById("colorbar");
    if (!this.colorbarElement) {
      console.error("Colorbar element not found!");
      window.FeverMonitor.NotificationManager.showSystemStatus(
        "error",
        "Heatmap Error",
        "Colorbar element not found in DOM."
      );
      return false;
    }

    // Get colorbar canvas context
    try {
      this.colorbarContext = this.colorbarElement.getContext("2d");
      console.log("Canvas context created:", this.colorbarContext);
    } catch (error) {
      console.error("Error getting canvas context:", error);
      window.FeverMonitor.NotificationManager.showSystemStatus(
        "error",
        "Heatmap Error",
        "Failed to get canvas context: " + error.message
      );
      return false;
    }

    return true;
  }

  /**
   * Update heatmap with new thermal data
   * @param {Array} thermalData - Array of 64 temperature values
   * @returns {boolean} Success status
   */
  updateHeatmap(thermalData) {
    if (!this.isInitialized) {
      console.warn("Heatmap not initialized - skipping update");
      return false;
    }

    if (!window.FeverMonitor.Utils.isValidTemperatureArray(thermalData, 64)) {
      console.error("Invalid thermal data:", thermalData);
      return false;
    }

    try {
      const minTemp = Math.min(...thermalData);
      const maxTemp = Math.max(...thermalData);

      console.log(
        `Compensated temperature range: ${minTemp.toFixed(
          1
        )}°F to ${maxTemp.toFixed(1)}°F`
      );

      // Convert thermal data to heatmap points
      const heatmapPoints = this.convertToHeatmapPoints(thermalData);

      console.log("Mapped points for heatmap:", heatmapPoints.length, "points");

      // Update heatmap
      this.heatmapInstance.setData({
        min: minTemp,
        max: maxTemp,
        data: heatmapPoints,
      });

      // Update colorbar
      this.drawColorbar(minTemp, maxTemp);

      // Update marker position
      this.updateMarker(thermalData, maxTemp);

      return true;
    } catch (error) {
      console.error("Error updating heatmap:", error);
      return false;
    }
  }

  /**
   * Convert thermal data array to heatmap points
   * @param {Array} thermalData - Array of temperature values
   * @returns {Array} Array of heatmap point objects
   */
  convertToHeatmapPoints(thermalData) {
    // Match working version coordinate calculation exactly
    const C = this.config.containerSize; // 256
    const size = this.config.size; // 8

    return thermalData.map((temperature, index) => ({
      x: (((index % size) + 0.5) * C) / size,
      y: ((Math.floor(index / size) + 0.5) * C) / size,
      value: temperature,
    }));
  }

  /**
   * Draw temperature colorbar
   * @param {number} minTemp - Minimum temperature (now in Fahrenheit from compensated data)
   * @param {number} maxTemp - Maximum temperature (now in Fahrenheit from compensated data)
   */
  drawColorbar(minTemp, maxTemp) {
    if (!this.colorbarContext || !this.colorbarElement) return;

    const canvas = this.colorbarElement;
    const ctx = this.colorbarContext;

    // Temperature data is now already in Fahrenheit from compensated pixels
    const minF = minTemp;
    const maxF = maxTemp;

    // Create gradient
    const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
    gradient.addColorStop(0, "#ff0000");
    gradient.addColorStop(0.2, "#ffff00");
    gradient.addColorStop(0.4, "#00ff00");
    gradient.addColorStop(0.6, "#00ffff");
    gradient.addColorStop(1, "#0066ff");

    // Clear and fill with gradient
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Add temperature labels
    ctx.fillStyle = "#fff";
    ctx.font = "14px Inter, sans-serif";
    ctx.fontWeight = "600";
    ctx.textAlign = "center";

    // Max temperature at top
    ctx.fillText(maxF.toFixed(1) + "°F", canvas.width / 2, 15);

    // Min temperature at bottom
    ctx.fillText(minF.toFixed(1) + "°F", canvas.width / 2, canvas.height - 5);

    // Middle temperature
    const midF = (minF + maxF) / 2;
    ctx.fillText(midF.toFixed(1) + "°F", canvas.width / 2, canvas.height / 2);
  }

  /**
   * Update marker position to show hottest spot
   * @param {Array} thermalData - Array of temperature values
   * @param {number} maxTemp - Maximum temperature value
   */
  updateMarker(thermalData, maxTemp) {
    if (!this.markerElement) return;

    // Find index of maximum temperature
    const maxIndex = thermalData.indexOf(maxTemp);

    // Calculate marker position using same formula as working version
    const C = this.config.containerSize; // 256
    const size = this.config.size; // 8
    const x = (((maxIndex % size) + 0.5) * C) / size;
    const y = ((Math.floor(maxIndex / size) + 0.5) * C) / size;

    // Position marker
    this.markerElement.style.left = `${x}px`;
    this.markerElement.style.top = `${y}px`;

    // Update marker content
    // maxTemp is already in Fahrenheit from compensatedPixels - no conversion needed
    this.markerElement.innerHTML = `
      <i class="fas fa-crosshairs"></i><br>
      ${maxTemp.toFixed(1)}°F
    `;

    console.log(
      `Marker positioned at (${x}, ${y}) for max temp ${maxTemp.toFixed(1)}°F`
    );
  }

  /**
   * Test heatmap with dummy data
   */
  testWithDummyData() {
    if (!this.isInitialized) {
      console.warn("Heatmap not initialized - cannot test");
      return;
    }

    console.log("Testing heatmap with dummy data...");

    // Generate test data (64 values between 20-30°C)
    const testData = Array.from({ length: 64 }, (_, i) => {
      // Create a hot spot in the center
      const row = Math.floor(i / 8);
      const col = i % 8;
      const centerDistance = Math.sqrt(
        Math.pow(row - 3.5, 2) + Math.pow(col - 3.5, 2)
      );
      return 20 + Math.random() * 5 + Math.max(0, 8 - centerDistance * 2);
    });

    this.updateHeatmap(testData);
    console.log("Test heatmap data applied");
  }

  /**
   * Resize heatmap container
   * @param {number} newSize - New container size in pixels
   */
  resize(newSize) {
    this.config.containerSize = newSize;
    this.calculateRadius();

    if (this.isInitialized) {
      // Reinitialize with new size
      this.initialize();
    }
  }

  /**
   * Update heatmap configuration
   * @param {Object} newConfig - New configuration options
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    this.calculateRadius();

    if (this.isInitialized && this.heatmapInstance) {
      // Update existing heatmap instance
      this.heatmapInstance._config.radius = this.config.radius;
      this.heatmapInstance._config.blur = this.config.blur;
      this.heatmapInstance._config.gradient = this.config.gradient;
    }
  }

  /**
   * Get current heatmap status
   * @returns {Object} Status information
   */
  getStatus() {
    return {
      initialized: this.isInitialized,
      hasInstance: !!this.heatmapInstance,
      hasMarker: !!this.markerElement,
      hasColorbar: !!this.colorbarElement,
      config: { ...this.config },
    };
  }

  /**
   * Destroy heatmap instance and clean up
   */
  destroy() {
    if (this.heatmapInstance) {
      // heatmap.js doesn't have a destroy method, so we clear the container
      const container = this.heatmapInstance._config.container;
      if (container) {
        container.innerHTML = "";
      }
    }

    this.heatmapInstance = null;
    this.markerElement = null;
    this.colorbarElement = null;
    this.colorbarContext = null;
    this.isInitialized = false;
  }
}

// Create and export singleton instance
const heatmapManager = new HeatmapManager();

// Export for use in other modules
window.FeverMonitor = window.FeverMonitor || {};
window.FeverMonitor.HeatmapManager = heatmapManager;
