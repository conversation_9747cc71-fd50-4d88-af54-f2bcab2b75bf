/**
 * Temperature Chart Module
 * Handles Chart.js initialization and temperature data visualization
 */

class ChartManager {
  constructor() {
    this.chartInstance = null;
    this.temperatureHistory = [];
    this.isInitialized = false;
    this.maxDataPoints = 50;
    
    // Chart configuration
    this.config = {
      type: 'line',
      data: {
        labels: [],
        datasets: [
          {
            label: 'Body Temperature',
            data: [],
            borderColor: '#ef4444',
            backgroundColor: 'rgba(239, 68, 68, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4,
          },
          {
            label: 'Baseline',
            data: [],
            borderColor: '#10b981',
            backgroundColor: 'rgba(16, 185, 129, 0.1)',
            borderWidth: 2,
            borderDash: [5, 5],
            fill: false,
          },
          {
            label: 'Fever Threshold',
            data: [],
            borderColor: '#f59e0b',
            backgroundColor: 'rgba(245, 158, 11, 0.1)',
            borderWidth: 2,
            borderDash: [10, 5],
            fill: false,
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: { 
            labels: { color: '#f8fafc' } 
          },
          title: {
            display: true,
            text: 'Temperature Monitoring',
            color: '#f8fafc',
          },
        },
        scales: {
          x: {
            ticks: { color: '#94a3b8' },
            grid: { color: '#334155' },
          },
          y: {
            ticks: { color: '#94a3b8' },
            grid: { color: '#334155' },
            beginAtZero: false,
          },
        },
        elements: {
          point: { radius: 4, hoverRadius: 6 },
        },
        animation: {
          duration: 750,
          easing: 'easeInOutQuart'
        }
      },
    };
  }

  /**
   * Initialize the temperature chart
   * @returns {boolean} Success status
   */
  initialize() {
    try {
      // Check if Chart.js is loaded
      if (typeof Chart === 'undefined') {
        console.error('Chart.js library not loaded!');
        window.FeverMonitor.NotificationManager.showSystemStatus(
          'error',
          'Chart Error',
          'Chart.js library failed to load. Check internet connection.'
        );
        return false;
      }

      // Find chart canvas element
      const canvas = document.getElementById('tempChart');
      if (!canvas) {
        console.error('Chart canvas element not found!');
        window.FeverMonitor.NotificationManager.showSystemStatus(
          'error',
          'Chart Error',
          'Chart canvas element not found in DOM.'
        );
        return false;
      }

      // Get canvas context
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        console.error('Failed to get canvas context!');
        return false;
      }

      // Create chart instance
      this.chartInstance = new Chart(ctx, this.config);
      this.isInitialized = true;

      console.log('Chart initialized successfully');
      
      // Add test data point to verify chart is working
      setTimeout(() => {
        console.log('Adding test data point');
        this.addDataPoint(98.6, 98.0, 2.0);
      }, 1000);

      return true;
    } catch (error) {
      console.error('Error initializing chart:', error);
      window.FeverMonitor.NotificationManager.showSystemStatus(
        'error',
        'Chart Error',
        'Failed to initialize chart: ' + error.message
      );
      return false;
    }
  }

  /**
   * Add a new temperature data point
   * @param {number} currentTemp - Current temperature
   * @param {number} baselineTemp - Baseline temperature
   * @param {number} feverThreshold - Fever threshold offset
   * @returns {boolean} Success status
   */
  addDataPoint(currentTemp, baselineTemp, feverThreshold) {
    if (!this.isInitialized) {
      console.error('Chart not initialized');
      return false;
    }

    // Validate input data
    if (!window.FeverMonitor.Utils.isValidTemperature(currentTemp) ||
        !window.FeverMonitor.Utils.isValidTemperature(baselineTemp) ||
        !window.FeverMonitor.Utils.isValidTemperature(feverThreshold)) {
      console.error('Invalid temperature data:', {
        currentTemp,
        baselineTemp,
        feverThreshold,
      });
      return false;
    }

    const now = new Date();
    const timeLabel = window.FeverMonitor.Utils.getTimestamp(now);

    console.log('Adding chart data point:', {
      currentTemp,
      baselineTemp,
      feverThreshold,
      timeLabel,
    });

    // Create data point
    const dataPoint = {
      time: timeLabel,
      timestamp: now,
      temp: currentTemp,
      baseline: baselineTemp,
      threshold: baselineTemp + feverThreshold,
    };

    // Add to history
    this.temperatureHistory.push(dataPoint);

    // Keep only last maxDataPoints
    if (this.temperatureHistory.length > this.maxDataPoints) {
      this.temperatureHistory.shift();
    }

    // Update chart data
    this.updateChartData();

    return true;
  }

  /**
   * Update chart with current temperature history
   */
  updateChartData() {
    if (!this.isInitialized || !this.chartInstance) {
      return;
    }

    // Update chart datasets
    this.chartInstance.data.labels = this.temperatureHistory.map(h => h.time);
    this.chartInstance.data.datasets[0].data = this.temperatureHistory.map(h => h.temp);
    this.chartInstance.data.datasets[1].data = this.temperatureHistory.map(h => h.baseline);
    this.chartInstance.data.datasets[2].data = this.temperatureHistory.map(h => h.threshold);

    console.log('Chart data updated:', {
      labels: this.chartInstance.data.labels.length,
      bodyTemp: this.chartInstance.data.datasets[0].data.length,
      baseline: this.chartInstance.data.datasets[1].data.length,
      threshold: this.chartInstance.data.datasets[2].data.length,
    });

    // Update chart with animation
    this.chartInstance.update('active');
  }

  /**
   * Clear all chart data
   */
  clearData() {
    this.temperatureHistory = [];
    if (this.isInitialized && this.chartInstance) {
      this.chartInstance.data.labels = [];
      this.chartInstance.data.datasets.forEach(dataset => {
        dataset.data = [];
      });
      this.chartInstance.update();
    }
  }

  /**
   * Update chart configuration
   * @param {Object} newConfig - New configuration options
   */
  updateConfig(newConfig) {
    if (!this.isInitialized || !this.chartInstance) {
      return;
    }

    // Deep merge configuration
    this.config = this.deepMerge(this.config, newConfig);
    
    // Apply configuration to chart
    Object.assign(this.chartInstance.config, this.config);
    this.chartInstance.update();
  }

  /**
   * Update chart colors for dark/light theme
   * @param {string} theme - Theme name ('dark' or 'light')
   */
  updateTheme(theme) {
    if (!this.isInitialized || !this.chartInstance) {
      return;
    }

    const colors = theme === 'dark' ? {
      text: '#f8fafc',
      grid: '#334155',
      tickColor: '#94a3b8'
    } : {
      text: '#1e293b',
      grid: '#e2e8f0',
      tickColor: '#64748b'
    };

    // Update chart colors
    this.chartInstance.options.plugins.legend.labels.color = colors.text;
    this.chartInstance.options.plugins.title.color = colors.text;
    this.chartInstance.options.scales.x.ticks.color = colors.tickColor;
    this.chartInstance.options.scales.x.grid.color = colors.grid;
    this.chartInstance.options.scales.y.ticks.color = colors.tickColor;
    this.chartInstance.options.scales.y.grid.color = colors.grid;

    this.chartInstance.update();
  }

  /**
   * Set maximum number of data points to display
   * @param {number} maxPoints - Maximum number of points
   */
  setMaxDataPoints(maxPoints) {
    this.maxDataPoints = Math.max(10, Math.min(200, maxPoints));
    
    // Trim existing data if necessary
    if (this.temperatureHistory.length > this.maxDataPoints) {
      this.temperatureHistory = this.temperatureHistory.slice(-this.maxDataPoints);
      this.updateChartData();
    }
  }

  /**
   * Get temperature statistics from current data
   * @returns {Object} Temperature statistics
   */
  getTemperatureStats() {
    if (this.temperatureHistory.length === 0) {
      return null;
    }

    const temps = this.temperatureHistory.map(h => h.temp);
    const baselines = this.temperatureHistory.map(h => h.baseline);

    return {
      current: temps[temps.length - 1],
      min: Math.min(...temps),
      max: Math.max(...temps),
      average: temps.reduce((sum, temp) => sum + temp, 0) / temps.length,
      baseline: baselines[baselines.length - 1],
      dataPoints: this.temperatureHistory.length,
      timeRange: this.temperatureHistory.length > 1 ? {
        start: this.temperatureHistory[0].timestamp,
        end: this.temperatureHistory[this.temperatureHistory.length - 1].timestamp
      } : null
    };
  }

  /**
   * Export chart data as JSON
   * @returns {string} JSON string of chart data
   */
  exportData() {
    return JSON.stringify({
      timestamp: new Date().toISOString(),
      temperatureHistory: this.temperatureHistory,
      stats: this.getTemperatureStats(),
      config: this.config
    }, null, 2);
  }

  /**
   * Import chart data from JSON
   * @param {string} jsonData - JSON string containing chart data
   * @returns {boolean} Success status
   */
  importData(jsonData) {
    try {
      const data = JSON.parse(jsonData);
      if (data.temperatureHistory && Array.isArray(data.temperatureHistory)) {
        this.temperatureHistory = data.temperatureHistory;
        this.updateChartData();
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error importing chart data:', error);
      return false;
    }
  }

  /**
   * Get chart status information
   * @returns {Object} Status information
   */
  getStatus() {
    return {
      initialized: this.isInitialized,
      hasInstance: !!this.chartInstance,
      dataPoints: this.temperatureHistory.length,
      maxDataPoints: this.maxDataPoints,
      stats: this.getTemperatureStats()
    };
  }

  /**
   * Deep merge two objects
   * @param {Object} target - Target object
   * @param {Object} source - Source object
   * @returns {Object} Merged object
   */
  deepMerge(target, source) {
    const result = { ...target };
    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = this.deepMerge(result[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    }
    return result;
  }

  /**
   * Destroy chart instance and clean up
   */
  destroy() {
    if (this.chartInstance) {
      this.chartInstance.destroy();
    }
    this.chartInstance = null;
    this.temperatureHistory = [];
    this.isInitialized = false;
  }
}

// Create and export singleton instance
const chartManager = new ChartManager();

// Export for use in other modules
window.FeverMonitor = window.FeverMonitor || {};
window.FeverMonitor.ChartManager = chartManager;
