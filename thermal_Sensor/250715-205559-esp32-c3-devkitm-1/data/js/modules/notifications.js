/**
 * Notifications and Alerts Module
 * Handles all notification display, audio alerts, and browser notifications
 */

class NotificationManager {
  constructor() {
    this.notificationQueue = [];
    this.isProcessingQueue = false;
    this.lastAlertTime = 0;
    this.alertCooldown = 5000; // 5 seconds between alerts

    // Browser audio and notification permissions removed - ESP<PERSON> buzzer handles audio alerts
  }

  // Browser notification permission requests removed - using simple beep only

  /**
   * Show a toast notification
   * @param {string} type - Notification type (success, error, info, warning)
   * @param {string} title - Notification title
   * @param {string} message - Notification message
   * @param {Object} options - Additional options
   */
  showToast(type, title, message, options = {}) {
    const {
      duration = 5000,
      position = "top-right",
      dismissible = true,
      icon = null,
    } = options;

    const toast = document.createElement("div");
    toast.className = `alert alert-${this.getBootstrapAlertClass(
      type
    )} position-fixed`;
    toast.style.cssText = this.getToastPositionStyles(position);

    const iconHtml = icon ? `<i class="${icon}"></i> ` : "";
    const dismissButton = dismissible
      ? '<button type="button" class="btn-close float-end" onclick="this.parentElement.remove()"></button>'
      : "";

    toast.innerHTML = `
      ${iconHtml}<strong>${title}</strong><br>
      ${message}
      ${dismissButton}
    `;

    // Add animation classes
    toast.style.opacity = "0";
    toast.style.transform = "translateX(100%)";
    toast.style.transition = "all 0.3s ease";

    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => {
      toast.style.opacity = "1";
      toast.style.transform = "translateX(0)";
    }, 10);

    // Auto remove after duration
    if (duration > 0) {
      setTimeout(() => {
        this.removeToast(toast);
      }, duration);
    }

    return toast;
  }

  /**
   * Remove toast with animation
   * @param {HTMLElement} toast - Toast element to remove
   */
  removeToast(toast) {
    if (!toast || !toast.parentElement) return;

    toast.style.opacity = "0";
    toast.style.transform = "translateX(100%)";

    setTimeout(() => {
      if (toast.parentElement) {
        toast.remove();
      }
    }, 300);
  }

  // Browser audio removed - ESP32 buzzer handles all audio alerts

  /**
   * Show fever alert with visual notification only
   * @param {Object} feverData - Fever detection data
   * @param {Object} settings - Current settings (unused but kept for compatibility)
   */
  showFeverAlert(feverData, settings) {
    const now = Date.now();

    // Check cooldown to prevent spam
    if (now - this.lastAlertTime < this.alertCooldown) {
      return;
    }

    this.lastAlertTime = now;

    const { currentTemp, baselineTemp, feverDuration } = feverData;
    const tempDiff = (currentTemp - baselineTemp).toFixed(1);

    // Visual toast notification only - ESP32 buzzer handles audio alerts
    this.showToast(
      "error",
      "🚨 FEVER DETECTED!",
      `Temperature: ${currentTemp.toFixed(1)}°F (${tempDiff}°F above baseline)`,
      {
        duration: 0, // Don't auto-dismiss
        icon: "fas fa-thermometer-full",
      }
    );

    // Audio alerts handled by ESP32 buzzer - no browser audio needed
  }

  /**
   * Show system status notification
   * @param {string} status - Status type (ready, error, warning, info)
   * @param {string} title - Notification title
   * @param {string} message - Notification message
   */
  showSystemStatus(status, title, message) {
    const iconMap = {
      ready: "fas fa-check-circle",
      error: "fas fa-exclamation-triangle",
      warning: "fas fa-exclamation-circle",
      info: "fas fa-info-circle",
    };

    this.showToast(status === "ready" ? "success" : status, title, message, {
      icon: iconMap[status] || iconMap.info,
      duration: status === "error" ? 0 : 5000,
    });
  }

  /**
   * Test visual alert system only
   * @param {Object} settings - Current settings (unused but kept for compatibility)
   */
  testAlerts(settings) {
    // Visual toast notification only - ESP32 buzzer handles audio testing
    this.showToast(
      "success",
      "🧪 Test Alert",
      "Visual notification system is working correctly! Audio alerts are handled by ESP32 buzzer.",
      {
        icon: "fas fa-vial",
      }
    );

    // Audio testing handled by ESP32 buzzer - no browser audio needed
  }

  /**
   * Get Bootstrap alert class for notification type
   * @param {string} type - Notification type
   * @returns {string} Bootstrap alert class
   */
  getBootstrapAlertClass(type) {
    const classMap = {
      success: "success",
      error: "danger",
      warning: "warning",
      info: "info",
    };
    return classMap[type] || "info";
  }

  /**
   * Get CSS styles for toast position
   * @param {string} position - Position string
   * @returns {string} CSS styles
   */
  getToastPositionStyles(position) {
    const baseStyles = "z-index: 9999; min-width: 300px; max-width: 400px;";

    switch (position) {
      case "top-left":
        return `${baseStyles} top: 20px; left: 20px;`;
      case "top-center":
        return `${baseStyles} top: 20px; left: 50%; transform: translateX(-50%);`;
      case "bottom-right":
        return `${baseStyles} bottom: 20px; right: 20px;`;
      case "bottom-left":
        return `${baseStyles} bottom: 20px; left: 20px;`;
      case "bottom-center":
        return `${baseStyles} bottom: 20px; left: 50%; transform: translateX(-50%);`;
      default: // top-right
        return `${baseStyles} top: 20px; right: 20px;`;
    }
  }

  /**
   * Clear all notifications
   */
  clearAllNotifications() {
    const notifications = document.querySelectorAll(".alert.position-fixed");
    notifications.forEach((notification) => {
      this.removeToast(notification);
    });
  }
}

// Create and export singleton instance
const notificationManager = new NotificationManager();

// Export for use in other modules
window.FeverMonitor = window.FeverMonitor || {};
window.FeverMonitor.NotificationManager = notificationManager;
