# Fever Monitor - Modular JavaScript Architecture

This document describes the modular architecture of the Fever Monitor application, which was refactored from a single monolithic JavaScript file into a well-organized, maintainable modular structure.

## Architecture Overview

The application is now organized into 6 core modules plus a lean main application file:

```
js/
├── fever-monitor-app.js  # Main application entry point (~250 lines)
└── modules/
    ├── utils.js          # Utility functions and helpers (~250 lines)
    ├── config.js         # Configuration and settings management (~250 lines)
    ├── notifications.js  # Notifications and alerts system (~250 lines)
    ├── heatmap.js        # Thermal heatmap visualization (~250 lines)
    ├── chart.js          # Temperature chart management (~250 lines)
    └── api.js            # API communication and data fetching (~250 lines)
```

## Module Dependencies

The modules are loaded in dependency order:

1. **utils.js** - No dependencies (core utilities)
2. **config.js** - Depends on utils.js
3. **notifications.js** - Depends on utils.js
4. **heatmap.js** - Depends on utils.js, notifications.js
5. **chart.js** - Depends on utils.js, notifications.js
6. **api.js** - Depends on utils.js, config.js, notifications.js, heatmap.js, chart.js
7. **fever-monitor-app.js** - Depends on all modules (lean main orchestrator)

## Module Details

### 1. utils.js - Utility Functions

**Purpose**: Common helper functions used throughout the application

**Key Features**:

- Temperature conversion (Celsius ↔ Fahrenheit)
- Value animation for UI elements
- Temperature formatting and validation
- Moving average calculations
- Debounce and throttle utilities
- Browser feature detection
- Data export/import helpers
- Logging utilities

**Main Class**: `Utils` (static methods)

**Global Access**: `window.FeverMonitor.Utils`

### 2. config.js - Configuration Management

**Purpose**: Manages application settings, defaults, and configuration persistence

**Key Features**:

- Default settings definition
- Settings validation and updates
- DOM synchronization for form elements
- Server-side settings persistence
- Settings import/export functionality
- Change notification system
- Slider display formatting

**Main Class**: `ConfigManager`

**Global Access**: `window.FeverMonitor.ConfigManager`

### 3. notifications.js - Notifications and Alerts

**Purpose**: Handles visual user notifications and alerts (audio handled by ESP32 buzzer)

**Key Features**:

- Toast notifications with animations
- Visual fever alert system with cooldown
- System status notifications
- Visual alert testing functionality
- Notification queue management
- Browser audio removed - ESP32 buzzer handles all audio alerts

**Main Class**: `NotificationManager`

**Global Access**: `window.FeverMonitor.NotificationManager`

### 4. heatmap.js - Thermal Visualization

**Purpose**: Manages the thermal sensor heatmap visualization

**Key Features**:

- Heatmap.js integration and initialization
- Thermal data visualization (8x8 grid)
- Temperature colorbar rendering
- Hot spot marker positioning
- Dynamic gradient coloring
- Test data generation
- Configuration management

**Main Class**: `HeatmapManager`

**Global Access**: `window.FeverMonitor.HeatmapManager`

### 5. chart.js - Temperature Chart

**Purpose**: Manages the Chart.js temperature monitoring chart

**Key Features**:

- Chart.js integration and setup
- Multi-dataset temperature tracking (current, baseline, threshold)
- Real-time data updates with animations
- Temperature history management
- Chart configuration and theming
- Data export/import functionality
- Statistics calculation

**Main Class**: `ChartManager`

**Global Access**: `window.FeverMonitor.ChartManager`

### 6. api.js - API Communication

**Purpose**: Handles all server communication and data fetching

**Key Features**:

- RESTful API communication
- Automatic retry logic with exponential backoff
- Real-time data fetching with configurable intervals
- Connection status monitoring
- Data validation and error handling
- Custom event emission for module communication
- Fever status processing and UI updates

**Main Class**: `APIManager`

**Global Access**: `window.FeverMonitor.APIManager`

### 7. app.js - Main Application Controller

**Purpose**: Orchestrates all modules and handles application lifecycle

**Key Features**:

- Module initialization and coordination
- Event listener setup and management
- Global function registration for HTML handlers
- Browser compatibility checking
- Application lifecycle management
- Error handling and recovery
- Data flow coordination between modules

**Main Class**: `FeverMonitorApp`

**Global Access**: `window.FeverMonitorApp`

## Benefits of Modular Architecture

### 1. **Maintainability**

- Each module has a single, well-defined responsibility
- Code is easier to understand and modify
- Changes to one module don't affect others
- Clear separation of concerns

### 2. **Testability**

- Individual modules can be tested in isolation
- Mock dependencies can be easily injected
- Unit testing is more straightforward
- Debugging is simplified

### 3. **Reusability**

- Modules can be reused in other projects
- Common utilities are centralized
- Configuration management is portable
- Notification system can be used elsewhere

### 4. **Scalability**

- New features can be added as separate modules
- Existing modules can be extended without affecting others
- Performance can be optimized per module
- Code splitting and lazy loading are possible

### 5. **Collaboration**

- Multiple developers can work on different modules
- Clear interfaces between modules
- Reduced merge conflicts
- Better code organization

## Communication Between Modules

### 1. **Direct Method Calls**

Modules can directly call methods on other modules through the global namespace:

```javascript
window.FeverMonitor.NotificationManager.showToast(
  "success",
  "Title",
  "Message"
);
```

### 2. **Event System**

Modules can emit and listen to custom events:

```javascript
// Emit event
document.dispatchEvent(
  new CustomEvent("feverMonitor:dataUpdate", { detail: data })
);

// Listen for event
document.addEventListener("feverMonitor:dataUpdate", (event) => {
  console.log("Data updated:", event.detail);
});
```

### 3. **Configuration Changes**

The config module provides a callback system for settings changes:

```javascript
configManager.onSettingsChange((keys, values, allSettings) => {
  // React to settings changes
});
```

## Migration from Monolithic Structure

The original `fever-monitor.js` file (853 lines) has been:

1. **Analyzed** for functional areas and dependencies
2. **Decomposed** into logical modules with clear boundaries
3. **Refactored** to use modern JavaScript classes and patterns
4. **Enhanced** with better error handling and validation
5. **Documented** with comprehensive JSDoc comments

### Original vs. Modular Comparison

| Aspect       | Original             | Modular                   |
| ------------ | -------------------- | ------------------------- |
| File Count   | 1 file (853 lines)   | 7 files (~300 lines each) |
| Structure    | Procedural functions | Object-oriented classes   |
| Dependencies | Implicit             | Explicit and documented   |
| Testing      | Difficult            | Easy (isolated modules)   |
| Maintenance  | Complex              | Simple (focused modules)  |
| Reusability  | Low                  | High                      |

## Usage Instructions

### 1. **HTML Integration**

The HTML file loads modules in dependency order:

```html
<!-- Core utilities and configuration -->
<script src="/js/modules/utils.js"></script>
<script src="/js/modules/config.js"></script>

<!-- UI and visualization modules -->
<script src="/js/modules/notifications.js"></script>
<script src="/js/modules/heatmap.js"></script>
<script src="/js/modules/chart.js"></script>

<!-- Data and API module -->
<script src="/js/modules/api.js"></script>

<!-- Main application controller -->
<script src="/js/modules/app.js"></script>
```

### 2. **Initialization**

The application automatically initializes when the DOM is ready. The main controller handles the initialization sequence.

### 3. **Accessing Modules**

All modules are available through the global `window.FeverMonitor` namespace:

```javascript
// Access configuration
const settings = window.FeverMonitor.ConfigManager.getSettings();

// Show notification
window.FeverMonitor.NotificationManager.showToast("info", "Hello", "World");

// Get application status
const status = window.FeverMonitorApp.getStatus();
```

## Future Enhancements

The modular architecture enables easy addition of new features:

1. **Data Storage Module** - Local storage and data persistence
2. **Analytics Module** - Data analysis and reporting
3. **Export Module** - Multiple export formats (PDF, CSV, etc.)
4. **Theme Module** - Dynamic theming and customization
5. **Plugin System** - Third-party module integration

## Rollback Plan

If issues arise with the modular version:

1. The original monolithic file is preserved as `fever-monitor.js` (marked as backup)
2. Simply change the HTML to load the original file instead of the modules
3. All functionality should work as before

This modular architecture provides a solid foundation for future development while maintaining all existing functionality.
