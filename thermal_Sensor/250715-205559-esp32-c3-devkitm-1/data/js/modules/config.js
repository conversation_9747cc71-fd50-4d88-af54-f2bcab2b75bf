/**
 * Configuration Module
 * Manages application settings, defaults, and configuration utilities
 */

class ConfigManager {
  constructor() {
    // Centralized UI constraints for all seekbars/sliders
    this.uiConstraints = {
      feverThreshold: { min: 1, max: 5, step: 0.1 },
      sustainedTime: { min: 5, max: 30, step: 1 },
      medicalThreshold: { min: 99.5, max: 101.5, step: 0.1 },
      compensationSensitivity: { min: 0, max: 100, step: 5 },
      baselineMin: { min: 85, max: 99, step: 0.5 },
      readInterval: { min: 100, max: 1000, step: 50 },
      displayBrightness: { min: 1, max: 255, step: 1 },
      dayVolume: { min: 0, max: 100, step: 5 },
      nightVolume: { min: 0, max: 100, step: 5 },
      feverVolume: { min: 0, max: 100, step: 5 },
    };

    this.defaultSettings = {
      feverThreshold: 2.0,
      distanceCompensation: 4.5,
      sustainedTime: 10,
      medicalThreshold: 100.4,
      dynamicCompensation: true,
      compensationSensitivity: 30.0,
      baselineMin: 90.0,
      readInterval: 100,
      debugMode: false,
      displayBrightness: 255,
      dayVolume: 100,
      nightVolume: 25,
      feverVolume: 75,
    };

    // Initialize settings with defaults
    this.settings = { ...this.defaultSettings };
    this.settingsCallbacks = new Set();
  }

  /**
   * Get current settings
   * @returns {Object} Current settings object
   */
  getSettings() {
    return { ...this.settings };
  }

  /**
   * Get UI constraints for a specific setting
   * @param {string} settingKey - The setting key
   * @returns {Object} UI constraints object with min, max, step
   */
  getUIConstraints(settingKey) {
    return this.uiConstraints[settingKey] || null;
  }

  /**
   * Get all UI constraints
   * @returns {Object} All UI constraints
   */
  getAllUIConstraints() {
    return { ...this.uiConstraints };
  }

  /**
   * Get validation constraints for a specific validation type
   * Uses UI constraints as the single source of truth
   * @param {string} validationType - The validation type key
   * @returns {Object} Validation constraints object with min, max
   */
  getValidationConstraints(validationType) {
    // Use UI constraints as the single source of truth
    if (this.uiConstraints[validationType]) {
      return {
        min: this.uiConstraints[validationType].min,
        max: this.uiConstraints[validationType].max,
      };
    }

    // For non-UI validation types, return reasonable defaults
    const defaults = {
      childBodyTemp: { min: 95.0, max: 105.0 },
      ambientRoomTemp: { min: 60.0, max: 85.0 },
      generalTemp: { min: -100, max: 200 },
    };

    return defaults[validationType] || null;
  }

  /**
   * Validate a value against specific constraints
   * @param {string} validationType - The validation type key
   * @param {number} value - The value to validate
   * @returns {Object} Validation result with isValid and message
   */
  validateValue(validationType, value) {
    const constraints = this.getValidationConstraints(validationType);
    if (!constraints) {
      return {
        isValid: false,
        message: `Unknown validation type: ${validationType}`,
      };
    }

    if (isNaN(value) || !isFinite(value)) {
      return { isValid: false, message: "Value must be a valid number" };
    }

    if (value < constraints.min || value > constraints.max) {
      return {
        isValid: false,
        message: `Value must be between ${constraints.min} and ${constraints.max}`,
      };
    }

    return { isValid: true, message: "Valid" };
  }

  /**
   * Get a specific setting value
   * @param {string} key - Setting key
   * @returns {*} Setting value
   */
  getSetting(key) {
    return this.settings[key];
  }

  /**
   * Update a setting value
   * @param {string} key - Setting key
   * @param {*} value - New value
   */
  setSetting(key, value) {
    if (this.settings.hasOwnProperty(key)) {
      this.settings[key] = value;
      this.notifySettingsChange(key, value);
    }
  }

  /**
   * Update multiple settings at once
   * @param {Object} newSettings - Object with setting key-value pairs
   */
  updateSettings(newSettings) {
    const updatedKeys = [];
    Object.keys(newSettings).forEach((key) => {
      if (this.settings.hasOwnProperty(key)) {
        this.settings[key] = newSettings[key];
        updatedKeys.push(key);
      }
    });

    if (updatedKeys.length > 0) {
      this.notifySettingsChange(updatedKeys, newSettings);
    }
  }

  /**
   * Reset settings to defaults
   */
  resetToDefaults() {
    this.settings = { ...this.defaultSettings };
    this.notifySettingsChange("all", this.settings);
  }

  /**
   * Initialize settings from DOM elements
   */
  initializeFromDOM() {
    Object.keys(this.settings).forEach((key) => {
      const element = document.getElementById(key);
      const valueElement = document.getElementById(key + "Value");

      if (element && element.type === "range") {
        element.value = this.settings[key];
        if (valueElement) {
          this.updateSliderDisplay(key, this.settings[key]);
        }

        element.addEventListener("input", (e) => {
          this.setSetting(key, parseFloat(e.target.value));
          this.updateSliderDisplay(key, this.settings[key]);

          // Special handling for display brightness - apply immediately with debounce
          if (key === "displayBrightness" && window.setDisplayBrightness) {
            // Clear any existing timeout
            if (this.brightnessTimeout) {
              clearTimeout(this.brightnessTimeout);
            }

            // Set new timeout to debounce rapid changes
            this.brightnessTimeout = setTimeout(() => {
              window.setDisplayBrightness(parseInt(e.target.value));
            }, 100); // 100ms debounce
          }
        });

        // Add change event for final value when user stops dragging
        element.addEventListener("change", (e) => {
          // Special handling for display brightness - ensure final value is applied
          if (key === "displayBrightness" && window.setDisplayBrightness) {
            // Clear any pending timeout and apply immediately
            if (this.brightnessTimeout) {
              clearTimeout(this.brightnessTimeout);
            }
            window.setDisplayBrightness(parseInt(e.target.value));
          }
        });
      } else if (element && element.type === "checkbox") {
        element.checked = this.settings[key];
        element.addEventListener("change", (e) => {
          this.setSetting(key, e.target.checked);

          // Special handling for dynamic compensation toggle
          if (key === "dynamicCompensation") {
            this.toggleSensitivityContainer(e.target.checked);
          }
        });
      }
    });

    // Initialize dynamic compensation visibility
    this.toggleSensitivityContainer(this.settings.dynamicCompensation);
  }

  /**
   * Update slider display with proper formatting
   * @param {string} key - Setting key
   * @param {*} value - Setting value
   */
  updateSliderDisplay(key, value) {
    const valueElement = document.getElementById(key + "Value");
    if (!valueElement) return;

    let displayValue = value;
    let unit = "";

    switch (key) {
      case "feverThreshold":
      case "medicalThreshold":
      case "baselineMax":
      case "baselineMin":
        unit = "°F";
        displayValue = value.toFixed(1);
        break;
      case "sustainedTime":
      case "baselineDuration":
      case "alertInterval":
        unit = " min";
        break;
      case "smoothing":
      case "historySize":
        unit = " samples";
        break;
      case "readInterval":
        unit = "ms";
        break;
      case "logInterval":
        unit = " sec";
        break;
      // alertVolume removed - ESP32 buzzer handles audio alerts
      case "dayVolume":
      case "nightVolume":
      case "feverVolume":
      case "compensationSensitivity":
        unit = "%";
        break;
    }

    valueElement.textContent = displayValue + unit;
  }

  /**
   * Toggle sensitivity container visibility based on dynamic compensation setting
   */
  toggleSensitivityContainer(enabled) {
    const container = document.getElementById("sensitivityContainer");
    if (container) {
      container.style.display = enabled ? "block" : "none";
    }
  }

  /**
   * Load settings from server
   * @returns {Promise} Promise that resolves when settings are loaded
   */
  async loadFromServer() {
    try {
      const response = await fetch("/get-settings");

      if (!response.ok) {
        throw new Error("Server error: " + response.status);
      }

      const serverSettings = await response.json();
      console.log("Loaded settings from server:", serverSettings);

      // Update settings with server values, keeping defaults for missing values
      this.updateSettings(serverSettings);

      // Store currentAmbientTemp in local settings for calibration access
      if (serverSettings.currentAmbientTemp !== undefined) {
        this.settings.currentAmbientTemp = serverSettings.currentAmbientTemp;
      }

      // Update DOM to reflect loaded settings
      this.initializeFromDOM();

      // Update current ambient temperature display
      this.updateCurrentAmbientDisplay(serverSettings);

      // Update auto-calibration ambient temperature display
      this.updateAutoCalibAmbientDisplay(serverSettings);

      return { success: true, settings: serverSettings };
    } catch (error) {
      console.error("Load settings error:", error);
      // Continue with defaults if loading fails
      return { success: false, error: error.message };
    }
  }

  /**
   * Save settings to server
   * @returns {Promise} Promise that resolves when settings are saved
   */
  async saveToServer() {
    try {
      // Collect current settings from form elements before saving
      this.collectFromDOM();

      const response = await fetch("/save-settings", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(this.settings),
      });

      if (!response.ok) {
        throw new Error("Server error: " + response.status);
      }

      const data = await response.text();

      // Reinitialize DOM to reflect saved settings
      this.initializeFromDOM();

      return { success: true, message: data };
    } catch (error) {
      console.error("Save settings error:", error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Collect current settings from DOM elements
   */
  collectFromDOM() {
    Object.keys(this.settings).forEach((key) => {
      const element = document.getElementById(key);
      if (element) {
        if (element.type === "range") {
          this.settings[key] = parseFloat(element.value);
        } else if (element.type === "checkbox") {
          this.settings[key] = element.checked;
        }
      }
    });
  }

  /**
   * Export settings as JSON
   * @returns {string} JSON string of current settings
   */
  exportSettings() {
    return JSON.stringify(this.settings, null, 2);
  }

  /**
   * Import settings from JSON
   * @param {string} jsonString - JSON string containing settings
   * @returns {boolean} Success status
   */
  importSettings(jsonString) {
    try {
      const importedSettings = JSON.parse(jsonString);
      this.updateSettings(importedSettings);
      this.initializeFromDOM();
      return true;
    } catch (error) {
      console.error("Import settings error:", error);
      return false;
    }
  }

  /**
   * Register callback for settings changes
   * @param {Function} callback - Function to call when settings change
   */
  onSettingsChange(callback) {
    this.settingsCallbacks.add(callback);
  }

  /**
   * Unregister settings change callback
   * @param {Function} callback - Function to remove
   */
  offSettingsChange(callback) {
    this.settingsCallbacks.delete(callback);
  }

  /**
   * Notify all registered callbacks of settings changes
   * @param {string|Array} keys - Changed setting key(s)
   * @param {*} values - New value(s)
   */
  notifySettingsChange(keys, values) {
    this.settingsCallbacks.forEach((callback) => {
      try {
        callback(keys, values, this.settings);
      } catch (error) {
        console.error("Settings callback error:", error);
      }
    });
  }

  /**
   * Update current ambient temperature display
   * @param {Object} serverSettings - Settings from server including currentAmbientTemp
   */
  updateCurrentAmbientDisplay(serverSettings) {
    const currentAmbientDisplay = document.getElementById(
      "currentAmbientDisplay"
    );
    const ambientSource = document.getElementById("ambientSource");

    if (
      currentAmbientDisplay &&
      ambientSource &&
      serverSettings.currentAmbientTemp !== undefined
    ) {
      currentAmbientDisplay.textContent =
        serverSettings.currentAmbientTemp.toFixed(1) + "°F";

      // Determine source of ambient temperature
      if (serverSettings.currentAmbientTemp !== serverSettings.ambientTemp) {
        ambientSource.textContent = "(from AHT10 sensor)";
        ambientSource.className = "text-success";
      } else {
        ambientSource.textContent = "(from manual setting)";
        ambientSource.className = "text-muted";
      }
    }
  }

  /**
   * Update auto-calibration ambient temperature display
   * @param {Object} serverSettings - Settings from server including currentAmbientTemp
   */
  updateAutoCalibAmbientDisplay(serverSettings) {
    const autoCalibAmbientTemp = document.getElementById(
      "autoCalibAmbientTemp"
    );
    const autoCalibAmbientSource = document.getElementById(
      "autoCalibAmbientSource"
    );

    if (
      autoCalibAmbientTemp &&
      serverSettings.currentAmbientTemp !== undefined
    ) {
      autoCalibAmbientTemp.textContent =
        serverSettings.currentAmbientTemp.toFixed(1) + "°F";

      if (autoCalibAmbientSource) {
        // Always show AHT10 since it's mandatory now
        autoCalibAmbientSource.textContent = "AHT10 Sensor";
        autoCalibAmbientSource.className = "badge bg-success";
      }
    }
  }
}

// Create and export singleton instance
const configManager = new ConfigManager();

// Export for use in other modules
window.FeverMonitor = window.FeverMonitor || {};
window.FeverMonitor.ConfigManager = configManager;
