/**
 * Calibration Module
 * Handles auto calibration functionality for the thermal sensor
 */

class CalibrationManager {
  constructor() {
    this.isCalibrating = false;
  }

  /**
   * Initialize calibration module
   */
  initialize() {
    console.log("📐 Calibration Manager initialized");
    this.setupEventListeners();
  }

  /**
   * Setup event listeners for auto calibration inputs
   */
  setupEventListeners() {
    // Add event listeners for auto calibration inputs
    const childBodyTempInput = document.getElementById("childBodyTemp");

    if (childBodyTempInput) {
      childBodyTempInput.addEventListener("input", () => {
        this.previewCalculations();
      });

      // Initial calculation on page load
      setTimeout(() => {
        this.previewCalculations();
      }, 1000);
    }

    // Add event listener for auto calibration tab shown event
    const autoCalibrationTab = document.getElementById("auto-calibrate-tab");
    if (autoCalibrationTab) {
      autoCalibrationTab.addEventListener("shown.bs.tab", () => {
        this.refreshCalibrationSettings();
      });
    }
  }

  /**
   * Refresh calibration settings when tab is shown
   */
  async refreshCalibrationSettings() {
    try {
      console.log("🔄 Refreshing calibration settings...");

      // Reload settings from server to ensure we have the latest values
      const result = await window.FeverMonitor.ConfigManager.loadFromServer();
      if (result.success) {
        console.log("✅ Settings refreshed from server");

        // Force update of DOM elements with current settings
        window.FeverMonitor.ConfigManager.initializeFromDOM();

        // Ambient temperature is now automatically displayed from AHT10 sensor

        console.log("✅ Calibration tab settings updated");
      } else {
        console.warn("⚠️ Failed to refresh settings:", result.error);
      }
    } catch (error) {
      console.error("❌ Error refreshing calibration settings:", error);
    }
  }

  /**
   * Preview auto calibration calculations
   */
  async previewCalculations() {
    const childBodyTemp = parseFloat(
      document.getElementById("childBodyTemp").value
    );

    if (isNaN(childBodyTemp)) {
      window.FeverMonitor.NotificationManager.showSystemStatus(
        "error",
        "Invalid Input",
        "Please enter a valid child body temperature"
      );
      return;
    }

    try {
      // Send child body temperature to server for calculation preview
      const response = await fetch("/auto-calibrate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          childBodyTemp: childBodyTemp,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP ${response.status}`);
      }

      const result = await response.json();
      console.log("Preview calculation result:", result);

      // Update preview displays
      document.getElementById(
        "calculatedDistanceCompensation"
      ).textContent = `+${result.distanceCompensation.toFixed(1)}°F`;

      window.FeverMonitor.NotificationManager.showSystemStatus(
        "info",
        "Calculations Updated",
        `Preview: Distance compensation +${result.distanceCompensation.toFixed(
          1
        )}°F (thermal reading: ${result.rawThermalReading.toFixed(
          1
        )}°F → actual: ${result.childBodyTemp.toFixed(1)}°F)`
      );
    } catch (error) {
      console.error("Preview calculation failed:", error);
      window.FeverMonitor.NotificationManager.showSystemStatus(
        "error",
        "Preview Failed",
        "Could not calculate preview: " + error.message
      );
    }
  }

  /**
   * Perform auto calibration
   */
  async performAutoCalibration() {
    if (this.isCalibrating) {
      return; // Prevent multiple simultaneous calibrations
    }

    const childBodyTemp = parseFloat(
      document.getElementById("childBodyTemp").value
    );

    if (isNaN(childBodyTemp)) {
      window.FeverMonitor.NotificationManager.showSystemStatus(
        "error",
        "Invalid Input",
        "Please enter a valid child body temperature"
      );
      return;
    }

    try {
      this.isCalibrating = true;

      // Show loading state
      const button = document.getElementById("performAutoCalibration");
      button.innerHTML =
        '<i class="fas fa-spinner fa-spin"></i> Calibrating...';
      button.disabled = true;

      // Send child body temperature to server for calculation
      const response = await fetch("/auto-calibrate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          childBodyTemp: childBodyTemp,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP ${response.status}`);
      }

      const result = await response.json();
      console.log("Auto calibration result:", result);

      // Update local settings with server-calculated values
      const currentSettings = window.FeverMonitor.ConfigManager.getSettings();
      currentSettings.distanceCompensation = result.distanceCompensation;

      window.FeverMonitor.ConfigManager.updateSettings(currentSettings);

      // Force update of DOM elements to reflect new settings immediately
      window.FeverMonitor.ConfigManager.initializeFromDOM();

      // Show success message
      const statusDiv = document.getElementById("calibrationStatus");
      const messageSpan = document.getElementById("calibrationMessage");
      const alertDiv = statusDiv.querySelector(".alert");

      // Change alert style to info (blue) instead of success (green)
      alertDiv.className = "alert alert-info";

      statusDiv.style.display = "block";
      messageSpan.textContent = `Auto calibration calculated! Distance compensation: +${result.distanceCompensation.toFixed(
        1
      )}°F (thermal reading: ${result.rawThermalReading.toFixed(
        1
      )}°F → actual: ${result.childBodyTemp.toFixed(
        1
      )}°F). Click "Save Settings" to apply.`;

      window.FeverMonitor.NotificationManager.showSystemStatus(
        "info",
        "Auto Calibration Calculated",
        "Settings have been calculated and updated on the page. Click 'Save Settings' to apply them to the device."
      );
    } catch (error) {
      console.error("Auto calibration failed:", error);
      window.FeverMonitor.NotificationManager.showSystemStatus(
        "error",
        "Calibration Failed",
        "Could not perform auto calibration: " + error.message
      );
    } finally {
      this.isCalibrating = false;

      // Restore button state
      const button = document.getElementById("performAutoCalibration");
      button.innerHTML =
        '<i class="fas fa-magic"></i> Calculate Auto Calibration';
      button.disabled = false;
    }
  }
}

// Create and export singleton instance
const calibrationManager = new CalibrationManager();

// Export for use in other modules
window.FeverMonitor = window.FeverMonitor || {};
window.FeverMonitor.CalibrationManager = calibrationManager;

// Global functions for HTML onclick handlers
window.previewCalculations = function () {
  calibrationManager.previewCalculations();
};

window.performAutoCalibration = function () {
  calibrationManager.performAutoCalibration();
};
