/**
 * API and Data Fetching Module
 * Handles all server communication and data fetching operations
 */

class APIManager {
  constructor() {
    this.baseURL = "";
    this.isConnected = false;
    this.lastFetchTime = 0;
    this.fetchIntervals = new Map();
    this.retryAttempts = 3;
    this.retryDelay = 1000;

    // API endpoints
    this.endpoints = {
      pixels: "/pixels",
      feverStatus: "/fever-status",
      getSettings: "/get-settings",
      resetBaseline: "/reset-baseline",
      calibrate: "/calibrate",
      saveSettings: "/save-settings",
    };
  }

  /**
   * Fetch thermal pixel data from sensor
   * @returns {Promise<Array>} Array of 64 temperature values
   */
  async fetchPixelData() {
    try {
      console.log("Fetching thermal pixel data...");
      const response = await this.makeRequest(this.endpoints.pixels);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log("Received pixel data:", data.length, "pixels");
      console.log("Sample data:", data.slice(0, 8));

      // Validate pixel data
      if (!window.FeverMonitor.Utils.isValidTemperatureArray(data, 64)) {
        throw new Error(
          `Invalid pixel data: expected 64 values, got ${data.length}`
        );
      }

      this.updateConnectionStatus(true);
      return data;
    } catch (error) {
      console.error("Error fetching pixels:", error);
      this.updateConnectionStatus(false);
      throw error;
    }
  }

  /**
   * Fetch fever detection status
   * @returns {Promise<Object>} Fever status object
   */
  async fetchFeverStatus() {
    try {
      const response = await this.makeRequest(this.endpoints.feverStatus);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const status = await response.json();
      console.log("Fever status received:", status);

      // Validate status data
      if (!this.isValidFeverStatus(status)) {
        throw new Error("Invalid fever status data received");
      }

      this.updateConnectionStatus(true);
      return status;
    } catch (error) {
      console.error("Error fetching fever status:", error);
      this.updateConnectionStatus(false);
      throw error;
    }
  }

  /**
   * Reset baseline temperature
   * @returns {Promise<string>} Server response message
   */
  async resetBaseline() {
    try {
      const response = await this.makeRequest(this.endpoints.resetBaseline, {
        method: "POST",
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const message = await response.text();
      console.log("Baseline reset response:", message);
      return message;
    } catch (error) {
      console.error("Error resetting baseline:", error);
      throw error;
    }
  }

  /**
   * Calibrate sensor
   * @returns {Promise<string>} Calibration response message
   */
  async calibrateSensor() {
    try {
      const response = await this.makeRequest(this.endpoints.calibrate, {
        method: "POST",
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const message = await response.text();
      console.log("Calibration response:", message);
      return message;
    } catch (error) {
      console.error("Error calibrating sensor:", error);
      throw error;
    }
  }

  /**
   * Get current settings from server
   * @returns {Promise<Object>} Settings object
   */
  async getSettings() {
    try {
      const response = await this.makeRequest(this.endpoints.getSettings);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const settings = await response.json();
      console.log("Settings received from server:", settings);
      return settings;
    } catch (error) {
      console.error("Error fetching settings:", error);
      throw error;
    }
  }

  /**
   * Save settings to server
   * @param {Object} settings - Settings object to save
   * @returns {Promise<string>} Save response message
   */
  async saveSettings(settings) {
    try {
      const response = await this.makeRequest(this.endpoints.saveSettings, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(settings),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const message = await response.text();
      console.log("Settings save response:", message);
      return message;
    } catch (error) {
      console.error("Error saving settings:", error);
      throw error;
    }
  }

  /**
   * Make HTTP request with retry logic
   * @param {string} endpoint - API endpoint
   * @param {Object} options - Fetch options
   * @returns {Promise<Response>} Fetch response
   */
  async makeRequest(endpoint, options = {}) {
    const url = this.baseURL + endpoint;
    let lastError;

    for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
      try {
        const response = await fetch(url, {
          timeout: 5000,
          ...options,
        });

        // If successful, return response
        if (response.ok || attempt === this.retryAttempts) {
          return response;
        }

        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      } catch (error) {
        lastError = error;
        console.warn(`Request attempt ${attempt} failed:`, error.message);

        // Wait before retry (except on last attempt)
        if (attempt < this.retryAttempts) {
          await this.delay(this.retryDelay * attempt);
        }
      }
    }

    throw lastError;
  }

  /**
   * Start automatic data fetching
   * @param {Object} intervals - Fetch intervals in milliseconds
   */
  startDataFetching(intervals = {}) {
    const { pixels = 1000, feverStatus = 2000 } = intervals;

    // Stop existing intervals
    this.stopDataFetching();

    // Start pixel data fetching
    const pixelInterval = setInterval(async () => {
      try {
        const pixelData = await this.fetchPixelData();
        this.handlePixelData(pixelData);
      } catch (error) {
        // Error already logged in fetchPixelData
      }
    }, pixels);

    // Start fever status fetching
    const statusInterval = setInterval(async () => {
      try {
        const feverStatus = await this.fetchFeverStatus();
        this.handleFeverStatus(feverStatus);
      } catch (error) {
        // Error already logged in fetchFeverStatus
      }
    }, feverStatus);

    // Store intervals for cleanup
    this.fetchIntervals.set("pixels", pixelInterval);
    this.fetchIntervals.set("feverStatus", statusInterval);

    console.log("Data fetching started with intervals:", {
      pixels,
      feverStatus,
    });
  }

  /**
   * Stop automatic data fetching
   */
  stopDataFetching() {
    this.fetchIntervals.forEach((interval, name) => {
      clearInterval(interval);
      console.log(`Stopped ${name} fetching interval`);
    });
    this.fetchIntervals.clear();
  }

  /**
   * Handle received pixel data
   * @param {Array} pixelData - Array of temperature values
   */
  handlePixelData(pixelData) {
    // Update heatmap if available
    if (window.FeverMonitor.HeatmapManager) {
      window.FeverMonitor.HeatmapManager.updateHeatmap(pixelData);
    }

    // Emit custom event for other modules
    this.emitDataEvent("pixelData", pixelData);
  }

  /**
   * Handle received fever status
   * @param {Object} feverStatus - Fever status object
   */
  handleFeverStatus(feverStatus) {
    // Update UI elements
    this.updateTemperatureDisplays(feverStatus);

    // Update chart if available
    if (window.FeverMonitor.ChartManager && feverStatus.baselineEstablished) {
      const settings = window.FeverMonitor.ConfigManager.getSettings();
      window.FeverMonitor.ChartManager.addDataPoint(
        feverStatus.currentTemp,
        feverStatus.baselineTemp,
        settings.feverThreshold
      );
    }

    // Handle fever alerts
    if (feverStatus.feverDetected) {
      this.handleFeverAlert(feverStatus);
    }

    // Update status display
    this.updateStatusDisplay(feverStatus);

    // Emit custom event for other modules
    this.emitDataEvent("feverStatus", feverStatus);
  }

  /**
   * Update temperature displays in UI
   * @param {Object} feverStatus - Fever status object
   */
  updateTemperatureDisplays(feverStatus) {
    // Update current temperature
    window.FeverMonitor.Utils.animateValue(
      "currentTemp",
      feverStatus.currentTemp.toFixed(1)
    );

    // Update baseline temperature
    window.FeverMonitor.Utils.animateValue(
      "baselineTemp",
      feverStatus.baselineTemp.toFixed(1)
    );

    // Update temperature difference
    const tempDiff = (
      feverStatus.currentTemp - feverStatus.baselineTemp
    ).toFixed(1);
    window.FeverMonitor.Utils.animateValue("tempDiff", tempDiff);

    // Update monitoring duration
    const duration = Math.floor((Date.now() - this.startTime) / 60000);
    const durationElement = document.getElementById("duration");
    if (durationElement) {
      durationElement.textContent = duration;
    }

    // Update active compensation if available
    if (feverStatus.activeCompensation !== undefined) {
      window.FeverMonitor.Utils.animateValue(
        "activeCompensation",
        "+" + feverStatus.activeCompensation.toFixed(1)
      );

      // Also update the compensation display in settings page
      const currentCompensationDisplay = document.getElementById(
        "currentCompensationDisplay"
      );
      if (currentCompensationDisplay) {
        currentCompensationDisplay.textContent =
          "+" + feverStatus.activeCompensation.toFixed(1) + "°F";
      }
    }
  }

  /**
   * Handle fever alert
   * @param {Object} feverStatus - Fever status object
   */
  handleFeverAlert(feverStatus) {
    const settings = window.FeverMonitor.ConfigManager.getSettings();

    if (window.FeverMonitor.NotificationManager) {
      window.FeverMonitor.NotificationManager.showFeverAlert(
        feverStatus,
        settings
      );
    }

    // Update fever alert display
    const feverAlert = document.getElementById("feverAlert");
    const feverDetails = document.getElementById("feverDetails");

    if (feverAlert && feverDetails) {
      feverAlert.style.display = "block";
      feverDetails.innerHTML = `
        <i class="fas fa-thermometer-full"></i> Temperature: ${feverStatus.currentTemp.toFixed(
          1
        )}°F |
        <i class="fas fa-clock"></i> Duration: ${feverStatus.feverDuration} min
      `;
    }
  }

  /**
   * Update status display
   * @param {Object} feverStatus - Fever status object
   */
  updateStatusDisplay(feverStatus) {
    const statusDiv = document.getElementById("statusDisplay");
    const feverAlert = document.getElementById("feverAlert");
    const currentStatus = document.getElementById("currentStatus");

    if (
      !feverStatus.feverDetected &&
      statusDiv &&
      feverAlert &&
      currentStatus
    ) {
      feverAlert.style.display = "none";
      statusDiv.style.display = "block";

      // Dynamic status styling
      statusDiv.className = "status-card ";
      if (feverStatus.status.includes("FEVER")) {
        statusDiv.className += "fever-status";
      } else if (feverStatus.status.includes("Elevated")) {
        statusDiv.className += "elevated-status";
      } else {
        statusDiv.className += "normal-status";
      }

      currentStatus.innerHTML = `<i class="fas fa-heartbeat"></i> ${feverStatus.status}`;
    }
  }

  /**
   * Update connection status indicator
   * @param {boolean} connected - Connection status
   */
  updateConnectionStatus(connected) {
    this.isConnected = connected;
    const statusElement = document.getElementById("connectionStatus");

    if (statusElement) {
      statusElement.innerHTML = connected
        ? '<i class="fas fa-wifi text-success"></i> Connected'
        : '<i class="fas fa-wifi text-danger"></i> Disconnected';
    }
  }

  /**
   * Validate fever status object
   * @param {Object} status - Status object to validate
   * @returns {boolean} True if valid
   */
  isValidFeverStatus(status) {
    return (
      status &&
      typeof status.currentTemp === "number" &&
      typeof status.baselineTemp === "number" &&
      typeof status.feverDetected === "boolean" &&
      typeof status.status === "string"
    );
  }

  /**
   * Emit custom data event
   * @param {string} type - Event type
   * @param {*} data - Event data
   */
  emitDataEvent(type, data) {
    const event = new CustomEvent(`feverMonitor:${type}`, {
      detail: data,
    });
    document.dispatchEvent(event);
  }

  /**
   * Delay utility for retry logic
   * @param {number} ms - Milliseconds to delay
   * @returns {Promise} Promise that resolves after delay
   */
  delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Get API status information
   * @returns {Object} Status information
   */
  getStatus() {
    return {
      connected: this.isConnected,
      baseURL: this.baseURL,
      activeIntervals: Array.from(this.fetchIntervals.keys()),
      lastFetchTime: this.lastFetchTime,
      endpoints: { ...this.endpoints },
    };
  }

  /**
   * Set start time for duration calculations
   * @param {number} timestamp - Start timestamp
   */
  setStartTime(timestamp) {
    this.startTime = timestamp;
  }
}

// Create and export singleton instance
const apiManager = new APIManager();

// Export for use in other modules
window.FeverMonitor = window.FeverMonitor || {};
window.FeverMonitor.APIManager = apiManager;
