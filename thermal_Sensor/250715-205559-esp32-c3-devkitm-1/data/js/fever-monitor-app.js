/**
 * Fever Monitor - Main Application Entry Point
 * Simple initialization and coordination of modular components
 */

class FeverMonitorApp {
  constructor() {
    this.startTime = Date.now();
    this.isInitialized = false;
  }

  /**
   * Initialize the application
   */
  async initialize() {
    try {
      console.log("🌡️ Starting Fever Monitor Application...");

      // Initialize modules in dependency order
      await this.initializeModules();

      // Setup global functions for HTML onclick handlers
      this.setupGlobalHandlers();

      // Start the monitoring system
      this.startMonitoring();

      this.isInitialized = true;
      console.log("✅ Fever Monitor Application ready!");

      // Show ready notification
      window.FeverMonitor.NotificationManager.showSystemStatus(
        "ready",
        "System Ready",
        "Fever monitoring system initialized successfully!"
      );
    } catch (error) {
      console.error("❌ Application initialization failed:", error);
      this.handleError(error);
    }
  }

  /**
   * Initialize all modules
   */
  async initializeModules() {
    console.log("📦 Initializing modules...");

    // Initialize UI constraints first (before loading settings)
    if (!window.FeverMonitor.UIConstraintsManager.initialize()) {
      console.warn(
        "⚠️ UI constraints initialization failed, continuing with hardcoded values"
      );
    }

    // Service worker removed - using simple beep notifications only

    // Load settings from server first, then initialize DOM
    try {
      const result = await window.FeverMonitor.ConfigManager.loadFromServer();
      if (result.success) {
        console.log("✅ Settings loaded from server");
      } else {
        console.warn(
          "⚠️ Failed to load settings from server, using defaults:",
          result.error
        );
        // Initialize configuration from DOM with defaults
        window.FeverMonitor.ConfigManager.initializeFromDOM();
      }
    } catch (error) {
      console.warn("⚠️ Settings load failed, using defaults:", error);
      // Initialize configuration from DOM with defaults
      window.FeverMonitor.ConfigManager.initializeFromDOM();
    }

    // Initialize chart
    if (!window.FeverMonitor.ChartManager.initialize()) {
      throw new Error("Failed to initialize temperature chart");
    }

    // Initialize heatmap (optional - can fail gracefully)
    if (!window.FeverMonitor.HeatmapManager.initialize()) {
      console.warn(
        "⚠️ Heatmap initialization failed, continuing without heatmap"
      );
    }

    // Initialize calibration module
    window.FeverMonitor.CalibrationManager.initialize();

    // Initialize time module (optional - can fail gracefully)
    if (window.FeverMonitor.Time) {
      if (!window.FeverMonitor.Time.init()) {
        console.warn(
          "⚠️ Time module initialization failed, continuing without time features"
        );
      } else {
        console.log("✅ Time module initialized");
      }
    }

    // Set API start time
    window.FeverMonitor.APIManager.setStartTime(this.startTime);

    console.log("✅ Modules initialized");
  }

  /**
   * Setup global functions for HTML onclick handlers
   */
  setupGlobalHandlers() {
    const handlers = new UIHandlers();

    // Make functions globally available for HTML onclick handlers
    window.resetBaseline = handlers.resetBaseline.bind(handlers);
    window.calibrateNow = handlers.calibrateNow.bind(handlers);
    window.testAlert = handlers.testAlert.bind(handlers);
    window.testBackgroundNotification =
      handlers.testBackgroundNotification.bind(handlers);
    window.downloadData = handlers.downloadData.bind(handlers);
    window.saveSettings = handlers.saveSettings.bind(handlers);
    window.resetSettings = handlers.resetSettings.bind(handlers);
    window.exportSettings = handlers.exportSettings.bind(handlers);
    window.importSettings = handlers.importSettings.bind(handlers);
    window.setDisplayBrightness = handlers.setDisplayBrightness.bind(handlers);
  }

  /**
   * Start monitoring system
   */
  startMonitoring() {
    console.log("🔄 Starting data monitoring...");

    const settings = window.FeverMonitor.ConfigManager.getSettings();

    // Start automatic data fetching
    window.FeverMonitor.APIManager.startDataFetching({
      pixels: settings.readInterval,
      feverStatus: 2000,
    });

    // Notification permissions removed - using simple beep only

    // Perform initial data fetch
    window.FeverMonitor.APIManager.fetchFeverStatus().catch((error) => {
      console.warn("Initial data fetch failed:", error);
    });

    // Test heatmap with dummy data after a delay
    setTimeout(() => {
      if (window.FeverMonitor.HeatmapManager.getStatus().initialized) {
        window.FeverMonitor.HeatmapManager.testWithDummyData();
      }
    }, 1000);
  }

  /**
   * Handle initialization errors
   */
  handleError(error) {
    const message = "Application failed to start: " + error.message;

    if (window.FeverMonitor?.NotificationManager) {
      window.FeverMonitor.NotificationManager.showSystemStatus(
        "error",
        "Initialization Failed",
        message
      );
    } else {
      alert(message); // Fallback
    }
  }

  /**
   * Get application status
   */
  getStatus() {
    return {
      initialized: this.isInitialized,
      startTime: this.startTime,
      uptime: Date.now() - this.startTime,
    };
  }
}

/**
 * UI Event Handlers
 * Handles all UI button clicks and form interactions
 */
class UIHandlers {
  async resetBaseline() {
    if (
      !confirm(
        "Are you sure you want to reset the baseline? This will restart temperature learning."
      )
    ) {
      return;
    }

    try {
      await window.FeverMonitor.APIManager.resetBaseline();
      window.FeverMonitor.NotificationManager.showSystemStatus(
        "ready",
        "Baseline Reset",
        "Learning new baseline temperature..."
      );
    } catch (error) {
      window.FeverMonitor.NotificationManager.showSystemStatus(
        "error",
        "Error",
        "Failed to reset baseline: " + error.message
      );
    }
  }

  async calibrateNow() {
    window.FeverMonitor.NotificationManager.showSystemStatus(
      "info",
      "Calibration",
      "Performing sensor calibration..."
    );

    try {
      const message = await window.FeverMonitor.APIManager.calibrateSensor();
      window.FeverMonitor.NotificationManager.showSystemStatus(
        "ready",
        "Calibration Complete",
        message
      );
    } catch (error) {
      window.FeverMonitor.NotificationManager.showSystemStatus(
        "error",
        "Calibration Failed",
        "Failed to calibrate sensor: " + error.message
      );
    }
  }

  testAlert() {
    const settings = window.FeverMonitor.ConfigManager.getSettings();
    window.FeverMonitor.NotificationManager.testAlerts(settings);
  }

  testBackgroundNotification() {
    // Browser audio removed - ESP32 buzzer handles audio alerts
    window.FeverMonitor.NotificationManager.showToast(
      "success",
      "🔊 Visual Notification Test",
      "Visual notification system working! Audio alerts are handled by ESP32 buzzer.",
      {
        icon: "fas fa-check-circle",
      }
    );
  }

  downloadData() {
    const data = {
      timestamp: new Date().toISOString(),
      chartData: JSON.parse(window.FeverMonitor.ChartManager.exportData()),
      settings: window.FeverMonitor.ConfigManager.getSettings(),
      systemInfo: {
        monitoringDuration: Math.floor(
          (Date.now() - window.FeverMonitorApp.startTime) / 60000
        ),
      },
    };

    const filename = `fever-monitor-data-${window.FeverMonitor.Utils.getFilenameTimestamp()}.json`;
    window.FeverMonitor.Utils.downloadData(data, filename);

    window.FeverMonitor.NotificationManager.showSystemStatus(
      "ready",
      "Data Downloaded",
      "Temperature monitoring data saved successfully!"
    );
  }

  async saveSettings() {
    try {
      // Save thermal settings
      const thermalResult =
        await window.FeverMonitor.ConfigManager.saveToServer();

      // Save time settings if time module is available
      let timeResult = { success: true };
      if (window.FeverMonitor.Time) {
        timeResult.success = await window.FeverMonitor.Time.saveTimeSettings();
      }

      if (thermalResult.success && timeResult.success) {
        window.FeverMonitor.NotificationManager.showSystemStatus(
          "ready",
          "Settings Saved",
          "Configuration updated successfully!"
        );
      } else {
        throw new Error(thermalResult.error || "Time settings failed to save");
      }
    } catch (error) {
      window.FeverMonitor.NotificationManager.showSystemStatus(
        "error",
        "Save Failed",
        "Could not save settings: " + error.message
      );
    }
  }

  resetSettings() {
    if (confirm("Reset all settings to default values?")) {
      window.FeverMonitor.ConfigManager.resetToDefaults();
      window.FeverMonitor.NotificationManager.showSystemStatus(
        "info",
        "Settings Reset",
        "All settings restored to defaults"
      );
    }
  }

  exportSettings() {
    const settingsData = window.FeverMonitor.ConfigManager.exportSettings();
    const filename = `fever-monitor-settings-${window.FeverMonitor.Utils.getFilenameTimestamp()}.json`;
    window.FeverMonitor.Utils.downloadData(settingsData, filename);

    window.FeverMonitor.NotificationManager.showSystemStatus(
      "ready",
      "Settings Exported",
      "Configuration file downloaded"
    );
  }

  importSettings() {
    const input = document.createElement("input");
    input.type = "file";
    input.accept = ".json";
    input.onchange = (e) => {
      const file = e.target.files[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const success = window.FeverMonitor.ConfigManager.importSettings(
            e.target.result
          );
          const status = success ? "ready" : "error";
          const title = success ? "Settings Imported" : "Import Failed";
          const message = success
            ? "Configuration loaded successfully"
            : "Invalid settings file";

          window.FeverMonitor.NotificationManager.showSystemStatus(
            status,
            title,
            message
          );
        };
        reader.readAsText(file);
      }
    };
    input.click();
  }

  async setDisplayBrightness(brightness) {
    try {
      const response = await fetch("/set-display-brightness", {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: `brightness=${brightness}`,
      });

      const result = await response.json();

      if (result.success) {
        console.log(`Display brightness set to: ${brightness}`);
        // Update the settings in memory
        window.FeverMonitor.ConfigManager.setSetting(
          "displayBrightness",
          brightness
        );
      } else {
        console.error("Failed to set display brightness:", result.error);
        window.FeverMonitor.NotificationManager.showSystemStatus(
          "error",
          "Display Error",
          "Failed to set display brightness"
        );
      }
    } catch (error) {
      console.error("Error setting display brightness:", error);
      window.FeverMonitor.NotificationManager.showSystemStatus(
        "error",
        "Connection Error",
        "Could not communicate with device"
      );
    }
  }
}

// Initialize application when DOM is ready
document.addEventListener("DOMContentLoaded", async function () {
  console.log("🚀 DOM ready - Starting Fever Monitor...");

  const app = new FeverMonitorApp();
  await app.initialize();

  // Make app globally available
  window.FeverMonitorApp = app;
});
