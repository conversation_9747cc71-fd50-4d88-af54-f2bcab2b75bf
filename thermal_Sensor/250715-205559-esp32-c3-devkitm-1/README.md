# Child Fever Monitor - Thermal Sensor Project

## 🌡️ Overview

This project creates an intelligent thermal monitoring system designed to detect fever in children from a 4-foot distance. Using an ESP32-C3 microcontroller with an AMG8833 thermal sensor, it provides real-time temperature monitoring through a sophisticated web interface with advanced fever detection algorithms.

## 🎯 Key Features

### Core Functionality

- **Non-contact fever detection** from 4 feet away
- **Real-time thermal imaging** with 8x8 pixel array
- **Intelligent baseline learning** for personalized monitoring
- **Multi-method fever detection** (medical threshold, baseline deviation, sustained elevation)
- **Distance compensation** (****°F for 4-foot positioning)
- **OLED display** for local monitoring with animations
- **Web dashboard** with comprehensive controls

### Advanced Features

- **Adaptive baseline learning** (5-minute initialization)
- **Temperature history tracking** (100-sample circular buffer)
- **Configurable fever thresholds** and alert settings
- **Audio feedback system** with piezoelectric buzzer:
  - Startup melody (custom tune)
  - Learning beeps (2 short beeps when baseline learning starts)
  - Fever alerts (continuous beeping every 2 seconds)
  - Configurable enable/disable through web interface
- **OTA (Over-The-Air) updates** with static IP
- **Serial console logging**
- **NTP time synchronization** with configurable timezone
- **Night mode** with display auto-off (11pm-8am)
- **Animated eye display** during no presence detection
- **Dual I2C bus architecture** for sensor isolation
- **Persistent settings storage** with NVS (Non-Volatile Storage)
- **Modular web interface** with SPIFFS file system

## 🔧 Hardware Requirements

### Components

1. **ESP32-C3 DevKit M-1** - Main microcontroller
2. **AMG8833 Thermal Camera Sensor** - 8x8 thermal array
3. **SSD1306 OLED Display** (128x32) - Local display with animations
4. **AHT10 Temperature & Humidity Sensor** - Ambient conditions monitoring
5. **Piezoelectric Buzzer** (5V Active) - Audio alerts and feedback
6. **Onboard LED** - GPIO 8 built-in LED for visual feedback
7. **Breadboard and jumper wires**
8. **USB-C cable** for programming and power

### Wiring Diagram

```
ESP32-C3 DevKit M-1 Connections:
┌─────────────────────────────────────────────────────────────────┐
│ ESP32-C3        │ AMG8833  │ OLED   │ AHT10   │ Buzzer (2-pin) │
├─────────────────┼──────────┼────────┼─────────┼────────────────┤
│ GPIO 9 (SDA)    │ SDA      │ SDA    │ --      │ --             │
│ GPIO 4 (SCL)    │ SCL      │ SCL    │ --      │ --             │
│ GPIO 6 (SDA2)   │ --       │ --     │ SDA     │ --             │
│ GPIO 5 (SCL2)   │ --       │ --     │ SCL     │ --             │
│ GPIO 7          │ --       │ --     │ --      │ VCC (+)        │
│ GPIO 8          │ --       │ --     │ --      │ -- (LED)       │
│ 3.3V            │ VCC      │ VCC    │ VIN     │ --             │
│ GND             │ GND      │ GND    │ GND     │ GND (-)        │
└─────────────────────────────────────────────────────────────────┘
```

### Additional Hardware Notes

- **Onboard LED**: GPIO 8 is connected to the ESP32-C3's built-in LED (used for animations/status indication)
- **OLED Display**: 128x32 pixels (not the more common 128x64 size)
- **Piezoelectric Buzzer**: 2-pin active buzzer on GPIO 7 for audio feedback
- **Audio Feedback**: Startup melody, learning beeps, and fever alerts
- **Dual I2C Buses**: Primary bus (GPIO 9/4) for AMG8833 and OLED, secondary bus (GPIO 6/5) for AHT10

### I2C Addresses

**Primary I2C Bus (GPIO 9/4):**

- **AMG8833**: 0x69 (default)
- **SSD1306 OLED**: 0x3C or 0x3D (auto-detected)

**Secondary I2C Bus (GPIO 6/5):**

- **AHT10**: 0x38 (default)

### Hardware Pin Summary

| Component   | Pin    | Function       | Notes                        |
| ----------- | ------ | -------------- | ---------------------------- |
| AMG8833 SDA | GPIO 9 | I2C Data       | Primary I2C bus              |
| AMG8833 SCL | GPIO 4 | I2C Clock      | Primary I2C bus              |
| OLED SDA    | GPIO 9 | I2C Data       | Shared with AMG8833          |
| OLED SCL    | GPIO 4 | I2C Clock      | Shared with AMG8833          |
| AHT10 SDA   | GPIO 6 | I2C Data       | Secondary I2C bus            |
| AHT10 SCL   | GPIO 5 | I2C Clock      | Secondary I2C bus            |
| Buzzer      | GPIO 7 | Digital Output | 2-pin active (3.3V→VCC, GND) |
| Onboard LED | GPIO 8 | Digital Output | Built-in LED for animations  |

## 📦 Software Dependencies

### PlatformIO Libraries

```ini
lib_deps =
    https://github.com/adafruit/Adafruit_AMG88xx
    adafruit/Adafruit BusIO
    adafruit/Adafruit SSD1306
    adafruit/Adafruit GFX Library
    adafruit/Adafruit AHTX0
    bblanchon/ArduinoJson
```

### Web Technologies

- **Bootstrap 5.3.0** - UI framework
- **Chart.js** - Temperature trending
- **Heatmap.js 2.0.2** - Thermal visualization
- **Font Awesome 6.4.0** - Icons
- **Google Fonts (Inter)** - Typography
- **SPIFFS** - File system for web assets

## 🚀 Installation & Setup

### 1. Hardware Assembly

1. Connect components according to wiring diagram
2. Ensure proper I2C connections (SDA/SCL)
3. Power the ESP32-C3 via USB-C

### 2. Software Installation

1. Install **PlatformIO** in VS Code
2. Clone/download this project
3. Open project folder in PlatformIO
4. Update WiFi credentials in `src/wifi_manager.cpp`:

   ```cpp
   const char* ssid = "YOUR_WIFI_SSID";
   const char* password = "YOUR_WIFI_PASSWORD";
   ```

   **Note**: The device uses static IP `*************` by default. If this conflicts with your network, modify the IP configuration in `wifi_manager.cpp`.

### 3. Upload & Configuration

1. Connect ESP32-C3 via USB-C
2. Build and upload firmware: `~/.platformio/penv/bin/pio run --target upload`
3. Upload SPIFFS filesystem: `~/.platformio/penv/bin/pio run --target uploadfs`
4. Monitor serial output: `PlatformIO: Serial Monitor`
5. Note the IP address displayed in serial monitor

**Note**: The project uses OTA (Over-The-Air) updates by default. For initial setup, use USB upload, then subsequent updates can be done wirelessly.

### 4. Positioning Setup

1. Mount sensor **4 feet** from child's bed
2. Aim sensor toward sleeping area
3. Ensure clear line of sight
4. Avoid heat sources (lamps, heaters)

## 🌐 Web Interface Guide

### Accessing the Dashboard

1. Open browser and navigate to: `http://[ESP32_IP_ADDRESS]`
2. The dashboard loads automatically with real-time data

### Dashboard Sections

#### 1. Header & Status

- **Connection status** indicator
- **Distance compensation** display (****°F)
- **System status** overview

#### 2. Temperature Statistics

- **Current Body Temperature** - Real-time reading
- **Baseline Temperature** - Learned normal temperature
- **Temperature Difference** - Deviation from baseline
- **Monitoring Duration** - Time since startup

#### 3. Thermal Visualization

- **Live heatmap** - 8x8 thermal image
- **Temperature scale** - Color-coded legend
- **Hotspot marker** - Indicates maximum temperature location

#### 4. Temperature Trend Chart

- **Real-time graphing** of temperature over time
- **Baseline reference line**
- **Fever threshold indicator**
- **50-point history** with timestamps

#### 5. Settings Panel

The web interface provides comprehensive configuration options:

##### Basic Settings

- **Fever Threshold**: Configurable threshold above baseline (default: 2.0°F)
- **Distance Compensation**: Adjustment for 4-foot distance (default: 4.5°F)
- **Ambient Temperature**: Manual or automatic (AHT10) ambient temperature
- **Person Threshold**: Minimum temperature to detect presence
- **Baseline Min**: Minimum temperature to start baseline learning
- **Display Brightness**: OLED brightness control (0-255)
- **Buzzer Enable/Disable**: Audio feedback control

##### Time & Night Mode Settings

- **NTP Server**: Time synchronization server (default: pool.ntp.org)
- **GMT Offset**: Timezone offset in seconds
- **Night Mode**: Enable/disable night mode functionality
- **Night Start/End Times**: Configurable night mode hours
- **Display Off at Night**: Turn off OLED during night hours when no presence detected

**Note**: The current implementation focuses on essential settings. Advanced settings like sustained time, medical thresholds, and alert intervals are handled by the firmware's default values and can be extended as needed.

#### 6. System Controls

- **Reset Baseline**: Restart temperature learning
- **Calibrate Now**: Update ambient temperature
- **Sync Time**: Force NTP time synchronization
- **Set Display Brightness**: Adjust OLED brightness (0-255)
- **Settings Management**: Save, load, and reset configuration settings

**Note**: Data export functionality and test alerts are planned features that can be implemented as needed.

## 🔬 Fever Detection Algorithm

### Multi-Method Detection

The system uses three complementary methods:

#### 1. Medical Threshold (Absolute)

- Triggers when temperature ≥ 100.4°F
- Based on standard medical fever definition
- Immediate detection regardless of baseline

#### 2. Baseline Deviation (Relative)

- Triggers when temperature exceeds baseline + threshold
- Personalized to individual child's normal temperature
- Configurable threshold (default: 2.0°F)

#### 3. Sustained Elevation

- Monitors temperatures ≥ 99°F for extended periods
- Triggers after 10+ minutes of sustained elevation
- Prevents false alarms from temporary spikes

### Baseline Learning Process

1. **5-minute learning period** (150 readings)
2. **Filters out fever readings** (>100°F) during learning
3. **Calculates average** of normal temperatures
4. **Validates range** (95-100°F) for safety
5. **Establishes personalized baseline**

### Distance Compensation

- ******°F compensation** for 4-foot distance
- **Ambient temperature filtering** (readings must exceed ambient + 10°F)
- **Automatic calibration** based on room temperature

## 📊 Data Management

### Temperature History

- **Circular buffer** stores 100 recent readings
- **30-second logging interval** for data points
- **Real-time chart updates** with 50-point display
- **Automatic data rotation** prevents memory overflow

### Settings Persistence

- **Export/Import** configuration as JSON files
- **Reset to defaults** functionality
- **Real-time setting updates** without restart

### Data Export Format

Data export functionality is planned for future implementation. The system currently maintains temperature history in a circular buffer and settings in NVS storage, which can be extended to provide JSON export capabilities.

## 🚨 Alert System

### Notification Types

1. **Audio Alerts** - Buzzer-based notifications (startup, learning, fever)
2. **Visual Alerts** - OLED display status and web dashboard color changes
3. **Serial Console** - Detailed logging for debugging

### Alert Triggers

- **Fever Detection** - Multi-method fever detection algorithm
- **System Status** - Connection issues or sensor errors
- **Baseline Events** - Learning completion or reset
- **Time Events** - Night mode transitions

### Customization Options

- **Buzzer Enable/Disable** - Control audio feedback
- **Night Mode** - Automatic display control during sleep hours
- **Debug Mode** - Enhanced logging for troubleshooting

## 🔧 Troubleshooting

### Common Issues

#### 1. Sensor Not Detected

```
Error: "Could not find a valid AMG88xx sensor"
```

**Solutions:**

- Check I2C wiring (SDA to GPIO 9, SCL to GPIO 4)
- Verify 3.3V power connection
- Test with I2C scanner code
- Check for loose connections
- Ensure AMG8833 is on primary I2C bus (not secondary)

#### 2. OLED Display Issues

```
Error: "SSD1306 allocation failed"
```

**Solutions:**

- System auto-tries addresses 0x3C and 0x3D
- Check I2C sharing with thermal sensor on primary bus (GPIO 9/4)
- Verify OLED power connections
- Try different OLED module
- Ensure OLED is 128x32 resolution (not 128x64)

#### 3. WiFi Connection Problems

```
Status: "Disconnected" in web interface
```

**Solutions:**

- Update WiFi credentials in `wifi_manager.cpp`
- Check network signal strength
- Verify 2.4GHz network (ESP32-C3 doesn't support 5GHz)
- Restart ESP32-C3

#### 4. Inaccurate Temperature Readings

```
Issue: Baseline outside normal range warning
```

**Solutions:**

- Ensure 4-foot distance from child
- Remove heat sources from sensor view
- Recalibrate ambient temperature
- Reset baseline learning
- Check sensor positioning

#### 5. AHT10 Sensor Issues

```
Error: "Failed to initialize AHT10 sensor!"
```

**Solutions:**

- Check secondary I2C wiring (SDA to GPIO 6, SCL to GPIO 5)
- Verify AHT10 power connections (VIN to 3.3V, GND to GND)
- Ensure AHT10 is on secondary I2C bus (not primary)
- Try different AHT10 module
- Check for I2C address conflicts

#### 6. Web Interface Not Loading

```
Issue: Cannot access dashboard
```

**Solutions:**

- Check serial monitor for IP address
- Ensure device and computer on same network
- Try different browser
- Clear browser cache
- Restart ESP32-C3

### Debug Mode

Enable debug mode in Advanced settings for detailed logging:

- Sensor reading timestamps
- Temperature calculation details
- Fever detection logic steps
- Network communication status
- AHT10 sensor readings
- Time synchronization events
- Night mode transitions

### Night Mode Features

The system includes intelligent night mode functionality:

#### Automatic Display Control

- **Time Range**: 11:00 PM to 8:00 AM (configurable)
- **Display Behavior**: OLED turns off during night hours when no presence detected
- **Animation Control**: Eye animations stop during night mode
- **Power Saving**: Reduces power consumption and eliminates light disturbance

#### Time Synchronization

- **NTP Integration**: Automatic time sync with configurable NTP servers
- **Timezone Support**: GMT offset configuration for local time
- **Manual Override**: Ability to manually set time if NTP fails
- **Sync Monitoring**: Status tracking and automatic retry on failures

## 📈 Performance Optimization

### Sensor Reading Optimization

- **100ms minimum interval** protects sensor hardware
- **Automatic orientation correction** for proper display
- **Temperature range calculation** for efficient processing
- **Circular buffer management** prevents memory leaks

### Web Interface Optimization

- **CDN-hosted libraries** for faster loading
- **Efficient data updates** (2-second intervals)
- **Responsive design** for mobile devices
- **Minimal data transfer** with JSON APIs

### Memory Management

- **Fixed-size arrays** prevent dynamic allocation
- **Circular buffers** for history storage
- **Efficient string handling** in web responses
- **Garbage collection** in JavaScript components

## 🔒 Safety Considerations

### Medical Disclaimer

⚠️ **Important**: This device is for monitoring purposes only and should not replace professional medical advice or traditional thermometers for medical decisions.

### Safety Features

- **Multiple detection methods** reduce false positives/negatives
- **Baseline validation** ensures reasonable temperature ranges
- **Sustained elevation detection** prevents alarm fatigue
- **Manual calibration** allows for environmental adjustments

### Best Practices

1. **Regular calibration** with known-good thermometer
2. **Proper positioning** at 4-foot distance
3. **Environmental awareness** (avoid heat sources)
4. **Backup monitoring** with traditional methods
5. **Professional consultation** for persistent fevers

## 🔄 OTA Updates

### Over-The-Air Programming

- **Static IP**: `*************` (no mDNS required)
- **Authentication**: `test` (change in production)
- **Upload method**: `espota` protocol
- **Automatic handling** in main loop

### Update Process

1. Ensure device is connected to WiFi
2. Use PlatformIO OTA upload option
3. Monitor serial output during update
4. Device automatically restarts after update

## 📝 API Reference

### REST Endpoints

#### GET Endpoints

- `GET /` - Main dashboard HTML (served from SPIFFS)
- `GET /debug` - Debug page for troubleshooting
- `GET /pixels` - Raw thermal data array (JSON)
- `GET /fever-status` - Current fever detection status (JSON)
- `GET /ambient-data` - AHT10 temperature and humidity data (JSON)
- `GET /get-settings` - Current configuration settings (JSON)
- `GET /settings-debug` - Settings debug information
- `GET /time-status` - Current time synchronization status (JSON)
- `GET /css/styles.css` - Stylesheet (served from SPIFFS)
- `GET /js/fever-monitor-app.js` - Main JavaScript application
- `GET /js/modules/*.js` - Modular JavaScript components

#### POST Endpoints

- `POST /reset-baseline` - Reset temperature baseline learning
- `POST /save-settings` - Save configuration settings (JSON body)
- `POST /calibrate` - Perform sensor calibration
- `POST /reset-settings` - Reset all settings to defaults
- `POST /set-display-brightness` - Set OLED brightness (0-255)
- `POST /sync-time` - Force NTP time synchronization
- `POST /save-time-settings` - Save time configuration
- `POST /reset-time-settings` - Reset time settings to defaults

### Data Formats

#### Pixels Response

```json
[25.5, 26.1, 25.8, ..., 27.2]  // 64 temperature values in Celsius
```

#### Fever Status Response

```json
{
  "currentTemp": 98.6,
  "baselineTemp": 97.8,
  "feverDetected": false,
  "baselineEstablished": true,
  "status": "Normal",
  "feverDuration": 0
}
```

#### Ambient Data Response

```json
{
  "temperature": 72.5,
  "temperatureF": 72.5,
  "humidity": 45.2,
  "isValid": true,
  "lastUpdate": 1640995200000
}
```

#### Time Status Response

```json
{
  "status": "TIME_SYNCED",
  "currentTime": "2024-01-15T10:30:00",
  "lastSync": 1640995200000,
  "timeSinceSync": 3600,
  "isNightMode": false,
  "nightModeActive": false
}
```

## 🤝 Contributing

### Development Setup

1. Fork the repository
2. Create feature branch
3. Follow existing code style
4. Test thoroughly with hardware
5. Submit pull request

### Code Structure

```
src/
├── thermal_Sensor.ino          # Main Arduino sketch
├── thermal_sensor.cpp/h        # Sensor and fever detection logic
├── web_server.cpp/h           # HTTP server initialization
├── web_handlers.cpp/h         # Web API endpoint handlers
├── wifi_manager.cpp/h         # WiFi connection management
├── oled_display.cpp/h         # Local display and animations
├── aht10_sensor.cpp/h         # AHT10 temperature/humidity sensor
├── time_manager.cpp/h         # NTP time synchronization
├── settings_manager.cpp/h     # Persistent configuration storage
├── buzzer.cpp/h               # Audio feedback system
└── platformio.ini             # Project configuration

data/
├── html/
│   └── index.html             # Main web interface
├── css/
│   └── styles.css             # Stylesheet
└── js/
    ├── fever-monitor-app.js   # Main application
    └── modules/               # Modular JavaScript components
        ├── api.js
        ├── chart.js
        ├── heatmap.js
        ├── notifications.js
        ├── time.js
        └── utils.js
```

## 📄 License

This project is open source. Please ensure proper attribution when using or modifying the code.

## 🆘 Support

For issues, questions, or contributions:

1. Check troubleshooting section
2. Review serial console output
3. Test with minimal configuration
4. Document hardware setup and symptoms
5. Create detailed issue report

## 📸 Screenshots & Visual Guide

### Web Dashboard Overview

The main dashboard provides a comprehensive view of the thermal monitoring system:

```
┌─────────────────────────────────────────────────────────────┐
│                    Child Fever Monitor                      │
│              Distance: 4ft | Compensation: ****°F          │
├─────────────────────────────────────────────────────────────┤
│  🚨 FEVER DETECTED! 🚨                                     │
│  Temperature: 101.2°F | Duration: 15 min                   │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│ │Current Temp │ │Baseline Temp│ │Temp Diff    │ │Duration │ │
│ │   101.2°F   │ │   98.6°F    │ │   +2.6°F    │ │ 45 min  │ │
│ └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
├─────────────────────────────────────────────────────────────┤
│                Temperature Trend Chart                      │
│ 102°F ┌─────────────────────────────────────────────────┐   │
│       │     ╭─╮                                         │   │
│ 100°F │   ╭─╯ ╰─╮                                       │   │
│       │ ╭─╯     ╰─╮                                     │   │
│  98°F │╱         ╰─────────────────────────────────────│   │
│       └─────────────────────────────────────────────────┘   │
├─────────────────────────────────────────────────────────────┤
│ Thermal View          │ Temperature Scale                   │
│ ┌─────────────────┐   │ ┌──┐ 102°F (Red)                   │
│ │ ● ● ● ● ● ● ● ● │   │ │██│                               │
│ │ ● ● ● ● ● ● ● ● │   │ │██│ 100°F                         │
│ │ ● ● ● ◉ ● ● ● ● │   │ │██│                               │
│ │ ● ● ● ● ● ● ● ● │   │ │██│ 98°F                          │
│ │ ● ● ● ● ● ● ● ● │   │ │██│                               │
│ │ ● ● ● ● ● ● ● ● │   │ │██│ 96°F (Blue)                   │
│ │ ● ● ● ● ● ● ● ● │   │ └──┘                               │
│ │ ● ● ● ● ● ● ● ● │   │                                     │
│ └─────────────────┘   │                                     │
│   ◉ = Hotspot (101.2°F)                                    │
└─────────────────────────────────────────────────────────────┘
```

### OLED Display States

#### Normal Operation

```
┌─────────────────┐
│Normal 98.6F     │
│^98.6F           │
│                 │
│ ● ● ● ● ● ● ● ● │
│ ● ● ● ● ● ● ● ● │
│ ● ● ● ◉ ● ● ● ● │
│ ● ● ● ● ● ● ● ● │
└─────────────────┘
```

#### Fever Detected

```
┌─────────────────┐
│FEVER! 101.2F    │
│!101.2F          │
│                 │
│ ● ● ● ● ● ● ● ● │
│ ● ● ● ● ● ● ● ● │
│ ● ● ● ◉ ● ● ● ● │
│ ● ● ● ● ● ● ● ● │
└─────────────────┘
```

#### Learning Baseline

```
┌─────────────────┐
│Learning...      │
│^97.8F           │
│                 │
│ ● ● ● ● ● ● ● ● │
│ ● ● ● ● ● ● ● ● │
│ ● ● ● ◉ ● ● ● ● │
│ ● ● ● ● ● ● ● ● │
└─────────────────┘
```

#### Waiting for Presence (Animated Eyes)

```
┌─────────────────┐
│Waiting...       │
│No presence      │
│                 │
│   ◯   ◯         │
│  ╱ ╲ ╱ ╲        │
│ ╱   ╲   ╲       │
│                 │
└─────────────────┘
```

**Note**: Eyes animate with blinking and scanning movements when no person is detected. During night mode (11pm-8am), display turns off completely to save power and avoid disturbing sleep.

## 🎯 Detailed Usage Instructions

### Initial Setup Workflow

#### Step 1: Hardware Preparation

1. **Assemble components** on breadboard according to wiring diagram
2. **Double-check connections**:
   - AMG8833 VCC → ESP32 3.3V
   - AMG8833 GND → ESP32 GND
   - AMG8833 SDA → ESP32 GPIO 9
   - AMG8833 SCL → ESP32 GPIO 4
   - OLED connections same as AMG8833 (shared I2C bus)
3. **Power on** and verify LED indicators

#### Step 2: Software Configuration

1. **Update WiFi credentials** in `src/wifi_manager.cpp`:
   ```cpp
   const char* ssid = "YourNetworkName";
   const char* password = "YourNetworkPassword";
   ```
2. **Upload firmware** using PlatformIO
3. **Monitor serial output** to confirm successful connection
4. **Access web interface** at `http://*************` (static IP)

#### Step 3: Physical Positioning

1. **Mount sensor** 4 feet from child's sleeping area
2. **Ensure clear line of sight** to child's body
3. **Avoid interference** from:
   - Heat sources (lamps, heaters, electronics)
   - Direct sunlight
   - Air conditioning vents
   - Reflective surfaces
4. **Test positioning** by checking thermal view in web interface

#### Step 4: Baseline Learning

1. **Access web dashboard** at ESP32's IP address
2. **Wait for 5-minute learning period**
3. **Monitor progress** in serial console:
   ```
   Learning baseline... 30/150 readings (Range: 97.2-98.4°F)
   Learning baseline... 60/150 readings (Range: 97.1-98.6°F)
   ...
   Baseline established: 98.2°F (Range: 97.1-98.8°F)
   ```
4. **Verify reasonable baseline** (95-100°F range)

### Daily Operation

#### Monitoring Process

1. **System automatically monitors** every 100ms
2. **OLED displays** current status locally
3. **Web dashboard** provides detailed view
4. **Alerts trigger** when fever detected

#### Understanding Status Indicators

##### Web Dashboard Status

- **🟢 Normal**: Temperature within normal range
- **🟡 Slightly elevated**: 0.5-1.0°F above baseline
- **🟠 Elevated**: 1.0-2.0°F above baseline
- **🔴 FEVER DETECTED**: Above fever threshold

##### OLED Display Symbols

- **^**: Normal temperature reading
- **!**: Fever detected
- **◉**: Hotspot location in thermal grid

#### Alert Response Workflow

1. **Fever alert triggers** → Multiple notification methods activate
2. **Browser notification** appears with temperature details
3. **Sound alert** plays (if enabled)
4. **Dashboard shows** prominent fever warning
5. **Serial console logs** detailed fever information
6. **Take action**:
   - Verify with traditional thermometer
   - Check child's condition
   - Consult healthcare provider if needed

### Advanced Configuration

#### Customizing Fever Detection

1. **Access Settings Panel** in web dashboard
2. **Detection Tab** - Adjust sensitivity:
   - Lower fever threshold (1.0°F) = More sensitive
   - Higher fever threshold (3.0°F) = Less sensitive
   - Shorter sustained time (5 min) = Faster detection
   - Longer sustained time (15 min) = Fewer false alarms

#### Environmental Calibration

1. **Calibration Tab** - Adjust for environment:
   - **Distance Compensation**: Increase if readings seem low
   - **Ambient Temperature**: Set to current room temperature
   - **Baseline Max**: Adjust if child runs naturally warm/cool

#### Alert Customization

1. **Alerts Tab** - Configure notifications:
   - **Browser Notifications**: Requires permission grant
   - **Sound Alerts**: Adjustable volume and frequency
   - **Alert Interval**: How often to repeat fever alerts

#### Performance Tuning

1. **Advanced Tab** - Optimize performance:
   - **Read Interval**: Faster = more responsive, slower = less CPU usage
   - **History Size**: More samples = better trending, more memory usage
   - **Debug Mode**: Enable for troubleshooting

### Data Management Workflow

#### Exporting Monitoring Data

1. **Click "Download Data"** in System Controls
2. **JSON file downloads** with:
   - Complete temperature history
   - Current settings configuration
   - System performance metrics
   - Timestamp information
3. **Use data for**:
   - Medical consultations
   - Pattern analysis
   - System optimization
   - Backup purposes

#### Settings Backup/Restore

1. **Export Settings** → Downloads configuration JSON
2. **Import Settings** → Uploads previously saved configuration
3. **Reset to Defaults** → Restores factory settings
4. **Save Settings** → Persists current configuration to device

### Maintenance Procedures

#### Regular Calibration (Weekly)

1. **Use "Calibrate Now"** button
2. **System updates** ambient temperature baseline
3. **Verify accuracy** with known-good thermometer
4. **Adjust distance compensation** if needed

#### Baseline Reset (When Needed)

1. **Click "Reset Baseline"** when:
   - Child's normal temperature changes (growth, illness recovery)
   - Sensor position changes
   - Environmental conditions change significantly
2. **Wait 5 minutes** for new baseline learning
3. **Monitor learning progress** in serial console

#### System Health Checks

1. **Monitor connection status** indicator
2. **Check temperature ranges** for reasonableness
3. **Verify alert functionality** with "Test Alert"
4. **Review serial console** for error messages

## 🔍 Advanced Troubleshooting

### Diagnostic Procedures

#### Temperature Reading Issues

**Symptom**: Readings seem too high/low

```
Diagnostic Steps:
1. Check distance compensation setting
2. Verify 4-foot positioning
3. Compare with traditional thermometer
4. Check for heat sources in sensor view
5. Recalibrate ambient temperature
```

**Symptom**: Erratic temperature readings

```
Diagnostic Steps:
1. Enable debug mode
2. Check I2C connections
3. Verify power supply stability
4. Look for electromagnetic interference
5. Test with different sensor read interval
```

#### Network Connectivity Issues

**Symptom**: Web dashboard not accessible

```
Diagnostic Steps:
1. Check serial monitor for IP address
2. Ping ESP32 IP from computer
3. Verify WiFi network connectivity
4. Check router firewall settings
5. Try different browser/device
```

**Symptom**: Intermittent disconnections

```
Diagnostic Steps:
1. Check WiFi signal strength
2. Verify router stability
3. Monitor power supply voltage
4. Check for network congestion
5. Consider WiFi channel interference
```

#### Fever Detection Issues

**Symptom**: False fever alerts

```
Diagnostic Steps:
1. Increase fever threshold
2. Enable sustained elevation detection
3. Check baseline learning quality
4. Verify environmental conditions
5. Adjust temperature smoothing
```

**Symptom**: Missed fever detection

```
Diagnostic Steps:
1. Decrease fever threshold
2. Check baseline establishment
3. Verify sensor positioning
4. Compare with medical thermometer
5. Review detection algorithm settings
```

### Performance Optimization

#### Memory Usage Optimization

- **Reduce history size** if memory issues occur
- **Disable debug mode** in production
- **Optimize web update intervals**
- **Monitor serial output** for memory warnings

#### Response Time Optimization

- **Decrease sensor read interval** for faster response
- **Reduce temperature smoothing** for immediate readings
- **Optimize web refresh rates**
- **Use wired connection** when possible

#### Power Consumption Optimization

- **Increase sensor read interval** to reduce power
- **Disable OLED display** if not needed locally
- **Optimize WiFi power management**
- **Use deep sleep** for battery operation (requires code modification)

## 📋 Quick Reference Guides

### Web Interface Quick Actions

| Action                 | Location             | Purpose                       |
| ---------------------- | -------------------- | ----------------------------- |
| View live thermal data | Main dashboard       | Monitor real-time temperature |
| Reset baseline         | System Controls      | Restart temperature learning  |
| Test alerts            | System Controls      | Verify notification system    |
| Download data          | System Controls      | Export monitoring history     |
| Adjust fever threshold | Settings → Detection | Change sensitivity            |
| Calibrate sensor       | System Controls      | Update ambient temperature    |
| Export settings        | Settings Panel       | Backup configuration          |

### Serial Console Commands Reference

| Output                                      | Meaning                            | Action Required         |
| ------------------------------------------- | ---------------------------------- | ----------------------- |
| `Learning baseline... X/150`                | System learning normal temperature | Wait for completion     |
| `Baseline established: XX.X°F`              | Learning complete                  | Normal operation begins |
| `🚨 FEVER DETECTED! 🚨`                     | Fever alert triggered              | Verify with thermometer |
| `Could not find AMG88xx sensor`             | Hardware connection issue          | Check I2C wiring        |
| `WiFi connected! IP: X.X.X.X`               | Network ready                      | Access web dashboard    |
| `⚠️ WARNING: Baseline outside normal range` | Calibration needed                 | Check positioning       |

### Default Settings Reference

| Setting               | Default Value | Range        | Purpose                    |
| --------------------- | ------------- | ------------ | -------------------------- |
| Fever Threshold       | 2.0°F         | Configurable | Baseline deviation trigger |
| Medical Threshold     | 100.4°F       | Fixed        | Absolute fever temperature |
| Distance Compensation | ****°F        | Configurable | 4-foot distance adjustment |
| Sustained Time        | 10 min        | Fixed        | Elevation duration trigger |
| Sensor Read Interval  | 100ms         | Fixed        | Hardware protection        |
| Baseline Duration     | 5 min         | Fixed        | Learning period            |
| Display Brightness    | 255           | 0-255        | OLED brightness control    |
| Night Mode Start      | 11:00 PM      | Configurable | Night mode start time      |
| Night Mode End        | 8:00 AM       | Configurable | Night mode end time        |
| NTP Server            | pool.ntp.org  | Configurable | Time synchronization       |
| Timezone Offset       | 0 seconds     | Configurable | GMT offset for local time  |
| Buzzer Enabled        | true          | true/false   | Audio feedback control     |

### Troubleshooting Quick Fixes

| Problem             | Quick Fix                  | If That Doesn't Work              |
| ------------------- | -------------------------- | --------------------------------- |
| No web access       | Check IP in serial monitor | Restart ESP32, verify WiFi        |
| Inaccurate readings | Click "Calibrate Now"      | Reset baseline, check positioning |
| No fever detection  | Lower fever threshold      | Verify baseline establishment     |
| False alarms        | Increase fever threshold   | Enable sustained elevation        |
| OLED blank          | Check I2C connections      | Try different I2C address         |
| WiFi won't connect  | Update credentials         | Check 2.4GHz network              |

### Temperature Interpretation Guide

| Reading   | Status       | Typical Action              |
| --------- | ------------ | --------------------------- |
| 95-98°F   | Normal/Low   | Check sensor positioning    |
| 98-100°F  | Normal Range | Continue monitoring         |
| 100-101°F | Elevated     | Increased attention         |
| 101-102°F | Low Fever    | Verify with thermometer     |
| 102°F+    | High Fever   | Immediate medical attention |

## 🔗 Additional Resources

### Hardware Specifications

#### ESP32-C3 DevKit M-1

- **CPU**: RISC-V single-core 32-bit, up to 160MHz
- **Memory**: 400KB SRAM, 384KB ROM
- **WiFi**: 802.11 b/g/n (2.4GHz)
- **GPIO**: 22 programmable pins
- **I2C**: Hardware I2C support
- **Power**: 3.3V operation, USB-C powered

#### AMG8833 Thermal Sensor

- **Resolution**: 8x8 pixels (64 temperature points)
- **Temperature Range**: 0°C to 80°C (32°F to 176°F)
- **Accuracy**: ±2.5°C (±4.5°F)
- **Field of View**: 60° viewing angle
- **Interface**: I2C (address 0x69)
- **Update Rate**: 10Hz maximum

#### SSD1306 OLED Display

- **Resolution**: 128x32 pixels
- **Size**: 0.91" diagonal
- **Interface**: I2C (address 0x3C or 0x3D)
- **Colors**: Monochrome (white on black)
- **Power**: 3.3V operation

### Software Architecture

#### File Structure

#### Key Functions

- `initSensor()` - Initialize AMG8833 and I2C (GPIO 9/4)
- `initAHT10()` - Initialize AHT10 on secondary I2C (GPIO 6/5)
- `initTimeManager()` - Initialize NTP time synchronization
- `initBuzzer()` - Initialize audio feedback system
- `readPixels()` - Read thermal data and process fever detection
- `calculateBodyTemperature()` - Apply distance compensation
- `updateBaseline()` - Learn normal temperature patterns
- `checkForFever()` - Multi-method fever detection algorithm
- `handleRoot()` - Serve comprehensive web dashboard
- `displayThermalData()` - Update OLED with current status and animations
- `startWaitingAnimation()` - Eye animation during no presence detection
- `handleBuzzer()` - Manage audio alerts and feedback

### Web Technologies Used

#### Frontend Libraries

- **Bootstrap 5.3.0**: Responsive UI framework
- **Chart.js**: Real-time temperature trending
- **Heatmap.js 2.0.2**: Thermal visualization
- **Font Awesome 6.4.0**: Professional icons
- **Google Fonts (Inter)**: Modern typography

#### Backend APIs

- **WebServer Library**: HTTP request handling
- **SPIFFS**: File system for web assets
- **ArduinoJson**: JSON parsing and generation
- **ArduinoOTA**: Over-the-air updates
- **WiFi Library**: Network connectivity
- **NVS (Non-Volatile Storage)**: Settings persistence

### Medical & Safety Information

#### Fever Temperature Guidelines

- **Normal**: 97-99°F (36.1-37.2°C)
- **Low-grade fever**: 99.1-100.4°F (37.3-38°C)
- **Fever**: 100.4°F+ (38°C+)
- **High fever**: 103°F+ (39.4°C+)
- **Dangerous**: 105°F+ (40.6°C+)

#### Important Medical Disclaimers

⚠️ **This device is for monitoring purposes only**

- Not a replacement for medical-grade thermometers
- Should not be used for medical diagnosis
- Always verify readings with traditional thermometer
- Consult healthcare providers for persistent fevers
- Not suitable for infants under 3 months without medical supervision

#### When to Seek Medical Attention

- Fever above 104°F (40°C)
- Fever lasting more than 3 days
- Signs of dehydration
- Difficulty breathing
- Severe headache or neck stiffness
- Persistent vomiting
- Unusual drowsiness or irritability

### Performance Benchmarks

#### System Response Times

- **Sensor Reading**: 100ms (configurable)
- **Web Dashboard Update**: 2 seconds
- **Fever Detection**: Real-time (within 1 reading cycle)
- **Baseline Learning**: 5 minutes (150 readings)
- **Alert Response**: <1 second

#### Memory Usage

- **Program Storage**: ~1.2MB (ESP32-C3 has 4MB flash)
- **Dynamic Memory**: ~45KB (ESP32-C3 has 400KB SRAM)
- **Temperature History**: 400 bytes (100 float values)
- **Web Interface**: Served from program memory

#### Network Performance

- **Web Page Load**: 2-5 seconds (depending on connection)
- **Real-time Updates**: 2KB/minute data transfer
- **Settings Save**: <1 second response time
- **OTA Updates**: 2-5 minutes (depending on connection)

### Future Enhancement Ideas

#### Hardware Enhancements

- **Battery operation** with deep sleep modes
- **Multiple sensor support** for room coverage
- **External antenna** for better WiFi range
- **SD card logging** for long-term data storage
- **Additional sensors** (motion, light, etc.)

#### Software Enhancements

- **Data export functionality** (JSON, CSV formats)
- **Browser notifications** for web alerts
- **Mobile app** for remote monitoring
- **Cloud integration** for data backup
- **Machine learning** for improved detection
- **Multi-child support** with individual profiles
- **Historical trend analysis** and reporting

#### Integration Possibilities

- **Home automation** system integration
- **MQTT support** for IoT platforms
- **Email/SMS alerts** for remote notifications
- **Medical record integration**
- **Pediatrician dashboard** access

---

**Last Updated**: December 2024
**Version**: 1.0
**Compatibility**: ESP32-C3, AMG8833, SSD1306, AHT10
**Hardware**: ESP32-C3 DevKit M-1 with dual I2C buses
**Author**: Thermal Sensor Project Team
**License**: Open Source

## 📋 Implementation Status

### ✅ Fully Implemented Features

- ✅ AMG8833 thermal sensor integration
- ✅ OLED display with animations (eye animation, status display)
- ✅ AHT10 temperature/humidity sensor on secondary I2C
- ✅ Buzzer audio feedback system (startup, learning, fever alerts)
- ✅ Web interface with modular JavaScript architecture
- ✅ NTP time synchronization with timezone support
- ✅ Night mode with automatic display control
- ✅ Settings persistence with NVS storage
- ✅ OTA (Over-The-Air) updates
- ✅ Fever detection with baseline learning
- ✅ Distance compensation for 4-foot positioning
- ✅ SPIFFS file system for web assets
- ✅ Dual I2C bus architecture

### 🚧 Planned Features

- 🚧 Data export functionality (JSON/CSV)
- 🚧 Browser notifications
- 🚧 Advanced settings UI (sustained time, medical thresholds)
- 🚧 Test alert functionality
- 🚧 Historical data analysis
- 🚧 Mobile app integration
