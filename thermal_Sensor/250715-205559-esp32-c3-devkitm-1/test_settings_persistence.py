#!/usr/bin/env python3
"""
Settings Persistence Test Script

This script tests the settings persistence functionality of the thermal sensor.
It verifies that settings are properly saved and restored across device restarts.

Usage:
    python3 test_settings_persistence.py <device_ip>

Example:
    python3 test_settings_persistence.py *************
"""

import requests
import json
import time
import sys
import argparse

class ThermalSensorTester:
    def __init__(self, device_ip):
        self.device_ip = device_ip
        self.base_url = f"http://{device_ip}"
        
    def get_settings(self):
        """Get current settings from the device."""
        try:
            response = requests.get(f"{self.base_url}/get-settings", timeout=10)
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            print(f"❌ Failed to get settings: {e}")
            return None
    
    def save_settings(self, settings):
        """Save settings to the device."""
        try:
            response = requests.post(
                f"{self.base_url}/save-settings",
                headers={"Content-Type": "application/json"},
                data=json.dumps(settings),
                timeout=10
            )
            response.raise_for_status()
            return True
        except requests.RequestException as e:
            print(f"❌ Failed to save settings: {e}")
            return False
    
    def reset_settings(self):
        """Reset settings to factory defaults."""
        try:
            response = requests.post(f"{self.base_url}/reset-settings", timeout=10)
            response.raise_for_status()
            return True
        except requests.RequestException as e:
            print(f"❌ Failed to reset settings: {e}")
            return False
    
    def reset_baseline(self):
        """Reset the temperature baseline."""
        try:
            response = requests.post(f"{self.base_url}/reset-baseline", timeout=10)
            response.raise_for_status()
            return True
        except requests.RequestException as e:
            print(f"❌ Failed to reset baseline: {e}")
            return False
    
    def test_basic_persistence(self):
        """Test basic settings persistence."""
        print("🧪 Testing basic settings persistence...")
        
        # Get initial settings
        initial_settings = self.get_settings()
        if not initial_settings:
            return False
        
        print(f"📊 Initial settings: {json.dumps(initial_settings, indent=2)}")
        
        # Modify settings
        test_settings = {
            "feverThreshold": 2.5,
            "distanceComp": 5.0,
            "ambientTemp": 75.0
        }
        
        print(f"💾 Saving test settings: {json.dumps(test_settings, indent=2)}")
        if not self.save_settings(test_settings):
            return False
        
        # Verify settings were applied
        time.sleep(2)
        current_settings = self.get_settings()
        if not current_settings:
            return False
        
        # Check if settings match
        for key, value in test_settings.items():
            if abs(current_settings.get(key, 0) - value) > 0.1:
                print(f"❌ Setting {key} not applied correctly. Expected: {value}, Got: {current_settings.get(key)}")
                return False
        
        print("✅ Settings successfully applied and verified")
        return True
    
    def test_factory_reset(self):
        """Test factory reset functionality."""
        print("🧪 Testing factory reset...")
        
        # Set some custom settings first
        custom_settings = {
            "feverThreshold": 3.0,
            "distanceComp": 6.0,
            "ambientTemp": 80.0
        }
        
        print("💾 Setting custom values before reset...")
        if not self.save_settings(custom_settings):
            return False
        
        time.sleep(2)
        
        # Perform factory reset
        print("🔄 Performing factory reset...")
        if not self.reset_settings():
            return False
        
        time.sleep(3)
        
        # Verify settings are back to defaults
        settings = self.get_settings()
        if not settings:
            return False
        
        # Check default values
        expected_defaults = {
            "feverThreshold": 2.0,
            "distanceComp": 4.5,
            "ambientTemp": 70.0  # May vary based on calibration
        }
        
        print(f"📊 Settings after reset: {json.dumps(settings, indent=2)}")
        
        # Verify fever threshold and distance compensation are reset
        if abs(settings.get("feverThreshold", 0) - expected_defaults["feverThreshold"]) > 0.1:
            print(f"❌ Fever threshold not reset. Expected: {expected_defaults['feverThreshold']}, Got: {settings.get('feverThreshold')}")
            return False
        
        if abs(settings.get("distanceComp", 0) - expected_defaults["distanceComp"]) > 0.1:
            print(f"❌ Distance compensation not reset. Expected: {expected_defaults['distanceComp']}, Got: {settings.get('distanceComp')}")
            return False
        
        print("✅ Factory reset successful")
        return True
    
    def test_baseline_reset(self):
        """Test baseline reset functionality."""
        print("🧪 Testing baseline reset...")
        
        # Reset baseline
        print("🔄 Resetting baseline...")
        if not self.reset_baseline():
            return False
        
        time.sleep(2)
        
        print("✅ Baseline reset completed")
        return True
    
    def run_all_tests(self):
        """Run all persistence tests."""
        print(f"🚀 Starting settings persistence tests for device at {self.device_ip}")
        print("=" * 60)
        
        tests = [
            ("Basic Persistence", self.test_basic_persistence),
            ("Baseline Reset", self.test_baseline_reset),
            ("Factory Reset", self.test_factory_reset),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n📋 Running test: {test_name}")
            print("-" * 40)
            
            try:
                if test_func():
                    print(f"✅ {test_name} PASSED")
                    passed += 1
                else:
                    print(f"❌ {test_name} FAILED")
            except Exception as e:
                print(f"❌ {test_name} FAILED with exception: {e}")
            
            time.sleep(1)
        
        print("\n" + "=" * 60)
        print(f"📊 Test Results: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All tests passed! Settings persistence is working correctly.")
            return True
        else:
            print("⚠️  Some tests failed. Please check the device and try again.")
            return False

def main():
    parser = argparse.ArgumentParser(description="Test thermal sensor settings persistence")
    parser.add_argument("device_ip", help="IP address of the thermal sensor device")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose output")
    
    args = parser.parse_args()
    
    if args.verbose:
        print("🔍 Verbose mode enabled")
    
    tester = ThermalSensorTester(args.device_ip)
    
    try:
        success = tester.run_all_tests()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️  Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
