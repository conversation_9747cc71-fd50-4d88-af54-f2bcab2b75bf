#!/bin/bash

# Connect to thermal sensor Android device
echo "Connecting to Android device at *************:46673..."
$HOME/Android/Sdk/platform-tools/adb connect *************:46673

# Check if connection was successful
if $HOME/Android/Sdk/platform-tools/adb devices | grep -q "*************:46673"; then
    echo "✅ Successfully connected to Android device"
    $HOME/Android/Sdk/platform-tools/adb devices
else
    echo "❌ Failed to connect to Android device"
    echo "Make sure:"
    echo "1. Device is on the same WiFi network"
    echo "2. Wireless debugging is enabled on the device"
    echo "3. Device IP hasn't changed"
fi
