# 🚀 BUILD INSTRUCTIONS

# ⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️

# 🔥🔥🔥 CRITICAL: P<PERSON><PERSON><PERSON><PERSON><PERSON> PATH REQUIREMENTS 🔥🔥🔥

# ⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️

## 🔥🔥🔥 PIO COMMANDS MUST BE RUN THROUGH FULL /BIN PATH 🔥🔥🔥

### ❌ NEVER USE: `pio` or `platformio` directly

### ✅ ALWAYS USE: `~/.platformio/penv/bin/pio`

### 🚫 DO NOT ADD TO PATH - USE FULL PATH EVERY TIME! 🚫

# ⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️⚠️

---

## 📋 Prerequisites

1. **PlatformIO Core** installed
2. **ESP32-C3 DevKit** connected via USB or available for OTA
3. **WiFi credentials** configured in the device

---

## 🛠️ Build Commands

### **Method 1: Using Full PlatformIO Path (RECOMMENDED)**

```bash
# Navigate to project directory
cd thermal_Sensor/250715-205559-esp32-c3-devkitm-1

# Build the project
~/.platformio/penv/bin/pio run

# Upload firmware only
~/.platformio/penv/bin/pio run --target upload

# Upload SPIFFS filesystem only
~/.platformio/penv/bin/pio run --target uploadfs

# Clean build
~/.platformio/penv/bin/pio run --target clean
```

### **Method 2: Using Python Module**

```bash
# If PlatformIO is installed as Python module
python3 -m platformio run
python3 -m platformio run --target upload
python3 -m platformio run --target uploadfs
```

---

## 🚀 Deployment Process

### **Complete Build & Deploy (Firmware + Web Files)**

```bash
# 1. Build everything
~/.platformio/penv/bin/pio run

# 2. Upload firmware (this will auto-upload SPIFFS after firmware)
~/.platformio/penv/bin/pio run --target upload
```

**Note:** The project includes an automatic post-upload script that uploads SPIFFS after firmware upload.

### **Manual SPIFFS Upload (if needed)**

```bash
# Upload web interface files to ESP32 filesystem
~/.platformio/penv/bin/pio run --target uploadfs
```

---

## 📡 OTA Configuration

The project is configured for **Over-The-Air (OTA)** updates:

- **Upload Protocol:** `espota`
- **Device Static IP:** `*************` (no mDNS required)
- **Auth Password:** `test`

### **OTA Upload Requirements:**

1. ESP32 must be connected to WiFi
2. Device must be accessible at static IP `*************`
3. Network must allow communication on port 3232 (OTA port)

---

## 🐛 Troubleshooting

### **Common Issues:**

1. **"pio: command not found"**

   - ✅ Use full path: `~/.platformio/penv/bin/pio`

2. **"Upload failed"**

   - Check USB connection
   - Verify device is in bootloader mode
   - Try different USB cable/port

3. **"OTA upload failed"**

   - Ensure device is on same network
   - Check if `esp32c3-ota.local` resolves
   - Verify auth password is correct

4. **"SPIFFS upload failed"**
   - Check if `data/` directory exists
   - Verify files are in correct structure:
     ```
     data/
     ├── css/styles.css
     ├── html/index.html
     └── js/fever-monitor.js
     ```

---

## 📁 Project Structure

```
thermal_Sensor/250715-205559-esp32-c3-devkitm-1/
├── src/                    # Source code
├── data/                   # Web interface files (SPIFFS)
│   ├── css/styles.css
│   ├── html/index.html
│   └── js/fever-monitor.js
├── platformio.ini          # Build configuration
└── upload_ota_spiff.py     # Auto-upload script
```

---

## 🎯 Quick Start

```bash
# 1. Navigate to project
cd thermal_Sensor/250715-205559-esp32-c3-devkitm-1

# 2. Build and deploy everything
~/.platformio/penv/bin/pio run --target upload

# 3. Monitor serial output
~/.platformio/penv/bin/pio device monitor
```

---

## 📝 Notes

- **Automatic SPIFFS Upload:** Enabled via `upload_ota_spiff.py`
- **USB Mode:** Enabled for debugging
- **Monitor Speed:** 115200 baud
- **Target Board:** ESP32-C3-DevKitM-1

---

## 🆘 Need Help?

1. Check serial monitor for error messages
2. Verify all connections
3. Ensure PlatformIO path is correct
4. Try manual SPIFFS upload if auto-upload fails

**Remember: ALWAYS use the full PlatformIO path!** 🔥
