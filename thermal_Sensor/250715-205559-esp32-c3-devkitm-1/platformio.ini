; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:esp32-c3-devkitm-1]
platform = espressif32
board = esp32-c3-devkitm-1
framework = arduino
monitor_speed = 115200
; upload via usb
; upload_protocol = esptool
; upload via ota
upload_protocol = espota
upload_port = ************* ; static IP address (no mDNS needed)
upload_flags = --auth=test
build_flags = 
	-D ARDUINO_USB_MODE=1        # Enable USB mode
	-D ARDUINO_USB_CDC_ON_BOOT=1 # Enable USB CDC on boot

; SPIFFS configuration
board_build.filesystem = spiffs
board_build.partitions = partitions_custom.csv

; Pre-upload script: automatically upload SPIFFS before firmware
extra_scripts = post:upload_ota_spiff.py

; Library dependencies for thermal sensor project
lib_deps =
    https://github.com/adafruit/Adafruit_AMG88xx
    adafruit/Adafruit BusIO
    adafruit/Adafruit SSD1306
    adafruit/Adafruit GFX Library
    bblanchon/ArduinoJson
    adafruit/Adafruit AHTX0
