# Settings Persistence System

This document describes the new settings persistence system that allows your thermal sensor configuration to survive device restarts.

## Overview

The thermal sensor now automatically saves and restores the following settings across power cycles:

### Persistent Settings

- **Fever Threshold** (default: 2.0°F) - Temperature difference above baseline to trigger fever detection
- **Distance Compensation** (default: 4.5°F) - Compensation factor for 4-foot sensor distance
- **Ambient Temperature** (default: 70.0°F) - Room temperature baseline
- **Baseline Temperature** - Learned normal body temperature for the child
- **Baseline Established** - Whether the baseline learning process has completed
- **Medical Threshold** (default: 100.4°F) - Absolute fever temperature threshold
- **Sustained Time** (default: 10 minutes) - Duration for sustained fever detection
- **Debug Mode** (default: disabled) - Enable/disable debug output

## How It Works

### Automatic Saving

Settings are automatically saved to the ESP32's non-volatile flash memory in these situations:

1. **When settings are changed via web interface** - Any configuration changes made through the web dashboard are immediately saved
2. **When baseline is established** - Once the system learns the child's normal temperature, it's automatically saved
3. **When baseline is reset** - Baseline reset operations are persisted
4. **When sensor calibration is performed** - Calibration results are automatically saved

### Automatic Loading

Settings are automatically loaded from flash memory during device startup:

1. **On power-up** - All saved settings are restored before sensor initialization
2. **After restart** - Settings persist through software resets and power cycles
3. **Fallback to defaults** - If no saved settings exist, the system uses built-in defaults

## Web Interface Integration

### New Endpoints

The web interface now includes a new endpoint for factory reset:

- `POST /reset-settings` - Reset all settings to factory defaults

### Enhanced Existing Endpoints

All existing configuration endpoints now automatically persist changes:

- `POST /save-settings` - Now saves to both memory and flash storage
- `POST /reset-baseline` - Now persists baseline reset state
- `POST /calibrate` - Now saves calibration results

## Usage Examples

### Changing Settings via Web Interface

1. Open the web dashboard in your browser
2. Navigate to the settings section
3. Modify any configuration values
4. Click "Save Settings"
5. Settings are immediately saved to flash memory
6. Restart the device to verify settings persist

### Factory Reset

To reset all settings to factory defaults:

```bash
curl -X POST http://your-device-ip/reset-settings
```

Or use the web interface reset button (if implemented in the frontend).

### Monitoring Settings

Check the serial monitor during startup to see loaded settings:

```
Settings manager initialized
Loading settings version 1
Settings loaded successfully
=== Current Settings ===
Fever Threshold: 2.00°F
Distance Compensation: 4.50°F
Ambient Temperature: 72.30°F
Baseline Temperature: 98.60°F
Baseline Established: YES
Medical Threshold: 100.40°F
Sustained Time: 10 minutes
Debug Mode: DISABLED
Version: 1
========================
Settings applied to global variables
```

## Technical Details

### Storage Technology

- **ESP32 Preferences Library** - Uses the ESP32's built-in NVS (Non-Volatile Storage)
- **Namespace**: `thermal_cfg` - Isolated storage namespace for thermal sensor settings
- **Versioning**: Settings include version numbers for future migration support

### Memory Usage

- **Minimal overhead** - Only stores changed values, not entire configuration
- **Efficient storage** - Uses ESP32's optimized NVS system
- **Wear leveling** - ESP32 automatically manages flash wear leveling

### Error Handling

- **Graceful fallbacks** - If settings can't be loaded, defaults are used
- **Validation** - Settings are validated before being applied
- **Recovery** - Factory reset available if settings become corrupted

## Migration Support

The system includes version tracking for future settings migrations:

- **Current version**: 1
- **Automatic migration** - Future versions will automatically migrate old settings
- **Backward compatibility** - Older settings formats will be supported

## Troubleshooting

### Settings Not Persisting

1. Check serial monitor for error messages during startup
2. Verify flash memory is not corrupted
3. Try factory reset: `POST /reset-settings`
4. Check that settings are being saved (look for "Settings saved to flash" messages)

### Unexpected Behavior After Restart

1. Check if baseline was properly saved (look for "Baseline saved to persistent storage")
2. Verify settings in serial monitor output
3. Compare loaded settings with expected values
4. Use factory reset if settings appear corrupted

### Debug Information

Enable debug mode to see detailed settings operations:

1. Set debug mode via web interface
2. Restart device
3. Monitor serial output for detailed settings loading/saving information

## Best Practices

1. **Regular monitoring** - Check serial output occasionally to verify settings persistence
2. **Backup important baselines** - Note down established baseline temperatures
3. **Test after changes** - Restart device after making configuration changes to verify persistence
4. **Use factory reset sparingly** - Only reset when troubleshooting issues

## Future Enhancements

Planned improvements to the settings system:

- **Settings export/import** - JSON-based configuration backup/restore
- **Remote settings management** - API for bulk settings operations
- **Settings history** - Track changes over time
- **Cloud backup** - Optional cloud-based settings backup
