<?xml version="1.0" encoding="utf-8"?>
<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    
    <PreferenceCategory android:title="Background Monitoring">
        
        <SeekBarPreference
            android:key="refresh_interval"
            android:title="Refresh Interval"
            android:summary="How often to check temperature in background"
            android:defaultValue="10"
            android:max="60"
            android:min="5"
            app:showSeekBarValue="true"
            app:updatesContinuously="true" />
            
    </PreferenceCategory>
    
    <PreferenceCategory android:title="Notifications">

        <SwitchPreferenceCompat
            android:key="show_last_updated"
            android:title="Show Last Updated Time"
            android:summary="Display timestamp in notification"
            android:defaultValue="true" />

        <SwitchPreferenceCompat
            android:key="fever_alerts_enabled"
            android:title="Fever Alerts"
            android:summary="Enable high-priority notifications when fever is detected"
            android:defaultValue="true" />

        <SwitchPreferenceCompat
            android:key="fever_alert_sound"
            android:title="Fever Alert Sound"
            android:summary="Play alarm sound for fever alerts"
            android:defaultValue="true"
            android:dependency="fever_alerts_enabled" />

        <SwitchPreferenceCompat
            android:key="fever_alert_vibration"
            android:title="Fever Alert Vibration"
            android:summary="Vibrate device for fever alerts"
            android:defaultValue="true"
            android:dependency="fever_alerts_enabled" />

    </PreferenceCategory>

    <PreferenceCategory android:title="Push Notifications (FCM)">

        <SwitchPreferenceCompat
            android:key="fcm_enabled"
            android:title="Enable Push Notifications"
            android:summary="Enable Firebase Cloud Messaging for remote notifications"
            android:defaultValue="true" />

        <SwitchPreferenceCompat
            android:key="fcm_fever_alerts_enabled"
            android:title="FCM Fever Alerts"
            android:summary="Send push notifications when fever is detected"
            android:defaultValue="true"
            android:dependency="fcm_enabled" />

        <SwitchPreferenceCompat
            android:key="fcm_general_enabled"
            android:title="FCM General Notifications"
            android:summary="Receive general push notifications"
            android:defaultValue="true"
            android:dependency="fcm_enabled" />

        <SwitchPreferenceCompat
            android:key="fcm_sound_enabled"
            android:title="FCM Sound"
            android:summary="Play sound for push notifications"
            android:defaultValue="true"
            android:dependency="fcm_enabled" />

        <SwitchPreferenceCompat
            android:key="fcm_vibration_enabled"
            android:title="FCM Vibration"
            android:summary="Vibrate for push notifications"
            android:defaultValue="true"
            android:dependency="fcm_enabled" />



    </PreferenceCategory>

</PreferenceScreen>
