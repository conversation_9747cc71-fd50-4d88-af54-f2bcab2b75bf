package com.thermalsensor.app;

import android.content.Context;
import android.net.wifi.WifiManager;
import android.util.Log;
import java.io.IOException;
import java.net.InetAddress;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import javax.jmdns.JmDNS;
import javax.jmdns.ServiceEvent;
import javax.jmdns.ServiceInfo;
import javax.jmdns.ServiceListener;

public class MDNSResolver {
    
    private static final String TAG = "MDNSResolver";
    private static final int TIMEOUT_SECONDS = 10;
    
    private Context context;
    private JmDNS jmdns;
    private WifiManager.MulticastLock multicastLock;
    
    public MDNSResolver(Context context) {
        this.context = context;
    }
    
    /**
     * Resolve a .local hostname to IP address
     * @param hostname The hostname to resolve (e.g., "esp32c3-ota.local")
     * @return CompletableFuture that resolves to the IP address or null if not found
     */
    public CompletableFuture<String> resolveHostname(String hostname) {
        CompletableFuture<String> future = new CompletableFuture<>();
        
        new Thread(() -> {
            try {
                Log.d(TAG, "Resolving hostname: " + hostname);
                
                // Acquire multicast lock for mDNS
                acquireMulticastLock();
                
                // Initialize JmDNS
                initJmDNS();
                
                if (jmdns == null) {
                    Log.e(TAG, "Failed to initialize JmDNS");
                    future.complete(null);
                    return;
                }
                
                // Try direct hostname resolution first
                try {
                    InetAddress[] addresses = InetAddress.getAllByName(hostname);
                    if (addresses.length > 0) {
                        String ip = addresses[0].getHostAddress();
                        Log.d(TAG, "Resolved " + hostname + " to " + ip + " via direct lookup");
                        future.complete(ip);
                        return;
                    }
                } catch (Exception e) {
                    Log.d(TAG, "Direct lookup failed, trying mDNS: " + e.getMessage());
                }
                
                // If direct lookup fails, try mDNS service discovery
                String serviceName = hostname.replace(".local", "");
                String serviceType = "_http._tcp.local.";
                
                Log.d(TAG, "Looking for service: " + serviceName + " of type: " + serviceType);
                
                ServiceListener listener = new ServiceListener() {
                    @Override
                    public void serviceAdded(ServiceEvent event) {
                        Log.d(TAG, "Service added: " + event.getInfo());
                        jmdns.requestServiceInfo(event.getType(), event.getName(), 1000);
                    }
                    
                    @Override
                    public void serviceRemoved(ServiceEvent event) {
                        Log.d(TAG, "Service removed: " + event.getInfo());
                    }
                    
                    @Override
                    public void serviceResolved(ServiceEvent event) {
                        ServiceInfo info = event.getInfo();
                        Log.d(TAG, "Service resolved: " + info);
                        
                        if (info != null && info.getInetAddresses().length > 0) {
                            String ip = info.getInetAddresses()[0].getHostAddress();
                            Log.d(TAG, "Resolved " + hostname + " to " + ip + " via mDNS");
                            future.complete(ip);
                        }
                    }
                };
                
                jmdns.addServiceListener(serviceType, listener);
                
                // Wait for resolution or timeout
                try {
                    String result = future.get(TIMEOUT_SECONDS, TimeUnit.SECONDS);
                    Log.d(TAG, "Resolution completed: " + result);
                } catch (Exception e) {
                    Log.e(TAG, "Resolution timeout or error: " + e.getMessage());
                    future.complete(null);
                }
                
            } catch (Exception e) {
                Log.e(TAG, "Error during hostname resolution: " + e.getMessage(), e);
                future.complete(null);
            } finally {
                cleanup();
            }
        }).start();
        
        return future;
    }
    
    private void acquireMulticastLock() {
        try {
            WifiManager wifiManager = (WifiManager) context.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
            if (wifiManager != null) {
                multicastLock = wifiManager.createMulticastLock("mdns_lock");
                multicastLock.setReferenceCounted(true);
                multicastLock.acquire();
                Log.d(TAG, "Multicast lock acquired");
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to acquire multicast lock: " + e.getMessage());
        }
    }
    
    private void initJmDNS() {
        try {
            WifiManager wifiManager = (WifiManager) context.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
            if (wifiManager != null) {
                int ipAddress = wifiManager.getConnectionInfo().getIpAddress();
                InetAddress inetAddress = InetAddress.getByAddress(new byte[]{
                    (byte) (ipAddress & 0xff),
                    (byte) (ipAddress >> 8 & 0xff),
                    (byte) (ipAddress >> 16 & 0xff),
                    (byte) (ipAddress >> 24 & 0xff)
                });
                
                jmdns = JmDNS.create(inetAddress);
                Log.d(TAG, "JmDNS initialized with address: " + inetAddress.getHostAddress());
            }
        } catch (IOException e) {
            Log.e(TAG, "Failed to initialize JmDNS: " + e.getMessage(), e);
        }
    }
    
    private void cleanup() {
        try {
            if (jmdns != null) {
                jmdns.close();
                jmdns = null;
                Log.d(TAG, "JmDNS closed");
            }
            
            if (multicastLock != null && multicastLock.isHeld()) {
                multicastLock.release();
                multicastLock = null;
                Log.d(TAG, "Multicast lock released");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error during cleanup: " + e.getMessage());
        }
    }
}
