package com.thermalsensor.app;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;
import android.app.Application;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class ThermalSensorViewModel extends ViewModel {

    private static final String TAG = "ThermalSensorViewModel";
    private static final long REFRESH_INTERVAL_SECONDS = 5; // Refresh every 5 seconds

    private final MutableLiveData<Double> temperature = new MutableLiveData<>();
    private final MutableLiveData<String> connectionStatus = new MutableLiveData<>();
    private final MutableLiveData<String> errorMessage = new MutableLiveData<>();
    private final MutableLiveData<Boolean> isAutoRefreshing = new MutableLiveData<>();

    private final ExecutorService executor = Executors.newSingleThreadExecutor();
    private ScheduledExecutorService scheduledExecutor = Executors.newSingleThreadScheduledExecutor();
    private final Handler mainHandler = new Handler(Looper.getMainLooper());

    private ThermalSensorService sensorService;
    private String currentAddress;
    private Context context;
    private boolean isConnected = false;

    public ThermalSensorViewModel() {
        connectionStatus.setValue("Disconnected");
        isAutoRefreshing.setValue(false);
        // Note: sensorService will be initialized when context is set
    }

    public void setContext(Context context) {
        this.context = context;
        if (sensorService == null) {
            sensorService = new ThermalSensorService(context);
        }
    }
    
    public LiveData<Double> getTemperature() {
        return temperature;
    }
    
    public LiveData<String> getConnectionStatus() {
        return connectionStatus;
    }
    
    public LiveData<String> getErrorMessage() {
        return errorMessage;
    }

    public LiveData<Boolean> getIsAutoRefreshing() {
        return isAutoRefreshing;
    }
    
    public void connectToDevice(String address) {
        if (sensorService == null) {
            errorMessage.setValue("Service not initialized. Please restart the app.");
            return;
        }

        currentAddress = address;
        connectionStatus.setValue("Connecting...");
        Log.d(TAG, "Connecting to device at: " + address);

        executor.execute(() -> {
            try {
                boolean connected = sensorService.connect(address);
                mainHandler.post(() -> {
                    if (connected) {
                        Log.d(TAG, "Successfully connected to device");
                        isConnected = true;
                        connectionStatus.setValue("Connected");
                        refreshTemperature();
                        startAutoRefresh();
                    } else {
                        Log.e(TAG, "Failed to connect to device");
                        isConnected = false;
                        connectionStatus.setValue("Connection Failed");
                        errorMessage.setValue("Failed to connect to device at " + address + ". Check address and ensure device is on same network.");
                        stopAutoRefresh();
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Exception during connection", e);
                mainHandler.post(() -> {
                    isConnected = false;
                    connectionStatus.setValue("Connection Error");
                    errorMessage.setValue("Error: " + e.getMessage());
                    stopAutoRefresh();
                });
            }
        });
    }
    
    public void refreshTemperature() {
        if (currentAddress == null || !isConnected) {
            errorMessage.setValue("Not connected to any device");
            return;
        }

        executor.execute(() -> {
            try {
                Double temp = sensorService.getTemperature();
                mainHandler.post(() -> {
                    if (temp != null) {
                        temperature.setValue(temp);
                    } else {
                        errorMessage.setValue("Failed to read temperature");
                        // If we can't read temperature, we might have lost connection
                        isConnected = false;
                        connectionStatus.setValue("Connection Lost");
                        stopAutoRefresh();
                    }
                });
            } catch (Exception e) {
                mainHandler.post(() -> {
                    errorMessage.setValue("Error reading temperature: " + e.getMessage());
                    // Connection error, stop auto refresh
                    isConnected = false;
                    connectionStatus.setValue("Connection Error");
                    stopAutoRefresh();
                });
            }
        });
    }

    private void startAutoRefresh() {
        stopAutoRefresh(); // Stop any existing auto refresh
        isAutoRefreshing.setValue(true);

        scheduledExecutor.scheduleWithFixedDelay(() -> {
            if (isConnected && currentAddress != null) {
                refreshTemperature();
            } else {
                stopAutoRefresh();
            }
        }, REFRESH_INTERVAL_SECONDS, REFRESH_INTERVAL_SECONDS, TimeUnit.SECONDS);

        Log.d(TAG, "Auto refresh started with interval: " + REFRESH_INTERVAL_SECONDS + " seconds");
    }

    private void stopAutoRefresh() {
        isAutoRefreshing.setValue(false);
        // Cancel all scheduled tasks
        if (scheduledExecutor != null && !scheduledExecutor.isShutdown()) {
            scheduledExecutor.shutdownNow();
        }
        // Create a new executor for future use
        scheduledExecutor = Executors.newSingleThreadScheduledExecutor();
        Log.d(TAG, "Auto refresh stopped");
    }

    public void disconnect() {
        isConnected = false;
        currentAddress = null;
        connectionStatus.setValue("Disconnected");
        stopAutoRefresh();
        if (sensorService != null) {
            sensorService.disconnect();
        }
    }
    
    @Override
    protected void onCleared() {
        super.onCleared();
        stopAutoRefresh();
        executor.shutdown();
        scheduledExecutor.shutdown();
        if (sensorService != null) {
            sensorService.disconnect();
        }
    }
}
