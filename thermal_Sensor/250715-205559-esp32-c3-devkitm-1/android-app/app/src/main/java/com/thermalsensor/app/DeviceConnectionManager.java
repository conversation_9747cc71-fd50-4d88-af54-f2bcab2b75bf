package com.thermalsensor.app;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

/**
 * Manages device connection preferences and auto-connect functionality
 */
public class DeviceConnectionManager {
    
    private static final String TAG = "DeviceConnectionManager";
    private static final String PREFS_NAME = "device_connection_prefs";
    
    // Preference keys
    private static final String KEY_LAST_DEVICE_ADDRESS = "last_device_address";
    private static final String KEY_LAST_WIFI_SSID = "last_wifi_ssid";
    private static final String KEY_AUTO_CONNECT_ENABLED = "auto_connect_enabled";
    private static final String KEY_LAST_CONNECTION_TIME = "last_connection_time";
    private static final String KEY_CONNECTION_SUCCESS_COUNT = "connection_success_count";
    
    private SharedPreferences prefs;
    private Context context;
    
    public DeviceConnectionManager(Context context) {
        this.context = context.getApplicationContext();
        this.prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
    }
    
    /**
     * Save successful device connection details
     */
    public void saveSuccessfulConnection(String deviceAddress, String wifiSSID) {
        Log.d(TAG, "Saving successful connection - Device: " + deviceAddress + ", WiFi: " + wifiSSID);
        
        SharedPreferences.Editor editor = prefs.edit();
        editor.putString(KEY_LAST_DEVICE_ADDRESS, deviceAddress);
        editor.putString(KEY_LAST_WIFI_SSID, wifiSSID);
        editor.putLong(KEY_LAST_CONNECTION_TIME, System.currentTimeMillis());
        
        // Increment success count
        int successCount = prefs.getInt(KEY_CONNECTION_SUCCESS_COUNT, 0);
        editor.putInt(KEY_CONNECTION_SUCCESS_COUNT, successCount + 1);
        
        editor.apply();
    }
    
    /**
     * Get the last successfully connected device address
     */
    public String getLastDeviceAddress() {
        return prefs.getString(KEY_LAST_DEVICE_ADDRESS, null);
    }
    
    /**
     * Get the WiFi SSID from the last successful connection
     */
    public String getLastWiFiSSID() {
        return prefs.getString(KEY_LAST_WIFI_SSID, null);
    }
    
    /**
     * Check if auto-connect is enabled
     */
    public boolean isAutoConnectEnabled() {
        return prefs.getBoolean(KEY_AUTO_CONNECT_ENABLED, true); // Default to enabled
    }
    
    /**
     * Set auto-connect preference
     */
    public void setAutoConnectEnabled(boolean enabled) {
        Log.d(TAG, "Setting auto-connect enabled: " + enabled);
        prefs.edit().putBoolean(KEY_AUTO_CONNECT_ENABLED, enabled).apply();
    }
    
    /**
     * Get the timestamp of the last successful connection
     */
    public long getLastConnectionTime() {
        return prefs.getLong(KEY_LAST_CONNECTION_TIME, 0);
    }
    
    /**
     * Get the number of successful connections
     */
    public int getConnectionSuccessCount() {
        return prefs.getInt(KEY_CONNECTION_SUCCESS_COUNT, 0);
    }
    
    /**
     * Check if we should attempt auto-connect for the current WiFi SSID
     */
    public boolean shouldAutoConnect(String currentWiFiSSID) {
        if (!isAutoConnectEnabled()) {
            Log.d(TAG, "Auto-connect is disabled");
            return false;
        }
        
        String lastDeviceAddress = getLastDeviceAddress();
        String lastWiFiSSID = getLastWiFiSSID();
        
        if (lastDeviceAddress == null || lastWiFiSSID == null) {
            Log.d(TAG, "No previous connection data available");
            return false;
        }
        
        if (currentWiFiSSID == null) {
            Log.d(TAG, "Current WiFi SSID is null");
            return false;
        }
        
        boolean ssidMatches = lastWiFiSSID.equals(currentWiFiSSID);
        Log.d(TAG, "SSID match check - Last: " + lastWiFiSSID + ", Current: " + currentWiFiSSID + ", Matches: " + ssidMatches);
        
        return ssidMatches;
    }
    
    /**
     * Get auto-connect data if conditions are met
     */
    public AutoConnectData getAutoConnectData(String currentWiFiSSID) {
        if (!shouldAutoConnect(currentWiFiSSID)) {
            return null;
        }
        
        return new AutoConnectData(
            getLastDeviceAddress(),
            getLastWiFiSSID(),
            getLastConnectionTime(),
            getConnectionSuccessCount()
        );
    }
    
    /**
     * Clear all connection data (useful for reset/logout)
     */
    public void clearConnectionData() {
        Log.d(TAG, "Clearing all connection data");
        SharedPreferences.Editor editor = prefs.edit();
        editor.remove(KEY_LAST_DEVICE_ADDRESS);
        editor.remove(KEY_LAST_WIFI_SSID);
        editor.remove(KEY_LAST_CONNECTION_TIME);
        editor.remove(KEY_CONNECTION_SUCCESS_COUNT);
        editor.apply();
    }
    
    /**
     * Check if we have any previous connection data
     */
    public boolean hasPreviousConnectionData() {
        return getLastDeviceAddress() != null && getLastWiFiSSID() != null;
    }
    
    /**
     * Get a summary of the last connection for display purposes
     */
    public String getLastConnectionSummary() {
        String deviceAddress = getLastDeviceAddress();
        String wifiSSID = getLastWiFiSSID();
        long lastTime = getLastConnectionTime();
        
        if (deviceAddress == null || wifiSSID == null) {
            return "No previous connections";
        }
        
        String timeAgo = getTimeAgoString(lastTime);
        return String.format("Last: %s on %s (%s)", deviceAddress, wifiSSID, timeAgo);
    }
    
    private String getTimeAgoString(long timestamp) {
        if (timestamp == 0) {
            return "unknown time";
        }
        
        long now = System.currentTimeMillis();
        long diff = now - timestamp;
        
        if (diff < 60 * 1000) {
            return "just now";
        } else if (diff < 60 * 60 * 1000) {
            return (diff / (60 * 1000)) + " minutes ago";
        } else if (diff < 24 * 60 * 60 * 1000) {
            return (diff / (60 * 60 * 1000)) + " hours ago";
        } else {
            return (diff / (24 * 60 * 60 * 1000)) + " days ago";
        }
    }
    
    /**
     * Data class for auto-connect information
     */
    public static class AutoConnectData {
        public final String deviceAddress;
        public final String wifiSSID;
        public final long lastConnectionTime;
        public final int successCount;
        
        public AutoConnectData(String deviceAddress, String wifiSSID, long lastConnectionTime, int successCount) {
            this.deviceAddress = deviceAddress;
            this.wifiSSID = wifiSSID;
            this.lastConnectionTime = lastConnectionTime;
            this.successCount = successCount;
        }
        
        @Override
        public String toString() {
            return String.format("AutoConnectData{device='%s', wifi='%s', time=%d, count=%d}", 
                deviceAddress, wifiSSID, lastConnectionTime, successCount);
        }
    }
}
