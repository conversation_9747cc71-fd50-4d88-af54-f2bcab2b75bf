package com.thermalsensor.app;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkRequest;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.Build;
import android.util.Log;
import androidx.annotation.NonNull;

/**
 * Monitors WiFi connectivity state and SSID changes
 */
public class WiFiStateMonitor {
    
    private static final String TAG = "WiFiStateMonitor";
    
    private Context context;
    private WiFiStateListener listener;
    private ConnectivityManager connectivityManager;
    private WifiManager wifiManager;
    private NetworkCallback networkCallback;
    private BroadcastReceiver wifiReceiver;
    private boolean isRegistered = false;
    
    public interface WiFiStateListener {
        void onWiFiConnected(String ssid);
        void onWiFiDisconnected();
        void onWiFiSSIDChanged(String newSSID);
    }
    
    public WiFiStateMonitor(Context context, WiFiStateListener listener) {
        this.context = context.getApplicationContext();
        this.listener = listener;
        this.connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        this.wifiManager = (WifiManager) context.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
        
        setupNetworkCallback();
        setupWiFiReceiver();
    }
    
    private void setupNetworkCallback() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            networkCallback = new NetworkCallback();
        }
    }
    
    private void setupWiFiReceiver() {
        wifiReceiver = new BroadcastReceiver() {
            private String lastSSID = null;
            
            @Override
            public void onReceive(Context context, Intent intent) {
                String action = intent.getAction();
                Log.d(TAG, "WiFi broadcast received: " + action);
                
                if (WifiManager.NETWORK_STATE_CHANGED_ACTION.equals(action) ||
                    WifiManager.WIFI_STATE_CHANGED_ACTION.equals(action) ||
                    ConnectivityManager.CONNECTIVITY_ACTION.equals(action)) {
                    
                    handleWiFiStateChange();
                }
            }
            
            private void handleWiFiStateChange() {
                if (isWiFiConnected()) {
                    String currentSSID = getCurrentSSID();
                    Log.d(TAG, "WiFi connected to SSID: " + currentSSID);
                    
                    if (currentSSID != null) {
                        if (lastSSID == null) {
                            // First connection
                            listener.onWiFiConnected(currentSSID);
                        } else if (!currentSSID.equals(lastSSID)) {
                            // SSID changed
                            listener.onWiFiSSIDChanged(currentSSID);
                        }
                        lastSSID = currentSSID;
                    }
                } else {
                    Log.d(TAG, "WiFi disconnected");
                    if (lastSSID != null) {
                        listener.onWiFiDisconnected();
                        lastSSID = null;
                    }
                }
            }
        };
    }
    
    public void startMonitoring() {
        if (isRegistered) {
            Log.d(TAG, "Already monitoring WiFi state");
            return;
        }
        
        Log.d(TAG, "Starting WiFi state monitoring");
        
        // Register network callback for modern Android versions
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N && networkCallback != null) {
            NetworkRequest.Builder builder = new NetworkRequest.Builder()
                    .addTransportType(NetworkCapabilities.TRANSPORT_WIFI);
            connectivityManager.registerNetworkCallback(builder.build(), networkCallback);
        }
        
        // Register broadcast receiver for all Android versions
        IntentFilter filter = new IntentFilter();
        filter.addAction(WifiManager.NETWORK_STATE_CHANGED_ACTION);
        filter.addAction(WifiManager.WIFI_STATE_CHANGED_ACTION);
        filter.addAction(ConnectivityManager.CONNECTIVITY_ACTION);
        
        context.registerReceiver(wifiReceiver, filter);
        isRegistered = true;
        
        // Check initial state
        if (isWiFiConnected()) {
            String ssid = getCurrentSSID();
            if (ssid != null) {
                listener.onWiFiConnected(ssid);
            }
        }
    }
    
    public void stopMonitoring() {
        if (!isRegistered) {
            return;
        }
        
        Log.d(TAG, "Stopping WiFi state monitoring");
        
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N && networkCallback != null) {
                connectivityManager.unregisterNetworkCallback(networkCallback);
            }
            context.unregisterReceiver(wifiReceiver);
        } catch (Exception e) {
            Log.e(TAG, "Error stopping WiFi monitoring", e);
        }
        
        isRegistered = false;
    }
    
    public boolean isWiFiConnected() {
        if (connectivityManager == null) {
            return false;
        }
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Network activeNetwork = connectivityManager.getActiveNetwork();
            if (activeNetwork == null) {
                return false;
            }
            
            NetworkCapabilities capabilities = connectivityManager.getNetworkCapabilities(activeNetwork);
            return capabilities != null && capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI);
        } else {
            // Fallback for older Android versions
            android.net.NetworkInfo wifiInfo = connectivityManager.getNetworkInfo(ConnectivityManager.TYPE_WIFI);
            return wifiInfo != null && wifiInfo.isConnected();
        }
    }
    
    public String getCurrentSSID() {
        if (wifiManager == null || !isWiFiConnected()) {
            return null;
        }
        
        WifiInfo wifiInfo = wifiManager.getConnectionInfo();
        if (wifiInfo == null) {
            return null;
        }
        
        String ssid = wifiInfo.getSSID();
        if (ssid != null) {
            // Remove quotes if present
            ssid = ssid.replace("\"", "");
            // Filter out unknown SSID
            if ("<unknown ssid>".equals(ssid) || "0x".equals(ssid)) {
                return null;
            }
        }
        
        return ssid;
    }
    
    private class NetworkCallback extends ConnectivityManager.NetworkCallback {
        @Override
        public void onAvailable(@NonNull Network network) {
            Log.d(TAG, "Network available: " + network);
            // Handle in broadcast receiver for consistency
        }
        
        @Override
        public void onLost(@NonNull Network network) {
            Log.d(TAG, "Network lost: " + network);
            // Handle in broadcast receiver for consistency
        }
    }
}
