package com.thermalsensor.app;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.media.AudioAttributes;
import android.media.RingtoneManager;
import android.net.Uri;
import android.os.Build;
import android.os.IBinder;
import android.os.VibrationEffect;
import android.os.Vibrator;
import android.util.Log;
import androidx.preference.PreferenceManager;
import androidx.core.app.NotificationCompat;
import com.google.gson.JsonObject;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.concurrent.TimeUnit;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class FeverMonitoringService extends Service {
    
    private static final String TAG = "FeverMonitoringService";
    private static final String CHANNEL_ID = "fever_monitoring_channel";
    private static final String FEVER_ALERT_CHANNEL_ID = "fever_alert_channel";
    private static final int NOTIFICATION_ID = 1;
    private static final int FEVER_ALERT_NOTIFICATION_ID = 2;
    private static final long DEFAULT_MONITORING_INTERVAL_SECONDS = 10; // Default check interval
    
    public static final String ACTION_START_MONITORING = "START_MONITORING";
    public static final String ACTION_STOP_MONITORING = "STOP_MONITORING";
    public static final String EXTRA_DEVICE_ADDRESS = "device_address";
    
    private ThermalSensorService sensorService;
    private ScheduledExecutorService scheduledExecutor;
    private NotificationManager notificationManager;
    private String deviceAddress;
    private boolean isMonitoring = false;
    private SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm:ss", Locale.getDefault());
    private SharedPreferences preferences;
    private Vibrator vibrator;
    private boolean lastFeverState = false; // Track previous fever state to detect changes

    // WiFi monitoring
    private WiFiStateMonitor wifiStateMonitor;
    private DeviceConnectionManager connectionManager;
    private boolean isPaused = false; // Service is paused due to WiFi issues
    private String lastKnownSSID = null;
    
    // Notification actions
    private static final String ACTION_STOP_SERVICE = "STOP_SERVICE";
    
    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "Service created");

        sensorService = new ThermalSensorService(this);
        scheduledExecutor = Executors.newSingleThreadScheduledExecutor();
        notificationManager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
        preferences = PreferenceManager.getDefaultSharedPreferences(this);
        vibrator = (Vibrator) getSystemService(Context.VIBRATOR_SERVICE);

        // Initialize WiFi monitoring
        connectionManager = new DeviceConnectionManager(this);
        wifiStateMonitor = new WiFiStateMonitor(this, new WiFiStateListener());

        createNotificationChannels();
    }
    
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (intent != null) {
            String action = intent.getAction();
            
            if (ACTION_START_MONITORING.equals(action)) {
                deviceAddress = intent.getStringExtra(EXTRA_DEVICE_ADDRESS);
                if (deviceAddress != null) {
                    startMonitoring();
                } else {
                    Log.e(TAG, "No device address provided");
                    stopSelf();
                }
            } else if (ACTION_STOP_MONITORING.equals(action) || ACTION_STOP_SERVICE.equals(action)) {
                stopMonitoring();
                stopSelf();
            } else if ("DISMISS_FEVER_ALERT".equals(action)) {
                dismissFeverAlert();
            }
        }
        
        return START_STICKY; // Restart if killed
    }
    
    @Override
    public IBinder onBind(Intent intent) {
        return null; // We don't provide binding
    }
    
    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "Service destroyed");

        stopMonitoring();

        if (wifiStateMonitor != null) {
            wifiStateMonitor.stopMonitoring();
        }

        if (scheduledExecutor != null) {
            scheduledExecutor.shutdown();
        }

        if (sensorService != null) {
            sensorService.disconnect();
        }
    }
    
    private void createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            // Regular monitoring channel (low priority)
            NotificationChannel monitoringChannel = new NotificationChannel(
                CHANNEL_ID,
                "Fever Monitoring",
                NotificationManager.IMPORTANCE_LOW
            );
            monitoringChannel.setDescription("Continuous fever monitoring notifications");
            monitoringChannel.setShowBadge(false);
            notificationManager.createNotificationChannel(monitoringChannel);

            // Fever alert channel (high priority with sound and vibration)
            NotificationChannel feverAlertChannel = new NotificationChannel(
                FEVER_ALERT_CHANNEL_ID,
                "Fever Alerts",
                NotificationManager.IMPORTANCE_HIGH
            );
            feverAlertChannel.setDescription("High priority fever detection alerts");
            feverAlertChannel.setShowBadge(true);

            // Check user preferences for vibration
            boolean vibrationEnabled = preferences.getBoolean("fever_alert_vibration", true);
            if (vibrationEnabled) {
                feverAlertChannel.enableVibration(true);
                feverAlertChannel.setVibrationPattern(new long[]{0, 1000, 500, 1000, 500, 1000});
            } else {
                feverAlertChannel.enableVibration(false);
            }

            // Check user preferences for sound
            boolean soundEnabled = preferences.getBoolean("fever_alert_sound", true);
            if (soundEnabled) {
                // Set default alarm sound
                Uri alarmSound = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_ALARM);
                if (alarmSound == null) {
                    alarmSound = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION);
                }

                AudioAttributes audioAttributes = new AudioAttributes.Builder()
                    .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
                    .setUsage(AudioAttributes.USAGE_ALARM)
                    .build();
                feverAlertChannel.setSound(alarmSound, audioAttributes);
            } else {
                feverAlertChannel.setSound(null, null);
            }

            notificationManager.createNotificationChannel(feverAlertChannel);
        }
    }
    
    private void startMonitoring() {
        if (isMonitoring) {
            Log.d(TAG, "Already monitoring");
            return;
        }

        Log.d(TAG, "Starting fever monitoring for device: " + deviceAddress);

        // Start WiFi monitoring
        wifiStateMonitor.startMonitoring();
        lastKnownSSID = wifiStateMonitor.getCurrentSSID();
        Log.d(TAG, "Current WiFi SSID: " + lastKnownSSID);

        // Start foreground service with initial notification immediately
        startForeground(NOTIFICATION_ID, createNotification("Connecting...", "Initializing fever monitoring", false));

        // Connect to device in background thread
        scheduledExecutor.execute(() -> {
            try {
                Log.d(TAG, "Attempting to connect to device in background thread");
                boolean connected = sensorService.connect(deviceAddress);

                if (connected) {
                    Log.d(TAG, "Successfully connected to device in service");
                    isMonitoring = true;
                    isPaused = false;

                    // Update notification to show successful connection
                    updateNotification("Connected", "Monitoring fever status...", false);

                    // Schedule periodic monitoring with configurable interval
                    long intervalSeconds = preferences.getInt("refresh_interval", (int) DEFAULT_MONITORING_INTERVAL_SECONDS);
                    Log.d(TAG, "Using monitoring interval: " + intervalSeconds + " seconds");
                    scheduledExecutor.scheduleWithFixedDelay(this::checkFeverStatus,
                        intervalSeconds, intervalSeconds, TimeUnit.SECONDS);
                } else {
                    Log.e(TAG, "Failed to connect to device in service");

                    // Check if we're on the right WiFi network
                    if (lastKnownSSID != null) {
                        updateNotification("⏳ Waiting for Device", "Device unreachable on " + lastKnownSSID, false);
                        isPaused = true;
                        // Don't stop service, keep trying
                        scheduleRetryConnection();
                    } else {
                        updateNotification("Connection Failed", "No WiFi connection", false);
                        scheduledExecutor.schedule(() -> stopSelf(), 3, TimeUnit.SECONDS);
                    }
                }
            } catch (Exception e) {
                Log.e(TAG, "Exception during service connection", e);

                // Check if we're on the right WiFi network
                if (lastKnownSSID != null) {
                    updateNotification("⏳ Waiting for Device", "Connection error on " + lastKnownSSID, false);
                    isPaused = true;
                    scheduleRetryConnection();
                } else {
                    updateNotification("Connection Error", "Error: " + e.getMessage(), false);
                    scheduledExecutor.schedule(() -> stopSelf(), 3, TimeUnit.SECONDS);
                }
            }
        });
    }
    
    private void stopMonitoring() {
        Log.d(TAG, "Stopping fever monitoring");
        isMonitoring = false;
        isPaused = false;

        if (wifiStateMonitor != null) {
            wifiStateMonitor.stopMonitoring();
        }

        if (scheduledExecutor != null) {
            scheduledExecutor.shutdownNow();
            scheduledExecutor = Executors.newSingleThreadScheduledExecutor();
        }

        if (sensorService != null) {
            sensorService.disconnect();
        }
    }
    
    private void checkFeverStatus() {
        // Don't check if service is paused
        if (isPaused) {
            Log.d(TAG, "Service is paused, skipping fever status check");
            return;
        }

        try {
            JsonObject status = sensorService.getStatus();
            if (status != null) {
                double currentTemp = status.has("currentTemp") ? status.get("currentTemp").getAsDouble() : 0.0;
                boolean hasFever = status.has("feverDetected") ? status.get("feverDetected").getAsBoolean() : false;
                String feverStatus = status.has("status") ? status.get("status").getAsString() : "Unknown";

                String title = hasFever ? "🔥 FEVER DETECTED!" : "✅ Normal Temperature";
                String content;

                boolean showLastUpdated = preferences.getBoolean("show_last_updated", true);
                if (showLastUpdated) {
                    String lastRefreshed = timeFormat.format(new Date());
                    content = String.format("%.1f°F - %s\nLast updated: %s", currentTemp, feverStatus, lastRefreshed);
                } else {
                    content = String.format("%.1f°F - %s", currentTemp, feverStatus);
                }

                updateNotification(title, content, hasFever);

                // Check if fever state changed and trigger alert if fever detected
                if (hasFever && !lastFeverState) {
                    // Fever just detected - trigger alert
                    triggerFeverAlert(currentTemp, feverStatus);
                } else if (!hasFever && lastFeverState) {
                    // Fever cleared - dismiss fever alert notification
                    dismissFeverAlert();
                }

                lastFeverState = hasFever;

                Log.d(TAG, String.format("Fever check: %.1f°F, Fever: %s, Status: %s",
                    currentTemp, hasFever, feverStatus));
            } else {
                boolean showLastUpdated = preferences.getBoolean("show_last_updated", true);
                String content = "Unable to read sensor data";
                if (showLastUpdated) {
                    String lastRefreshed = timeFormat.format(new Date());
                    content += "\nLast updated: " + lastRefreshed;
                }
                updateNotification("Connection Error", content, false);
                Log.e(TAG, "Failed to get status from sensor");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error checking fever status", e);
            boolean showLastUpdated = preferences.getBoolean("show_last_updated", true);
            String content = "Error: " + e.getMessage();
            if (showLastUpdated) {
                String lastRefreshed = timeFormat.format(new Date());
                content += "\nLast updated: " + lastRefreshed;
            }
            updateNotification("Monitoring Error", content, false);
        }
    }
    
    private Notification createNotification(String title, String content, boolean isFever) {
        // Create intent to open main activity
        Intent mainIntent = new Intent(this, MainActivity.class);
        PendingIntent mainPendingIntent = PendingIntent.getActivity(this, 0, mainIntent, 
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);
        
        // Create stop action
        Intent stopIntent = new Intent(this, FeverMonitoringService.class);
        stopIntent.setAction(ACTION_STOP_SERVICE);
        PendingIntent stopPendingIntent = PendingIntent.getService(this, 0, stopIntent, 
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);
        
        NotificationCompat.Builder builder = new NotificationCompat.Builder(this, CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentTitle(title)
            .setContentText(content)
            .setContentIntent(mainPendingIntent)
            .setOngoing(true) // Make it non-dismissible
            .setPriority(isFever ? NotificationCompat.PRIORITY_HIGH : NotificationCompat.PRIORITY_LOW)
            .addAction(R.drawable.ic_launcher_foreground, "Stop Monitoring", stopPendingIntent);
        
        if (isFever) {
            builder.setCategory(NotificationCompat.CATEGORY_ALARM)
                   .setVisibility(NotificationCompat.VISIBILITY_PUBLIC);
        }
        
        return builder.build();
    }
    
    private void updateNotification(String title, String content, boolean isFever) {
        Notification notification = createNotification(title, content, isFever);
        notificationManager.notify(NOTIFICATION_ID, notification);
    }

    private void triggerFeverAlert(double temperature, String status) {
        // Check if fever alerts are enabled
        boolean feverAlertsEnabled = preferences.getBoolean("fever_alerts_enabled", true);
        if (!feverAlertsEnabled) {
            Log.d(TAG, "Fever detected but alerts are disabled in settings");
            return;
        }

        Log.w(TAG, "🚨 FEVER ALERT TRIGGERED! Temperature: " + temperature + "°F");

        // Create high-priority fever alert notification
        Intent mainIntent = new Intent(this, MainActivity.class);
        mainIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
        PendingIntent mainPendingIntent = PendingIntent.getActivity(this, 1, mainIntent,
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);

        // Create dismiss action
        Intent dismissIntent = new Intent(this, FeverMonitoringService.class);
        dismissIntent.setAction("DISMISS_FEVER_ALERT");
        PendingIntent dismissPendingIntent = PendingIntent.getService(this, 2, dismissIntent,
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);

        NotificationCompat.Builder builder = new NotificationCompat.Builder(this, FEVER_ALERT_CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentTitle("🚨 FEVER DETECTED!")
            .setContentText(String.format("Temperature: %.1f°F - %s", temperature, status))
            .setStyle(new NotificationCompat.BigTextStyle()
                .bigText(String.format("🔥 FEVER ALERT!\n\nTemperature: %.1f°F\nStatus: %s\n\nPlease check on the monitored person immediately.",
                    temperature, status)))
            .setContentIntent(mainPendingIntent)
            .setPriority(NotificationCompat.PRIORITY_MAX)
            .setCategory(NotificationCompat.CATEGORY_ALARM)
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
            .setAutoCancel(false)
            .setOngoing(false)
            .addAction(R.drawable.ic_launcher_foreground, "Open App", mainPendingIntent)
            .addAction(R.drawable.ic_launcher_foreground, "Dismiss", dismissPendingIntent);

        // Check user preferences for sound and vibration
        boolean soundEnabled = preferences.getBoolean("fever_alert_sound", true);
        boolean vibrationEnabled = preferences.getBoolean("fever_alert_vibration", true);

        // For older Android versions, manually add sound and vibration
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.O) {
            if (soundEnabled) {
                Uri alarmSound = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_ALARM);
                if (alarmSound == null) {
                    alarmSound = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION);
                }
                builder.setSound(alarmSound);
            }
            if (vibrationEnabled) {
                builder.setVibrate(new long[]{0, 1000, 500, 1000, 500, 1000});
            }
        }

        Notification feverNotification = builder.build();
        notificationManager.notify(FEVER_ALERT_NOTIFICATION_ID, feverNotification);

        // Trigger vibration manually for immediate feedback if enabled
        if (vibrationEnabled && vibrator != null && vibrator.hasVibrator()) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                VibrationEffect effect = VibrationEffect.createWaveform(
                    new long[]{0, 1000, 500, 1000, 500, 1000}, -1);
                vibrator.vibrate(effect);
            } else {
                vibrator.vibrate(new long[]{0, 1000, 500, 1000, 500, 1000}, -1);
            }
        }
    }

    private void dismissFeverAlert() {
        Log.i(TAG, "Dismissing fever alert - temperature returned to normal");
        notificationManager.cancel(FEVER_ALERT_NOTIFICATION_ID);
    }
    
    // Static methods to control the service
    public static void startMonitoring(Context context, String deviceAddress) {
        Intent intent = new Intent(context, FeverMonitoringService.class);
        intent.setAction(ACTION_START_MONITORING);
        intent.putExtra(EXTRA_DEVICE_ADDRESS, deviceAddress);
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            context.startForegroundService(intent);
        } else {
            context.startService(intent);
        }
    }
    
    public static void stopMonitoring(Context context) {
        Intent intent = new Intent(context, FeverMonitoringService.class);
        intent.setAction(ACTION_STOP_MONITORING);
        context.startService(intent);
    }

    /**
     * Schedule retry connection attempt
     */
    private void scheduleRetryConnection() {
        Log.d(TAG, "Scheduling retry connection in 30 seconds");
        scheduledExecutor.schedule(() -> {
            if (isPaused && !isMonitoring) {
                Log.d(TAG, "Retrying connection...");
                boolean connected = sensorService.connect(deviceAddress);
                if (connected) {
                    Log.d(TAG, "Retry connection successful");
                    isMonitoring = true;
                    isPaused = false;
                    updateNotification("Connected", "Monitoring fever status...", false);

                    // Resume monitoring
                    long intervalSeconds = preferences.getInt("refresh_interval", (int) DEFAULT_MONITORING_INTERVAL_SECONDS);
                    scheduledExecutor.scheduleWithFixedDelay(this::checkFeverStatus,
                        intervalSeconds, intervalSeconds, TimeUnit.SECONDS);
                } else {
                    Log.d(TAG, "Retry connection failed, scheduling another retry");
                    scheduleRetryConnection();
                }
            }
        }, 30, TimeUnit.SECONDS);
    }

    /**
     * WiFi state listener implementation
     */
    private class WiFiStateListener implements WiFiStateMonitor.WiFiStateListener {
        @Override
        public void onWiFiConnected(String ssid) {
            Log.d(TAG, "Service: WiFi connected to: " + ssid);
            lastKnownSSID = ssid;

            // If we were paused and this is the same SSID we expect, try to reconnect
            if (isPaused && connectionManager.shouldAutoConnect(ssid)) {
                Log.d(TAG, "WiFi reconnected to expected network, attempting to resume monitoring");
                scheduleRetryConnection();
            }
        }

        @Override
        public void onWiFiDisconnected() {
            Log.d(TAG, "Service: WiFi disconnected");
            lastKnownSSID = null;

            if (isMonitoring) {
                Log.d(TAG, "Pausing monitoring due to WiFi disconnection");
                isMonitoring = false;
                isPaused = true;
                updateNotification("⏳ WiFi Disconnected", "Waiting for WiFi connection...", false);
            }
        }

        @Override
        public void onWiFiSSIDChanged(String newSSID) {
            Log.d(TAG, "Service: WiFi SSID changed to: " + newSSID);
            lastKnownSSID = newSSID;

            // Check if we should continue monitoring on this network
            if (connectionManager.shouldAutoConnect(newSSID)) {
                Log.d(TAG, "New WiFi network is compatible, attempting to reconnect");
                if (isPaused) {
                    scheduleRetryConnection();
                }
            } else {
                Log.d(TAG, "New WiFi network is not compatible, stopping service");
                if (isMonitoring || isPaused) {
                    updateNotification("WiFi Changed", "Stopped monitoring - different network", false);
                    scheduledExecutor.schedule(() -> stopSelf(), 3, TimeUnit.SECONDS);
                }
            }
        }
    }
}
