package com.thermalsensor.app;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.widget.TextView;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.ViewModelProvider;

import com.google.firebase.FirebaseApp;
import com.google.firebase.messaging.FirebaseMessaging;
import android.util.Log;

public class MainActivity extends AppCompatActivity implements WiFiStateMonitor.WiFiStateListener {

    private static final String TAG = "MainActivity";

    private ThermalSensorViewModel viewModel;
    private TextView temperatureText;
    private TextView statusText;
    private EditText ipAddressEdit;
    private Button connectButton;
    private Button refreshButton;
    private Button monitoringButton;
    private Button settingsButton;

    private boolean isMonitoring = false;

    // Auto-connect components
    private WiFiStateMonitor wifiStateMonitor;
    private DeviceConnectionManager connectionManager;
    private boolean isAutoConnecting = false;



    private static final int NOTIFICATION_PERMISSION_REQUEST_CODE = 1001;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        FirebaseApp.initializeApp(this);

        // Initialize ViewModel
        viewModel = new ViewModelProvider(this).get(ThermalSensorViewModel.class);
        viewModel.setContext(this);

        // Initialize auto-connect components
        connectionManager = new DeviceConnectionManager(this);
        wifiStateMonitor = new WiFiStateMonitor(this, this);

        // Initialize FCM - bare minimum
        getAndDisplayFcmToken();

        // Initialize views
        initViews();

        // Setup observers
        setupObservers();

        // Setup click listeners
        setupClickListeners();

        // Start WiFi monitoring
        wifiStateMonitor.startMonitoring();

        Log.d(TAG, "MainActivity created, WiFi monitoring started");
    }
    
    private void initViews() {
        temperatureText = findViewById(R.id.temperatureText);
        statusText = findViewById(R.id.statusText);
        ipAddressEdit = findViewById(R.id.ipAddressEdit);
        connectButton = findViewById(R.id.connectButton);
        refreshButton = findViewById(R.id.refreshButton);
        monitoringButton = findViewById(R.id.monitoringButton);
        settingsButton = findViewById(R.id.settingsButton);
        
        // Set default static IP address (no mDNS needed)
        ipAddressEdit.setText("*************"); // ESP32 static IP address
    }
    
    private void setupObservers() {
        viewModel.getTemperature().observe(this, temperature -> {
            if (temperature != null) {
                temperatureText.setText(String.format("%.1f°F", temperature));
            }
        });
        
        viewModel.getConnectionStatus().observe(this, status -> {
            statusText.setText(status);
            connectButton.setEnabled(!status.equals("Connecting..."));

            // Enable monitoring button only when connected
            boolean isConnected = "Connected".equals(status);
            monitoringButton.setEnabled(isConnected && !isMonitoring);

            // Handle connection success/failure for auto-connect
            if ("Connected".equals(status)) {
                String address = ipAddressEdit.getText().toString().trim();
                onConnectionSuccess(address);

                // Auto-start monitoring if this was an auto-connect
                if (isAutoConnecting) {
                    startBackgroundMonitoring();
                }
            } else if ("Connection Failed".equals(status) || "Connection Error".equals(status)) {
                onConnectionFailure();
            }
        });
        
        viewModel.getErrorMessage().observe(this, error -> {
            if (error != null && !error.isEmpty()) {
                Toast.makeText(this, error, Toast.LENGTH_SHORT).show();
            }
        });

        viewModel.getIsAutoRefreshing().observe(this, isRefreshing -> {
            if (isRefreshing != null && isRefreshing) {
                refreshButton.setText("Auto-Refreshing...");
                refreshButton.setEnabled(true); // Still allow manual refresh
            } else {
                refreshButton.setText("Refresh Temperature");
                refreshButton.setEnabled(true);
            }
        });
    }
    
    private void setupClickListeners() {
        connectButton.setOnClickListener(v -> {
            String address = ipAddressEdit.getText().toString().trim();
            if (!address.isEmpty()) {
                connectToDevice(address, false); // Manual connection
            } else {
                Toast.makeText(this, "Please enter hostname or IP address", Toast.LENGTH_SHORT).show();
            }
        });

        refreshButton.setOnClickListener(v -> {
            viewModel.refreshTemperature();
        });

        monitoringButton.setOnClickListener(v -> {
            if (isMonitoring) {
                stopBackgroundMonitoring();
            } else {
                startBackgroundMonitoring();
            }
        });

        settingsButton.setOnClickListener(v -> {
            Intent intent = new Intent(this, SettingsActivity.class);
            startActivity(intent);
        });
    }

    private void startBackgroundMonitoring() {
        String address = ipAddressEdit.getText().toString().trim();
        if (address.isEmpty()) {
            Toast.makeText(this, "Please connect to a device first", Toast.LENGTH_SHORT).show();
            return;
        }

        // Check notification permission for Android 13+ (API 33+)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.POST_NOTIFICATIONS)
                != PackageManager.PERMISSION_GRANTED) {
                // Request notification permission
                ActivityCompat.requestPermissions(this,
                    new String[]{Manifest.permission.POST_NOTIFICATIONS},
                    NOTIFICATION_PERMISSION_REQUEST_CODE);
                return;
            }
        }

        // Start the background monitoring service
        FeverMonitoringService.startMonitoring(this, address);

        isMonitoring = true;
        monitoringButton.setText("Stop Background Monitoring");
        monitoringButton.setEnabled(true);

        Toast.makeText(this, "Background monitoring started", Toast.LENGTH_SHORT).show();
    }

    private void stopBackgroundMonitoring() {
        // Stop the background monitoring service
        FeverMonitoringService.stopMonitoring(this);

        isMonitoring = false;
        monitoringButton.setText("Start Background Monitoring");

        // Re-enable button if still connected
        String currentStatus = statusText.getText().toString();
        monitoringButton.setEnabled("Connected".equals(currentStatus));

        Toast.makeText(this, "Background monitoring stopped", Toast.LENGTH_SHORT).show();
    }

    /**
     * Connect to device with auto-connect tracking
     */
    private void connectToDevice(String address, boolean isAutoConnect) {
        if (isAutoConnect) {
            isAutoConnecting = true;
            statusText.setText("Auto-connecting...");
            Log.d(TAG, "Attempting auto-connect to: " + address);
        }

        viewModel.connectToDevice(address);
    }

    /**
     * Handle successful connection
     */
    private void onConnectionSuccess(String address) {
        Log.d(TAG, "Connection successful to: " + address);

        // Save connection details if WiFi is connected
        String currentSSID = wifiStateMonitor.getCurrentSSID();
        if (currentSSID != null) {
            connectionManager.saveSuccessfulConnection(address, currentSSID);
            Log.d(TAG, "Saved connection details - Device: " + address + ", WiFi: " + currentSSID);
        }

        isAutoConnecting = false;
    }

    /**
     * Handle connection failure
     */
    private void onConnectionFailure() {
        if (isAutoConnecting) {
            Log.d(TAG, "Auto-connect failed");
            isAutoConnecting = false;
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        if (requestCode == NOTIFICATION_PERMISSION_REQUEST_CODE) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                // Permission granted, start monitoring
                Toast.makeText(this, "Notification permission granted", Toast.LENGTH_SHORT).show();
                startBackgroundMonitoring();
            } else {
                // Permission denied
                Toast.makeText(this, "Notification permission is required for background monitoring", Toast.LENGTH_LONG).show();
            }
        }
    }

    // WiFiStateListener implementation
    @Override
    public void onWiFiConnected(String ssid) {
        Log.d(TAG, "WiFi connected to: " + ssid);
        runOnUiThread(() -> {
            attemptAutoConnect(ssid);
        });
    }

    @Override
    public void onWiFiDisconnected() {
        Log.d(TAG, "WiFi disconnected");
        runOnUiThread(() -> {
            // Stop background monitoring if running
            if (isMonitoring) {
                stopBackgroundMonitoring();
                Toast.makeText(this, "WiFi disconnected - monitoring stopped", Toast.LENGTH_SHORT).show();
            }
        });
    }

    @Override
    public void onWiFiSSIDChanged(String newSSID) {
        Log.d(TAG, "WiFi SSID changed to: " + newSSID);
        runOnUiThread(() -> {
            // Stop current monitoring and attempt auto-connect to new network
            if (isMonitoring) {
                stopBackgroundMonitoring();
            }
            attemptAutoConnect(newSSID);
        });
    }

    /**
     * Attempt auto-connect if conditions are met
     */
    private void attemptAutoConnect(String currentSSID) {
        DeviceConnectionManager.AutoConnectData autoConnectData =
            connectionManager.getAutoConnectData(currentSSID);

        if (autoConnectData != null) {
            Log.d(TAG, "Auto-connect conditions met: " + autoConnectData);

            // Update UI to show the device address
            ipAddressEdit.setText(autoConnectData.deviceAddress);

            // Attempt connection
            connectToDevice(autoConnectData.deviceAddress, true);

            Toast.makeText(this, "Auto-connecting to " + autoConnectData.deviceAddress, Toast.LENGTH_SHORT).show();
        } else {
            Log.d(TAG, "Auto-connect conditions not met for SSID: " + currentSSID);
        }
    }

    private void getAndDisplayFcmToken() {
        Log.d(TAG, "Getting FCM token...");

        // Add timeout to see if callback is called
        new android.os.Handler().postDelayed(() -> {
            Log.w(TAG, "FCM token request timeout - no callback received in 10 seconds");
            Toast.makeText(MainActivity.this, "FCM Token request timeout", Toast.LENGTH_LONG).show();
        }, 10000);

        FirebaseMessaging.getInstance().getToken()
            .addOnCompleteListener(task -> {
                Log.d(TAG, "FCM getToken() completed. Success: " + task.isSuccessful());

                if (!task.isSuccessful()) {
                    Exception exception = task.getException();
                    Log.e(TAG, "FCM token failed", exception);
                    Toast.makeText(MainActivity.this, "FCM Error: " + (exception != null ? exception.getMessage() : "Unknown"), Toast.LENGTH_LONG).show();
                    return;
                }

                String token = task.getResult();
                Log.i(TAG, "FCM token: " + (token != null ? "SUCCESS" : "NULL"));

                if (token != null && !token.isEmpty()) {
                    String displayToken = token.substring(0, Math.min(50, token.length())) + "...";
                    Toast.makeText(MainActivity.this, "FCM Token: " + displayToken, Toast.LENGTH_LONG).show();
                    Log.i(TAG, "Full token: " + token);
                } else {
                    Toast.makeText(MainActivity.this, "FCM Token is null", Toast.LENGTH_LONG).show();
                }
            });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (wifiStateMonitor != null) {
            wifiStateMonitor.stopMonitoring();
        }
    }
}
