package com.thermalsensor.app;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import android.content.Context;
import android.util.Log;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

public class ThermalSensorService {

    private static final String TAG = "ThermalSensorService";
    private static final int TIMEOUT_SECONDS = 15;
    private OkHttpClient client;
    private String baseUrl;
    private Gson gson;
    private Context context;
    private MDNSResolver mdnsResolver;
    
    public ThermalSensorService(Context context) {
        this.context = context;
        this.mdnsResolver = new MDNSResolver(context);
        client = new OkHttpClient.Builder()
                .connectTimeout(TIMEOUT_SECONDS, TimeUnit.SECONDS)
                .readTimeout(TIMEOUT_SECONDS, TimeUnit.SECONDS)
                .writeTimeout(TIMEOUT_SECONDS, TimeUnit.SECONDS)
                .build();
        gson = new Gson();
    }
    
    public boolean connect(String address) {
        Log.d(TAG, "Attempting to connect to: " + address);

        try {
            String resolvedAddress = address;

            // If it's a .local hostname, try to resolve it via mDNS
            if (address.endsWith(".local")) {
                Log.d(TAG, "Resolving mDNS hostname: " + address);
                try {
                    String resolvedIP = mdnsResolver.resolveHostname(address).get();
                    if (resolvedIP != null) {
                        resolvedAddress = resolvedIP;
                        Log.d(TAG, "Resolved " + address + " to " + resolvedIP);
                    } else {
                        Log.e(TAG, "Failed to resolve mDNS hostname: " + address);
                        return false;
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Exception during mDNS resolution: " + e.getMessage(), e);
                    return false;
                }
            }

            baseUrl = "http://" + resolvedAddress;
            Log.d(TAG, "Using base URL: " + baseUrl);

            // Test connection by trying to get fever status (ESP32 endpoint)
            Request request = new Request.Builder()
                    .url(baseUrl + "/fever-status")
                    .build();

            Log.d(TAG, "Sending request to: " + baseUrl + "/fever-status");
            try (Response response = client.newCall(request).execute()) {
                Log.d(TAG, "Response code: " + response.code());
                Log.d(TAG, "Response message: " + response.message());
                if (response.isSuccessful()) {
                    Log.d(TAG, "Connection successful!");
                    return true;
                } else {
                    Log.e(TAG, "Connection failed with HTTP " + response.code() + ": " + response.message());
                    return false;
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Connection failed with exception: " + e.getMessage(), e);
            return false;
        }
    }
    
    public Double getTemperature() throws IOException {
        if (baseUrl == null) {
            throw new IllegalStateException("Not connected to any device");
        }

        Request request = new Request.Builder()
                .url(baseUrl + "/fever-status")
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful() && response.body() != null) {
                String responseBody = response.body().string();
                JsonObject jsonObject = gson.fromJson(responseBody, JsonObject.class);

                if (jsonObject.has("currentTemp")) {
                    return jsonObject.get("currentTemp").getAsDouble();
                }
            }
        }

        return null;
    }
    
    public JsonObject getStatus() throws IOException {
        if (baseUrl == null) {
            throw new IllegalStateException("Not connected to any device");
        }

        Request request = new Request.Builder()
                .url(baseUrl + "/fever-status")
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful() && response.body() != null) {
                String responseBody = response.body().string();
                return gson.fromJson(responseBody, JsonObject.class);
            }
        }

        return null;
    }
    
    public void disconnect() {
        baseUrl = null;
        // OkHttpClient doesn't need explicit disconnection
        // It manages connections automatically
    }
}
