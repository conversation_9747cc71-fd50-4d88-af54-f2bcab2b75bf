# Thermal Sensor Android App

A native Android application for monitoring temperature data from the ESP32 thermal sensor device.

## Features

- **Real-time Temperature Monitoring**: Connect to ESP32 device and display current temperature readings
- **Simple UI**: Clean, Material Design interface with large temperature display
- **Network Communication**: HTTP-based communication with ESP32 web server
- **Error Handling**: Comprehensive error handling and user feedback
- **Responsive Design**: Optimized for various screen sizes

## Architecture

- **MVVM Pattern**: Uses Android Architecture Components (ViewModel, LiveData)
- **HTTP Client**: OkHttp for reliable network communication
- **JSON Parsing**: Gson for parsing ESP32 API responses
- **Material Design**: Modern UI following Material Design guidelines

## Project Structure

```
app/src/main/
├── java/com/thermalsensor/app/
│   ├── MainActivity.java          # Main UI activity
│   ├── ThermalSensorViewModel.java # Business logic and state management
│   └── ThermalSensorService.java  # ESP32 communication service
├── res/
│   ├── layout/
│   │   └── activity_main.xml      # Main UI layout
│   ├── values/
│   │   ├── strings.xml            # String resources
│   │   ├── colors.xml             # Color definitions
│   │   └── themes.xml             # App themes
│   └── drawable/
│       └── ic_launcher.xml        # App icon
└── AndroidManifest.xml            # App configuration
```

## API Endpoints

The app communicates with the ESP32 device using these endpoints:

- `GET /api/status` - Check device connectivity
- `GET /api/temperature` - Get current temperature reading

Expected JSON response format:

```json
{
  "temperature": 25.6,
  "status": "ok",
  "timestamp": 1234567890
}
```

## Building the App

### Prerequisites

- Android SDK with API 34
- Java 17
- Gradle 8.14.3+

### Build Commands

```bash
# Debug build
./gradlew assembleDebug

# Release build
./gradlew assembleRelease

# Install on connected device
./gradlew installDebug
```

### Build Output

- Debug APK: `app/build/outputs/apk/debug/app-debug.apk`
- Release APK: `app/build/outputs/apk/release/app-release.apk`

## Installation

1. Enable "Unknown Sources" in Android Settings
2. Transfer the APK to your Android device
3. Install the APK file
4. Grant necessary permissions (Internet access)

## Usage

1. **Launch the app** on your Android device
2. **Enter ESP32 IP address** in the input field (default: *************)
3. **Tap "Connect"** to establish connection with the ESP32 device
4. **View temperature** displayed in the large temperature card
5. **Tap "Refresh Temperature"** to get updated readings

## Configuration

### Network Settings

- Default ESP32 IP: `*************` (static IP, no mDNS required)
- HTTP timeout: 15 seconds
- Automatic retry on connection failure

### Permissions

- `INTERNET` - Required for HTTP communication
- `ACCESS_NETWORK_STATE` - Check network connectivity
- `ACCESS_WIFI_STATE` - WiFi status monitoring

## Development

### Adding New Features

1. Update the `ThermalSensorService.java` for new API endpoints
2. Modify `ThermalSensorViewModel.java` for business logic
3. Update `MainActivity.java` and layouts for UI changes

### Testing

- Unit tests can be added in `app/src/test/`
- Instrumented tests in `app/src/androidTest/`

## Troubleshooting

### Common Issues

1. **Connection Failed**: Check ESP32 IP address and network connectivity
2. **Build Errors**: Ensure all dependencies are properly installed
3. **App Crashes**: Check device logs using `adb logcat`

### Debug Commands

```bash
# View device logs
adb logcat | grep ThermalSensor

# Install debug build
adb install app/build/outputs/apk/debug/app-debug.apk

# Uninstall app
adb uninstall com.thermalsensor.app
```

## License

This project is part of the thermal sensor monitoring system.
