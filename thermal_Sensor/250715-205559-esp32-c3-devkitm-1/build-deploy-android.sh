#!/bin/bash

# Build and deploy Android app to connected device
# This script builds the APK and installs it on the connected Android device

echo "🔨 Building and deploying thermal sensor Android app..."
echo "=================================================="

# Set environment variables for Android build
export JAVA_HOME="/opt/homebrew/opt/openjdk@17"
export ANDROID_HOME="$HOME/Android/Sdk"
export PATH="$ANDROID_HOME/platform-tools:$ANDROID_HOME/tools:$PATH"

# Check if device is connected
echo "📱 Checking for connected Android devices..."
if ! $ANDROID_HOME/platform-tools/adb devices | grep -q "device$"; then
    echo "❌ No Android device connected!"
    echo "Run ./connect-android.sh first to connect your device"
    exit 1
fi

echo "✅ Android device found:"
$ANDROID_HOME/platform-tools/adb devices

# Navigate to android app directory
cd android-app

# Clean and build the project
echo ""
echo "🧹 Cleaning previous build..."
./gradlew clean

echo ""
echo "🔨 Building debug APK..."
if ./gradlew assembleDebug; then
    echo "✅ Build successful!"
else
    echo "❌ Build failed!"
    exit 1
fi

# Install the APK
echo ""
echo "📲 Installing APK on device..."
if $ANDROID_HOME/platform-tools/adb install -r app/build/outputs/apk/debug/app-debug.apk; then
    echo "✅ Installation successful!"
else
    echo "❌ Installation failed!"
    exit 1
fi

# Launch the app
echo ""
echo "🚀 Launching thermal sensor app..."
if $ANDROID_HOME/platform-tools/adb shell am start -n com.thermalsensor.app/.MainActivity; then
    echo "✅ App launched successfully!"
else
    echo "❌ Failed to launch app!"
    exit 1
fi

echo ""
echo "🎉 Build and deployment complete!"
echo "The thermal sensor app should now be running on your device."
