# Playwright Tests for Thermal Sensor

This directory contains end-to-end tests for the thermal sensor web interface using Playwright.

## Setup

1. Install dependencies:
   ```bash
   cd playwright
   npm install
   ```

2. Install Playwright browsers:
   ```bash
   npx playwright install
   ```

## Running Tests

Make sure the thermal sensor device is running and accessible at `http://192.168.0.201` before running tests.

### Time Settings Test
```bash
node test_time_settings.js
```

This test verifies:
- Time & Schedule tab functionality
- NTP server configuration
- Timezone selection
- Settings persistence
- Form value updates

## Test Structure

- `test_time_settings.js` - Tests for time and schedule settings functionality
- `package.json` - Node.js dependencies for testing
- `node_modules/` - Installed dependencies (gitignored)

## Notes

- Tests run in headless mode by default
- Device must be accessible on the network
- Tests verify both UI interactions and API responses
