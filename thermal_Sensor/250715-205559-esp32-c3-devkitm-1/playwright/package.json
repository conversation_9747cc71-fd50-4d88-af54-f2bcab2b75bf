{"name": "250715-205559-esp32-c3-devkitm-1", "version": "1.0.0", "description": "## 🌡️ Overview", "main": "test_time_settings.js", "directories": {"lib": "lib", "test": "test"}, "scripts": {"test": "npx playwright test", "test:headed": "npx playwright test --headed", "test:debug": "npx playwright test --debug", "test:ui": "npx playwright test --ui", "test:basic": "npx playwright test tests/01-basic-page-load.spec.js", "test:header": "npx playwright test tests/02-header-section.spec.js", "test:stats": "npx playwright test tests/03-stats-grid.spec.js", "test:thermal": "npx playwright test tests/04-thermal-visualization.spec.js", "test:navigation": "npx playwright test tests/05-settings-navigation.spec.js", "test:calibration": "npx playwright test tests/06-auto-calibration.spec.js", "test:essential": "npx playwright test tests/07-essential-settings.spec.js", "test:detection": "npx playwright test tests/08-detection-alerts.spec.js", "test:advanced": "npx playwright test tests/09-advanced-settings.spec.js", "test:time": "npx playwright test tests/10-time-schedule.spec.js", "test:controls": "npx playwright test tests/11-system-controls.spec.js", "test:persistence": "npx playwright test tests/12-settings-persistence.spec.js", "test:responsive": "npx playwright test tests/13-responsive-design.spec.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@playwright/test": "^1.54.2", "playwright": "^1.54.2"}}