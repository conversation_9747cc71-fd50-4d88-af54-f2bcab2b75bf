const { test, expect } = require("@playwright/test");
const { ThermalSensorTestBase } = require("./base-test");

test.describe("System & Advanced Settings Tests", () => {
  let testBase;

  test.beforeEach(async ({ page }) => {
    testBase = new ThermalSensorTestBase(page);
  });

  test("should display system & advanced tab content correctly", async ({
    page,
  }) => {
    console.log("⚡ Testing system & advanced tab content...");

    await testBase.navigateToHomePage();

    // Navigate to System & Advanced tab
    await testBase.clickSettingsTab("system-advanced-tab");

    // Check system & advanced content is visible
    const systemAdvancedContent = page.locator("#system-advanced");
    await expect(systemAdvancedContent).toBeVisible();
    await expect(systemAdvancedContent).toHaveClass(/show/);
    await expect(systemAdvancedContent).toHaveClass(/active/);
    console.log("✅ System & advanced content is visible and active");
  });

  test("should display system performance info correctly", async ({ page }) => {
    console.log("🔧 Testing system performance info...");

    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("system-advanced-tab");

    // Check for system performance info message
    const systemPerformanceInfo = page
      .locator(".alert-info")
      .filter({ hasText: "System Performance" });
    await expect(systemPerformanceInfo).toBeVisible();
    console.log("✅ System performance info message is present");

    // Verify the info message contains expected text
    const infoText = await systemPerformanceInfo.textContent();
    expect(infoText).toContain("optimized settings");
    expect(infoText).toContain("real-time monitoring");
    console.log("✅ System performance info message contains correct text");
  });

  test("should display read interval control correctly", async ({ page }) => {
    console.log("⚡ Testing read interval control...");

    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("system-advanced-tab");

    // Check read interval input
    const readIntervalInput = page.locator("#readInterval");
    await expect(readIntervalInput).toBeVisible();
    await expect(readIntervalInput).toHaveAttribute("type", "range");
    await expect(readIntervalInput).toHaveAttribute("min", "100");
    await expect(readIntervalInput).toHaveAttribute("max", "1000");
    await expect(readIntervalInput).toHaveAttribute("step", "50");
    console.log("✅ Read interval input has correct attributes");

    // Check label
    const readIntervalLabel = page
      .locator("label")
      .filter({ hasText: "Sensor Read Interval" });
    await expect(readIntervalLabel).toBeVisible();
    console.log("✅ Read interval label is present");

    // Check tachometer icon
    const tachometerIcon = readIntervalLabel.locator("i.fas.fa-tachometer-alt");
    await expect(tachometerIcon).toBeVisible();
    console.log("✅ Tachometer icon is present");

    // Check value display
    const readIntervalValue = page.locator("#readIntervalValue");
    await expect(readIntervalValue).toBeVisible();
    console.log("✅ Read interval value display is present");
  });

  test("should display data logging info correctly", async ({ page }) => {
    console.log("📝 Testing data logging info...");

    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("system-advanced-tab");

    // Check for data logging info message
    const dataLoggingInfo = page
      .locator(".alert-info")
      .filter({ hasText: "Data Logging" });
    await expect(dataLoggingInfo).toBeVisible();
    console.log("✅ Data logging info message is present");

    // Verify the info message contains expected text
    const infoText = await dataLoggingInfo.textContent();
    expect(infoText).toContain("automatically logged");
    expect(infoText).toContain("30 seconds");
    expect(infoText).toContain("100-sample");
    console.log("✅ Data logging info message contains correct text");
  });

  test("should display debug mode control correctly", async ({ page }) => {
    console.log("🐛 Testing debug mode control...");

    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("system-advanced-tab");

    // Check debug mode checkbox
    const debugModeInput = page.locator("#debugMode");
    await expect(debugModeInput).toBeVisible();
    await expect(debugModeInput).toHaveAttribute("type", "checkbox");
    console.log("✅ Debug mode checkbox has correct attributes");

    // Check label
    const debugModeLabel = page
      .locator("label")
      .filter({ hasText: "Debug Mode" });
    await expect(debugModeLabel).toBeVisible();
    console.log("✅ Debug mode label is present");

    // Check bug icon
    const bugIcon = debugModeLabel.locator("i.fas.fa-bug");
    await expect(bugIcon).toBeVisible();
    console.log("✅ Bug icon is present");
  });

  test("should display display brightness control correctly", async ({
    page,
  }) => {
    console.log("💡 Testing display brightness control...");

    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("display-audio-tab");

    // Check display brightness input
    const brightnessInput = page.locator("#displayBrightness");
    await expect(brightnessInput).toBeVisible();
    await expect(brightnessInput).toHaveAttribute("type", "range");
    await expect(brightnessInput).toHaveAttribute("min", "1");
    await expect(brightnessInput).toHaveAttribute("max", "255");
    await expect(brightnessInput).toHaveAttribute("step", "1");
    console.log("✅ Display brightness input has correct attributes");

    // Check label
    const brightnessLabel = page
      .locator("label")
      .filter({ hasText: "OLED Display Brightness" });
    await expect(brightnessLabel).toBeVisible();
    console.log("✅ Display brightness label is present");

    // Check sun icon
    const sunIcon = brightnessLabel.locator("i.fas.fa-sun");
    await expect(sunIcon).toBeVisible();
    console.log("✅ Sun icon is present");

    // Check value display
    const brightnessValue = page.locator("#displayBrightnessValue");
    await expect(brightnessValue).toBeVisible();
    console.log("✅ Display brightness value display is present");
  });

  test("should update temperature smoothing value when slider changes", async ({
    page,
  }) => {
    console.log("🔄 Testing temperature smoothing value updates...");

    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("system-advanced-tab");

    // Get initial value
    const smoothingValue = page.locator("#smoothingValue");
    const initialValue = await smoothingValue.textContent();
    console.log(`Initial smoothing: ${initialValue}`);

    // Change slider value
    const smoothingSlider = page.locator("#smoothing");
    await smoothingSlider.fill("7");
    await page.waitForTimeout(500);

    // Check that value updated
    const updatedValue = await smoothingValue.textContent();
    console.log(`Updated smoothing: ${updatedValue}`);
    expect(updatedValue).toContain("7");
    console.log("✅ Temperature smoothing value updates correctly");
  });

  test("should have proper advanced settings structure", async ({ page }) => {
    console.log("🏗️ Testing advanced settings structure...");

    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("system-advanced-tab");

    // Check section titles
    const systemPerformanceTitle = page
      .locator("h5")
      .filter({ hasText: "System Performance" });
    const displaySettingsTitle = page
      .locator("h5")
      .filter({ hasText: "Display Settings" });

    await expect(systemPerformanceTitle).toBeVisible();
    await expect(displaySettingsTitle).toBeVisible();
    console.log("✅ Section titles are present");

    // Check that all controls are within the advanced tab content
    const advancedContent = page.locator("#advanced");
    const smoothingControl = advancedContent.locator("#smoothing");
    const baselineDurationControl =
      advancedContent.locator("#baselineDuration");
    const baselineMaxControl = advancedContent.locator("#baselineMax");
    const readIntervalControl = advancedContent.locator("#readInterval");
    const logIntervalControl = advancedContent.locator("#logInterval");
    const historySizeControl = advancedContent.locator("#historySize");
    const debugModeControl = advancedContent.locator("#debugMode");
    const brightnessControl = advancedContent.locator("#displayBrightness");

    await expect(smoothingControl).toBeVisible();
    await expect(baselineDurationControl).toBeVisible();
    await expect(baselineMaxControl).toBeVisible();
    await expect(readIntervalControl).toBeVisible();
    await expect(logIntervalControl).toBeVisible();
    await expect(historySizeControl).toBeVisible();
    await expect(debugModeControl).toBeVisible();
    await expect(brightnessControl).toBeVisible();

    console.log("✅ All advanced settings controls are properly contained");
  });
});
