const { test, expect } = require("@playwright/test");
const { ThermalSensorTestBase } = require("./base-test");

test.describe("Fever Detection Settings Tests", () => {
  let testBase;

  test.beforeEach(async ({ page }) => {
    testBase = new ThermalSensorTestBase(page);
  });

  test("should display fever detection tab content correctly", async ({
    page,
  }) => {
    console.log("🔔 Testing fever detection tab content...");

    await testBase.navigateToHomePage();

    // Navigate to Fever Detection tab
    await testBase.clickSettingsTab("fever-detection-tab");

    // Check fever detection content is visible
    const feverDetectionContent = page.locator("#fever-detection");
    await expect(feverDetectionContent).toBeVisible();
    await expect(feverDetectionContent).toHaveClass(/show/);
    await expect(feverDetectionContent).toHaveClass(/active/);
    console.log("✅ Fever detection content is visible and active");
  });

  test("should display fever threshold control correctly", async ({ page }) => {
    console.log("🌡️ Testing fever threshold control...");

    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("fever-detection-tab");

    // Check fever threshold input
    const feverThresholdInput = page.locator("#feverThreshold");
    await expect(feverThresholdInput).toBeVisible();
    await expect(feverThresholdInput).toHaveAttribute("type", "range");
    await expect(feverThresholdInput).toHaveAttribute("min", "1");
    await expect(feverThresholdInput).toHaveAttribute("max", "5");
    await expect(feverThresholdInput).toHaveAttribute("step", "0.1");
    console.log("✅ Fever threshold input has correct attributes");

    // Check label
    const feverLabel = page
      .locator("label")
      .filter({ hasText: "Fever Threshold (above baseline)" });
    await expect(feverLabel).toBeVisible();
    console.log("✅ Fever threshold label is present");

    // Check thermometer icon
    const thermometerIcon = feverLabel.locator("i.fas.fa-thermometer-half");
    await expect(thermometerIcon).toBeVisible();
    console.log("✅ Thermometer icon is present");

    // Check value display
    const feverValue = page.locator("#feverThresholdValue");
    await expect(feverValue).toBeVisible();
    console.log("✅ Fever threshold value display is present");
  });

  test("should display sustained time control correctly", async ({ page }) => {
    console.log("⏱️ Testing sustained time control...");

    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("detection-alerts-tab");

    // Check sustained time input
    const sustainedTimeInput = page.locator("#sustainedTime");
    await expect(sustainedTimeInput).toBeVisible();
    await expect(sustainedTimeInput).toHaveAttribute("type", "range");
    await expect(sustainedTimeInput).toHaveAttribute("min", "5");
    await expect(sustainedTimeInput).toHaveAttribute("max", "30");
    await expect(sustainedTimeInput).toHaveAttribute("step", "1");
    console.log("✅ Sustained time input has correct attributes");

    // Check label
    const sustainedLabel = page
      .locator("label")
      .filter({ hasText: "Sustained Elevation Time" });
    await expect(sustainedLabel).toBeVisible();
    console.log("✅ Sustained time label is present");

    // Check clock icon
    const clockIcon = sustainedLabel.locator("i.fas.fa-clock");
    await expect(clockIcon).toBeVisible();
    console.log("✅ Clock icon is present");

    // Check value display
    const sustainedValue = page.locator("#sustainedTimeValue");
    await expect(sustainedValue).toBeVisible();
    console.log("✅ Sustained time value display is present");
  });

  test("should display medical threshold control correctly", async ({
    page,
  }) => {
    console.log("🏥 Testing medical threshold control...");

    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("detection-alerts-tab");

    // Check medical threshold input
    const medicalThresholdInput = page.locator("#medicalThreshold");
    await expect(medicalThresholdInput).toBeVisible();
    await expect(medicalThresholdInput).toHaveAttribute("type", "range");
    await expect(medicalThresholdInput).toHaveAttribute("min", "99.5");
    await expect(medicalThresholdInput).toHaveAttribute("max", "101.5");
    await expect(medicalThresholdInput).toHaveAttribute("step", "0.1");
    console.log("✅ Medical threshold input has correct attributes");

    // Check label
    const medicalLabel = page
      .locator("label")
      .filter({ hasText: "Medical Fever Threshold" });
    await expect(medicalLabel).toBeVisible();
    console.log("✅ Medical threshold label is present");

    // Check medical icon
    const medicalIcon = medicalLabel.locator("i.fas.fa-hospital");
    await expect(medicalIcon).toBeVisible();
    console.log("✅ Medical icon is present");

    // Check value display
    const medicalValue = page.locator("#medicalThresholdValue");
    await expect(medicalValue).toBeVisible();
    console.log("✅ Medical threshold value display is present");
  });

  test("should display alert configuration info correctly", async ({
    page,
  }) => {
    console.log("🔊 Testing alert configuration info...");

    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("detection-alerts-tab");

    // Check for alert configuration info message
    const alertConfigInfo = page
      .locator(".alert-warning")
      .filter({ hasText: "Alert Configuration" });
    await expect(alertConfigInfo).toBeVisible();
    console.log("✅ Alert configuration info message is present");

    // Verify the info message contains expected text
    const infoText = await alertConfigInfo.textContent();
    expect(infoText).toContain("automatically triggered");
    expect(infoText).toContain("Advanced tab");
    console.log("✅ Alert configuration info message contains correct text");
  });

  test("should display ESP32 buzzer info (browser audio removed)", async ({
    page,
  }) => {
    console.log("🔉 Testing ESP32 buzzer info display...");

    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("detection-alerts-tab");

    // Browser alert volume control should no longer exist
    const alertVolumeInput = page.locator("#alertVolume");
    await expect(alertVolumeInput).not.toBeVisible();
    console.log("✅ Browser alert volume control removed");

    // Check for ESP32 buzzer info message
    const buzzerInfo = page
      .locator(".alert-info")
      .filter({ hasText: "ESP32 buzzer" });
    await expect(buzzerInfo).toBeVisible();
    console.log("✅ ESP32 buzzer info message is present");

    // Verify the info message contains expected text
    const infoText = await buzzerInfo.textContent();
    expect(infoText).toContain("built-in ESP32 buzzer");
    expect(infoText).toContain("Advanced tab");
    console.log("✅ ESP32 buzzer info message contains correct text");
  });

  test("should update fever threshold value when slider changes", async ({
    page,
  }) => {
    console.log("🔄 Testing fever threshold value updates...");

    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("detection-alerts-tab");

    // Get current settings from API
    const currentSettings = await testBase.getCurrentSettings();
    console.log(
      `Current fever threshold from API: ${currentSettings.feverThreshold}`
    );

    // Get initial value from UI
    const feverValue = page.locator("#feverThresholdValue");
    const initialValue = await feverValue.textContent();
    console.log(`Initial fever threshold from UI: ${initialValue}`);

    // Choose a test value different from current (within valid range 1-5)
    const testValue =
      currentSettings.feverThreshold < 4
        ? currentSettings.feverThreshold + 0.5
        : currentSettings.feverThreshold - 0.5;

    // Change slider value
    const feverSlider = page.locator("#feverThreshold");
    await feverSlider.fill(testValue.toString());
    await page.waitForTimeout(500);

    // Check that value updated
    const updatedValue = await feverValue.textContent();
    console.log(`Updated fever threshold: ${updatedValue}`);
    expect(updatedValue).toContain(testValue.toString());
    console.log("✅ Fever threshold value updates correctly");
  });

  test("should update sustained time value when slider changes", async ({
    page,
  }) => {
    console.log("⏱️ Testing sustained time value updates...");

    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("detection-alerts-tab");

    // Get initial value
    const sustainedValue = page.locator("#sustainedTimeValue");
    const initialValue = await sustainedValue.textContent();
    console.log(`Initial sustained time: ${initialValue}`);

    // Change slider value
    const sustainedSlider = page.locator("#sustainedTime");
    await sustainedSlider.fill("15");
    await page.waitForTimeout(500);

    // Check that value updated
    const updatedValue = await sustainedValue.textContent();
    console.log(`Updated sustained time: ${updatedValue}`);
    expect(updatedValue).toContain("15");
    console.log("✅ Sustained time value updates correctly");
  });

  test("should update medical threshold value when slider changes", async ({
    page,
  }) => {
    console.log("🏥 Testing medical threshold value updates...");

    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("detection-alerts-tab");

    // Get initial value
    const medicalValue = page.locator("#medicalThresholdValue");
    const initialValue = await medicalValue.textContent();
    console.log(`Initial medical threshold: ${initialValue}`);

    // Change slider value
    const medicalSlider = page.locator("#medicalThreshold");
    await medicalSlider.fill("100.8");
    await page.waitForTimeout(500);

    // Check that value updated
    const updatedValue = await medicalValue.textContent();
    console.log(`Updated medical threshold: ${updatedValue}`);
    expect(updatedValue).toContain("100.8");
    console.log("✅ Medical threshold value updates correctly");
  });

  test("should verify browser audio controls are removed", async ({ page }) => {
    console.log("🔉 Testing browser audio controls removal...");

    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("detection-alerts-tab");

    // Browser alert volume controls should no longer exist
    const volumeValue = page.locator("#alertVolumeValue");
    const volumeSlider = page.locator("#alertVolume");

    await expect(volumeValue).not.toBeVisible();
    await expect(volumeSlider).not.toBeVisible();

    console.log("✅ Browser audio controls successfully removed");
  });

  test("should have proper detection & alerts structure", async ({ page }) => {
    console.log("🏗️ Testing detection & alerts structure...");

    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("detection-alerts-tab");

    // Check section titles
    const feverDetectionTitle = page
      .locator("h5")
      .filter({ hasText: "Fever Detection" });
    const alertSettingsTitle = page
      .locator("h5")
      .filter({ hasText: "Alert Settings" });

    await expect(feverDetectionTitle).toBeVisible();
    await expect(alertSettingsTitle).toBeVisible();
    console.log("✅ Section titles are present");

    // Check that main controls are within the detection-alerts tab content
    const alertsContent = page.locator("#detection-alerts");
    const feverControl = alertsContent.locator("#feverThreshold");
    const sustainedControl = alertsContent.locator("#sustainedTime");
    const medicalControl = alertsContent.locator("#medicalThreshold");

    await expect(feverControl).toBeVisible();
    await expect(sustainedControl).toBeVisible();
    await expect(medicalControl).toBeVisible();

    console.log("✅ All detection & alerts controls are properly contained");
  });
});
