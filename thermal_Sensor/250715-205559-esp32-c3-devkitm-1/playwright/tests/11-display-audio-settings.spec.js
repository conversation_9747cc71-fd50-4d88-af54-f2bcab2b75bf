const { test, expect } = require("@playwright/test");
const { ThermalSensorTestBase } = require("./base-test");

test.describe("Display & Audio Settings Tests", () => {
  let testBase;

  test.beforeEach(async ({ page }) => {
    testBase = new ThermalSensorTestBase(page);
  });

  test("should display display & audio tab content correctly", async ({ page }) => {
    console.log("🖥️ Testing display & audio tab content...");

    await testBase.navigateToHomePage();

    // Navigate to Display & Audio tab
    await testBase.clickSettingsTab("display-audio-tab");

    // Check display & audio content is visible
    const displayAudioContent = page.locator("#display-audio");
    await expect(displayAudioContent).toBeVisible();
    await expect(displayAudioContent).toHaveClass(/show/);
    await expect(displayAudioContent).toHaveClass(/active/);
    console.log("✅ Display & audio content is visible and active");
  });

  test("should display brightness control correctly", async ({ page }) => {
    console.log("💡 Testing display brightness control...");

    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("display-audio-tab");

    // Check display brightness input
    const brightnessInput = page.locator("#displayBrightness");
    await expect(brightnessInput).toBeVisible();
    await expect(brightnessInput).toHaveAttribute("type", "range");
    await expect(brightnessInput).toHaveAttribute("min", "10");
    await expect(brightnessInput).toHaveAttribute("max", "255");
    await expect(brightnessInput).toHaveAttribute("step", "5");
    console.log("✅ Display brightness input has correct attributes");

    // Check label
    const brightnessLabel = page
      .locator("label")
      .filter({ hasText: "OLED Display Brightness" });
    await expect(brightnessLabel).toBeVisible();
    console.log("✅ Display brightness label is present");

    // Check sun icon
    const sunIcon = brightnessLabel.locator("i.fas.fa-sun");
    await expect(sunIcon).toBeVisible();
    console.log("✅ Sun icon is present");

    // Check value display
    const brightnessValue = page.locator("#displayBrightnessValue");
    await expect(brightnessValue).toBeVisible();
    console.log("✅ Display brightness value display is present");
  });

  test("should display volume controls correctly", async ({ page }) => {
    console.log("🔊 Testing volume controls...");

    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("display-audio-tab");

    // Check day volume input
    const dayVolumeInput = page.locator("#dayVolume");
    await expect(dayVolumeInput).toBeVisible();
    await expect(dayVolumeInput).toHaveAttribute("type", "range");
    await expect(dayVolumeInput).toHaveAttribute("min", "0");
    await expect(dayVolumeInput).toHaveAttribute("max", "100");
    await expect(dayVolumeInput).toHaveAttribute("step", "5");
    console.log("✅ Day volume input has correct attributes");

    // Check night volume input
    const nightVolumeInput = page.locator("#nightVolume");
    await expect(nightVolumeInput).toBeVisible();
    await expect(nightVolumeInput).toHaveAttribute("type", "range");
    await expect(nightVolumeInput).toHaveAttribute("min", "0");
    await expect(nightVolumeInput).toHaveAttribute("max", "100");
    await expect(nightVolumeInput).toHaveAttribute("step", "5");
    console.log("✅ Night volume input has correct attributes");

    // Check fever volume input
    const feverVolumeInput = page.locator("#feverVolume");
    await expect(feverVolumeInput).toBeVisible();
    await expect(feverVolumeInput).toHaveAttribute("type", "range");
    await expect(feverVolumeInput).toHaveAttribute("min", "0");
    await expect(feverVolumeInput).toHaveAttribute("max", "100");
    await expect(feverVolumeInput).toHaveAttribute("step", "5");
    console.log("✅ Fever volume input has correct attributes");

    // Check value displays
    const dayVolumeValue = page.locator("#dayVolumeValue");
    await expect(dayVolumeValue).toBeVisible();
    const nightVolumeValue = page.locator("#nightVolumeValue");
    await expect(nightVolumeValue).toBeVisible();
    const feverVolumeValue = page.locator("#feverVolumeValue");
    await expect(feverVolumeValue).toBeVisible();
    console.log("✅ Volume value displays are present");
  });

  test("should display night mode info correctly", async ({ page }) => {
    console.log("🌙 Testing night mode info...");

    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("display-audio-tab");

    // Check for night mode info message
    const nightModeInfo = page
      .locator(".alert-info")
      .filter({ hasText: "Night Mode" });
    await expect(nightModeInfo).toBeVisible();
    console.log("✅ Night mode info message is present");

    // Verify the info message contains expected text
    const infoText = await nightModeInfo.textContent();
    expect(infoText).toContain("automatically dims");
    expect(infoText).toContain("Time & Schedule");
    console.log("✅ Night mode info message contains correct text");
  });

  test("should display audio system info correctly", async ({ page }) => {
    console.log("🔊 Testing audio system info...");

    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("display-audio-tab");

    // Check for audio system info message
    const audioSystemInfo = page
      .locator(".alert-warning")
      .filter({ hasText: "Audio System" });
    await expect(audioSystemInfo).toBeVisible();
    console.log("✅ Audio system info message is present");

    // Verify the info message contains expected text
    const infoText = await audioSystemInfo.textContent();
    expect(infoText).toContain("built-in ESP32 buzzer");
    expect(infoText).toContain("automatically adjust");
    console.log("✅ Audio system info message contains correct text");
  });
});
