const { test, expect } = require("@playwright/test");
const { ThermalSensorTestBase } = require("./base-test");

test.describe("Thermal Visualization Tests", () => {
  let testBase;

  test.beforeEach(async ({ page }) => {
    testBase = new ThermalSensorTestBase(page);
  });

  test("should display thermal section with correct structure", async ({ page }) => {
    console.log("🌡️ Testing thermal section structure...");
    
    await testBase.navigateToHomePage();
    
    // Check thermal section container
    const thermalSection = page.locator(".thermal-section");
    await expect(thermalSection).toBeVisible();
    console.log("✅ Thermal section container is visible");
    
    // Check section title
    const sectionTitle = page.locator("h4").filter({ hasText: "Thermal View" });
    await expect(sectionTitle).toBeVisible();
    await expect(sectionTitle).toContainText("Thermal View");
    console.log("✅ Thermal View title is present");
    
    // Check thermometer icon in title
    const titleIcon = sectionTitle.locator("i.fas.fa-eye");
    await expect(titleIcon).toBeVisible();
    console.log("✅ Eye icon in thermal view title is present");
  });

  test("should display heatmap container correctly", async ({ page }) => {
    console.log("🔥 Testing heatmap container...");
    
    await testBase.navigateToHomePage();
    
    // Check heatmap container
    const heatmapContainer = page.locator("#heatmapContainer");
    await expect(heatmapContainer).toBeVisible();
    console.log("✅ Heatmap container is visible");
    
    // Check container dimensions (should be 256x256 based on CSS)
    const containerBox = await heatmapContainer.boundingBox();
    expect(containerBox.width).toBe(256);
    expect(containerBox.height).toBe(256);
    console.log(`✅ Heatmap container has correct dimensions: ${containerBox.width}x${containerBox.height}`);
    
    // Check that container has proper styling
    const containerStyle = await heatmapContainer.evaluate((el) => {
      const style = window.getComputedStyle(el);
      return {
        position: style.position,
        display: style.display
      };
    });
    expect(containerStyle.position).toBe("relative");
    expect(containerStyle.display).toBe("inline-block");
    console.log("✅ Heatmap container has correct CSS styling");
  });

  test("should display temperature scale correctly", async ({ page }) => {
    console.log("📊 Testing temperature scale...");
    
    await testBase.navigateToHomePage();
    
    // Check temperature scale section
    const scaleSection = page.locator("h5").filter({ hasText: "Temperature Scale" });
    await expect(scaleSection).toBeVisible();
    await expect(scaleSection).toContainText("Temperature Scale");
    console.log("✅ Temperature Scale title is present");
    
    // Check colorbar canvas
    const colorbarCanvas = page.locator("#colorbar");
    await expect(colorbarCanvas).toBeVisible();
    console.log("✅ Colorbar canvas is visible");
    
    // Check that colorbar is a canvas element
    const canvasTag = await colorbarCanvas.evaluate((el) => el.tagName.toLowerCase());
    expect(canvasTag).toBe("canvas");
    console.log("✅ Colorbar is a canvas element");
  });

  test("should display current temperature reading", async ({ page }) => {
    console.log("🌡️ Testing current temperature reading...");
    
    await testBase.navigateToHomePage();
    
    // Check for temperature reading display
    const tempReading = page.locator(".thermal-section").locator("text").filter({ hasText: /\d+\.\d+°F/ });
    
    // Wait a bit for temperature data to load
    await page.waitForTimeout(3000);
    
    // Check if temperature reading is visible (it might not always be present)
    const isVisible = await tempReading.isVisible().catch(() => false);
    if (isVisible) {
      const tempText = await tempReading.textContent();
      expect(tempText).toMatch(/\d+\.\d+°F/);
      console.log(`✅ Current temperature reading displayed: ${tempText}`);
    } else {
      console.log("ℹ️ Temperature reading not currently visible (normal during initialization)");
    }
  });

  test("should have proper thermal section layout", async ({ page }) => {
    console.log("🎨 Testing thermal section layout...");
    
    await testBase.navigateToHomePage();
    
    // Check thermal section styling
    const thermalSection = page.locator(".thermal-section");
    const sectionStyle = await thermalSection.evaluate((el) => {
      const style = window.getComputedStyle(el);
      return {
        display: style.display,
        justifyContent: style.justifyContent,
        alignItems: style.alignItems,
        flexWrap: style.flexWrap
      };
    });
    
    expect(sectionStyle.display).toBe("flex");
    expect(sectionStyle.justifyContent).toBe("center");
    expect(sectionStyle.alignItems).toBe("center");
    expect(sectionStyle.flexWrap).toBe("wrap");
    console.log("✅ Thermal section has proper flex layout");
  });

  test("should handle heatmap initialization", async ({ page }) => {
    console.log("🔄 Testing heatmap initialization...");
    
    await testBase.navigateToHomePage();
    
    // Wait for potential heatmap initialization
    await page.waitForTimeout(5000);
    
    // Check if heatmap canvas exists inside container
    const heatmapCanvas = page.locator("#heatmapContainer canvas");
    const canvasExists = await heatmapCanvas.count();
    
    if (canvasExists > 0) {
      await expect(heatmapCanvas.first()).toBeVisible();
      console.log("✅ Heatmap canvas is initialized and visible");
      
      // Check canvas dimensions
      const canvasBox = await heatmapCanvas.first().boundingBox();
      expect(canvasBox.width).toBeGreaterThan(0);
      expect(canvasBox.height).toBeGreaterThan(0);
      console.log(`✅ Heatmap canvas has valid dimensions: ${canvasBox.width}x${canvasBox.height}`);
    } else {
      console.log("ℹ️ Heatmap canvas not yet initialized (normal during startup)");
    }
  });

  test("should display thermal data when available", async ({ page }) => {
    console.log("📡 Testing thermal data display...");
    
    await testBase.navigateToHomePage();
    
    // Wait for thermal data to potentially load
    await page.waitForTimeout(8000);
    
    // Check for any thermal data indicators
    const heatmapContainer = page.locator("#heatmapContainer");
    
    // Check if there's any visual content in the heatmap
    const hasContent = await heatmapContainer.evaluate((el) => {
      // Check if there are any child elements or canvas
      return el.children.length > 0 || el.innerHTML.trim().length > 0;
    });
    
    if (hasContent) {
      console.log("✅ Thermal data container has content");
    } else {
      console.log("ℹ️ Thermal data not yet loaded (normal during initialization)");
    }
    
    // The container should always be present regardless of data
    await expect(heatmapContainer).toBeVisible();
    console.log("✅ Heatmap container remains visible");
  });

  test("should have accessible thermal visualization", async ({ page }) => {
    console.log("♿ Testing thermal visualization accessibility...");
    
    await testBase.navigateToHomePage();
    
    // Check that thermal section has proper heading structure
    const thermalHeading = page.locator("h4").filter({ hasText: "Thermal View" });
    await expect(thermalHeading).toBeVisible();
    
    const scaleHeading = page.locator("h5").filter({ hasText: "Temperature Scale" });
    await expect(scaleHeading).toBeVisible();
    
    console.log("✅ Proper heading hierarchy for thermal visualization");
    
    // Check that heatmap container has some form of identification
    const heatmapContainer = page.locator("#heatmapContainer");
    const hasId = await heatmapContainer.getAttribute("id");
    expect(hasId).toBe("heatmapContainer");
    console.log("✅ Heatmap container has proper ID for accessibility");
  });

  test("should maintain thermal section responsiveness", async ({ page }) => {
    console.log("📱 Testing thermal section responsiveness...");
    
    await testBase.navigateToHomePage();
    
    // Test on different viewport sizes
    const viewports = [
      { width: 1200, height: 800, name: "Desktop" },
      { width: 768, height: 1024, name: "Tablet" },
      { width: 375, height: 667, name: "Mobile" }
    ];
    
    for (const viewport of viewports) {
      await page.setViewportSize({ width: viewport.width, height: viewport.height });
      await page.waitForTimeout(500); // Allow layout to adjust
      
      // Check that thermal section is still visible
      const thermalSection = page.locator(".thermal-section");
      await expect(thermalSection).toBeVisible();
      
      // Check that heatmap container is still visible
      const heatmapContainer = page.locator("#heatmapContainer");
      await expect(heatmapContainer).toBeVisible();
      
      console.log(`✅ Thermal section responsive on ${viewport.name} (${viewport.width}x${viewport.height})`);
    }
    
    // Reset to default viewport
    await page.setViewportSize({ width: 1280, height: 720 });
  });

  test("should handle thermal section interactions", async ({ page }) => {
    console.log("🖱️ Testing thermal section interactions...");
    
    await testBase.navigateToHomePage();
    
    // Test hovering over heatmap container
    const heatmapContainer = page.locator("#heatmapContainer");
    await heatmapContainer.hover();
    
    // Container should remain stable during hover
    await expect(heatmapContainer).toBeVisible();
    console.log("✅ Heatmap container handles hover interaction");
    
    // Test clicking on thermal section (should not cause errors)
    const thermalSection = page.locator(".thermal-section");
    await thermalSection.click();
    
    // Section should remain visible after click
    await expect(thermalSection).toBeVisible();
    console.log("✅ Thermal section handles click interaction");
  });
});
