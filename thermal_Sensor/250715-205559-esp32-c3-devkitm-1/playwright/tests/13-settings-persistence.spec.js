const { test, expect } = require("@playwright/test");
const { ThermalSensorTestBase } = require("./base-test");

test.describe("Settings Persistence Tests", () => {
  let testBase;

  test.beforeEach(async ({ page }) => {
    testBase = new ThermalSensorTestBase(page);
  });

  test("should save and persist essential settings after page refresh", async ({
    page,
  }) => {
    console.log("💾 Testing essential settings persistence...");

    await testBase.navigateToHomePage();

    // First, get current settings from API
    const currentSettings = await page.evaluate(async () => {
      const response = await fetch("/get-settings");
      return await response.json();
    });

    console.log("Current settings from API:", currentSettings);

    await testBase.clickSettingsTab("essential-tab");

    // Set new test values (different from current)
    const testDistanceComp = Math.min(currentSettings.distanceComp + 1.0, 19.0);
    const testAmbientTemp = Math.min(currentSettings.ambientTemp + 2.0, 79.0);
    const testPersonThreshold = Math.min(
      currentSettings.personThreshold + 1.0,
      9.0
    );

    // Change essential settings using evaluate to set range values
    const distanceCompSlider = page.locator("#distanceComp");
    await distanceCompSlider.evaluate((el, value) => {
      el.value = value;
      el.dispatchEvent(new Event("input"));
    }, testDistanceComp.toString());

    const ambientTempSlider = page.locator("#ambientTemp");
    await ambientTempSlider.evaluate((el, value) => {
      el.value = value;
      el.dispatchEvent(new Event("input"));
    }, testAmbientTemp.toString());

    const personThresholdSlider = page.locator("#personThreshold");
    await personThresholdSlider.evaluate((el, value) => {
      el.value = value;
      el.dispatchEvent(new Event("input"));
    }, testPersonThreshold.toString());

    // Save settings
    const saveButton = page
      .locator("button")
      .filter({ hasText: "Save Settings" });
    await saveButton.click();

    // Wait for save confirmation
    await page.waitForTimeout(3000);

    // Refresh page with longer timeout
    await page.reload({ waitUntil: "domcontentloaded" });
    await page.waitForTimeout(2000);

    // Navigate back to essential settings
    await testBase.clickSettingsTab("essential-tab");

    // Verify settings persisted
    const distanceCompValue = await distanceCompSlider.inputValue();
    const ambientTempValue = await ambientTempSlider.inputValue();
    const personThresholdValue = await personThresholdSlider.inputValue();

    expect(parseFloat(distanceCompValue)).toBeCloseTo(testDistanceComp, 1);
    expect(parseFloat(ambientTempValue)).toBeCloseTo(testAmbientTemp, 1);
    expect(parseFloat(personThresholdValue)).toBeCloseTo(
      testPersonThreshold,
      1
    );

    console.log("✅ Essential settings persisted correctly after page refresh");
  });

  test("should save and persist detection & alerts settings after page refresh", async ({
    page,
  }) => {
    console.log("🚨 Testing detection & alerts settings persistence...");

    await testBase.navigateToHomePage();

    // Get current settings from API
    const currentSettings = await page.evaluate(async () => {
      const response = await fetch("/get-settings");
      return await response.json();
    });

    console.log("Current detection settings from API:", currentSettings);

    await testBase.clickSettingsTab("detection-alerts-tab");

    // Set new test values (different from current) - only test feverThreshold which is in API
    const testFeverThreshold = Math.min(
      currentSettings.feverThreshold + 0.3,
      4.8
    );

    // Change detection settings - only test feverThreshold since medicalThreshold and sustainedTime are not in API
    const feverThresholdSlider = page.locator("#feverThreshold");
    await feverThresholdSlider.evaluate((el, value) => {
      el.value = value;
      el.dispatchEvent(new Event("input", { bubbles: true }));
      el.dispatchEvent(new Event("change", { bubbles: true }));
    }, testFeverThreshold.toString());

    console.log(
      `Testing fever threshold change from ${currentSettings.feverThreshold} to ${testFeverThreshold}`
    );

    // Save settings
    const saveButton = page
      .locator("button")
      .filter({ hasText: "Save Settings" });
    await saveButton.click();

    // Wait for save confirmation
    await page.waitForTimeout(2000);

    // Refresh page
    await page.reload();
    await page.waitForLoadState("domcontentloaded");

    // Navigate back to detection settings
    await testBase.clickSettingsTab("detection-alerts-tab");

    // Verify settings persisted - only test feverThreshold which is available in the API
    const feverThresholdValue = await feverThresholdSlider.inputValue();

    expect(parseFloat(feverThresholdValue)).toBeCloseTo(testFeverThreshold, 1);

    console.log(
      `✅ Fever threshold persisted: ${feverThresholdValue} (expected: ${testFeverThreshold})`
    );

    console.log(
      "✅ Detection & alerts settings persisted correctly after page refresh"
    );
  });

  test("should display alert controls settings (browser audio removed)", async ({
    page,
  }) => {
    console.log(
      "🔊 Testing alert controls settings (browser audio removed)..."
    );

    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("detection-alerts-tab");

    // Browser audio elements have been removed - ESP32 buzzer handles audio alerts
    // Verify that browser audio controls are no longer present
    const alertVolumeSlider = page.locator("#alertVolume");
    const soundAlertsCheckbox = page.locator("#soundAlerts");

    // These elements should no longer exist
    await expect(alertVolumeSlider).not.toBeVisible();
    await expect(soundAlertsCheckbox).not.toBeVisible();

    // Verify alert interval slider still exists (for ESP32 buzzer timing)
    const alertIntervalSlider = page.locator("#alertInterval");
    await expect(alertIntervalSlider).toBeVisible();

    // Verify info message about ESP32 buzzer is present
    const buzzerInfo = page
      .locator(".alert-info")
      .filter({ hasText: "ESP32 buzzer" });
    await expect(buzzerInfo).toBeVisible();

    console.log(
      "✅ Browser audio controls removed, ESP32 buzzer info displayed correctly"
    );
  });

  test("should save and persist advanced settings after page refresh", async ({
    page,
  }) => {
    console.log("⚙️ Testing advanced settings persistence...");

    await testBase.navigateToHomePage();

    // Get current settings from API
    const currentSettings = await page.evaluate(async () => {
      const response = await fetch("/get-settings");
      return await response.json();
    });

    console.log("Current advanced settings from API:", currentSettings);

    await testBase.clickSettingsTab("advanced-tab");

    // Only test displayBrightness which is available in the API
    // Other advanced settings (smoothing, baselineDuration, readInterval) are not in the API
    const testDisplayBrightness = Math.min(
      currentSettings.displayBrightness + 50,
      250
    );

    const displayBrightnessSlider = page.locator("#displayBrightness");
    await displayBrightnessSlider.evaluate((el, value) => {
      el.value = value;
      el.dispatchEvent(new Event("input", { bubbles: true }));
      el.dispatchEvent(new Event("change", { bubbles: true }));
    }, testDisplayBrightness.toString());

    console.log(
      `Testing display brightness change from ${currentSettings.displayBrightness} to ${testDisplayBrightness}`
    );

    // Save settings
    const saveButton = page
      .locator("button")
      .filter({ hasText: "Save Settings" });
    await saveButton.click();

    // Wait for save confirmation
    await page.waitForTimeout(2000);

    // Refresh page
    await page.reload();
    await page.waitForLoadState("domcontentloaded");

    // Navigate back to advanced settings
    await testBase.clickSettingsTab("advanced-tab");

    // Verify settings persisted - only test displayBrightness which is available in the API
    const displayBrightnessValue = await displayBrightnessSlider.inputValue();

    expect(parseInt(displayBrightnessValue)).toBe(testDisplayBrightness);

    console.log(
      `✅ Display brightness persisted: ${displayBrightnessValue} (expected: ${testDisplayBrightness})`
    );

    console.log("✅ Advanced settings persisted correctly after page refresh");
  });

  test("should save and persist time & schedule settings after page refresh", async ({
    page,
  }) => {
    console.log("🕐 Testing time & schedule settings persistence...");

    await testBase.navigateToHomePage();

    // Get current time settings from API
    const currentTimeSettings = await page.evaluate(async () => {
      const response = await fetch("/time-status");
      return await response.json();
    });

    console.log("Current time settings from API:", currentTimeSettings);

    await testBase.clickSettingsTab("time-tab");

    // Set new test values (different from current)
    const testNtpServer =
      currentTimeSettings.ntpServer === "time.google.com"
        ? "pool.ntp.org"
        : "time.google.com";
    const testTimezone =
      currentTimeSettings.timezone === "PST8PDT" ? "EST5EDT" : "PST8PDT";

    // Change time settings
    const ntpServerInput = page.locator("#ntpServer");
    await ntpServerInput.fill(testNtpServer);

    const timezoneSelect = page.locator("#timezone");
    await timezoneSelect.selectOption(testTimezone);

    // Toggle checkboxes
    const autoTimeSyncCheckbox = page.locator("#autoTimeSync");
    const nightModeCheckbox = page.locator("#nightModeEnabled");
    const displayOffCheckbox = page.locator("#displayOffAtNight");

    const autoTimeSyncChecked = await autoTimeSyncCheckbox.isChecked();
    const nightModeChecked = await nightModeCheckbox.isChecked();
    const displayOffChecked = await displayOffCheckbox.isChecked();

    if (autoTimeSyncChecked) {
      await autoTimeSyncCheckbox.uncheck();
    } else {
      await autoTimeSyncCheckbox.check();
    }

    if (nightModeChecked) {
      await nightModeCheckbox.uncheck();
    } else {
      await nightModeCheckbox.check();
    }

    if (displayOffChecked) {
      await displayOffCheckbox.uncheck();
    } else {
      await displayOffCheckbox.check();
    }

    // Save settings
    const saveButton = page
      .locator("button")
      .filter({ hasText: "Save Settings" });
    await saveButton.click();

    // Wait for save confirmation
    await page.waitForTimeout(2000);

    // Refresh page
    await page.reload();
    await page.waitForLoadState("domcontentloaded");

    // Navigate back to time settings
    await testBase.clickSettingsTab("time-tab");

    // Verify settings persisted
    const ntpServerValue = await ntpServerInput.inputValue();
    const timezoneValue = await timezoneSelect.inputValue();
    const autoTimeSyncNewState = await autoTimeSyncCheckbox.isChecked();
    const nightModeNewState = await nightModeCheckbox.isChecked();
    const displayOffNewState = await displayOffCheckbox.isChecked();

    expect(ntpServerValue).toBe(testNtpServer);
    expect(timezoneValue).toBe(testTimezone);
    expect(autoTimeSyncNewState).toBe(!autoTimeSyncChecked);
    expect(nightModeNewState).toBe(!nightModeChecked);
    expect(displayOffNewState).toBe(!displayOffChecked);

    console.log(
      "✅ Time & schedule settings persisted correctly after page refresh"
    );
  });

  test("should handle settings save success notification", async ({ page }) => {
    console.log("✅ Testing settings save success notification...");

    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("essential-tab");

    // Make a small change
    const distanceCompSlider = page.locator("#distanceComp");
    await distanceCompSlider.evaluate((el, value) => {
      el.value = value;
      el.dispatchEvent(new Event("input", { bubbles: true }));
      el.dispatchEvent(new Event("change", { bubbles: true }));
    }, "4.0");

    // Save settings and look for success notification
    const saveButton = page
      .locator("button")
      .filter({ hasText: "Save Settings" });
    await saveButton.click();

    // Wait for save operation to complete
    await page.waitForTimeout(1000);

    // Verify the save button is still clickable (indicating save completed)
    await expect(saveButton).toBeEnabled();

    console.log("✅ Settings save success notification displayed correctly");
  });

  test("should reset settings to defaults correctly", async ({ page }) => {
    console.log("🔄 Testing reset to defaults functionality...");

    await testBase.navigateToHomePage();

    // Get current settings from API to know what defaults should be
    const currentSettings = await page.evaluate(async () => {
      const response = await fetch("/get-settings");
      return await response.json();
    });

    console.log("Current settings before reset:", currentSettings);

    await testBase.clickSettingsTab("essential-tab");

    // Change some settings first (make them different from defaults)
    const distanceCompSlider = page.locator("#distanceComp");
    await distanceCompSlider.evaluate((el, value) => {
      el.value = value;
      el.dispatchEvent(new Event("input", { bubbles: true }));
      el.dispatchEvent(new Event("change", { bubbles: true }));
    }, "15.0");

    const ambientTempSlider = page.locator("#ambientTemp");
    await ambientTempSlider.evaluate((el, value) => {
      el.value = value;
      el.dispatchEvent(new Event("input", { bubbles: true }));
      el.dispatchEvent(new Event("change", { bubbles: true }));
    }, "78.0");

    // Save the changes
    const saveButton = page
      .locator("button")
      .filter({ hasText: "Save Settings" });
    await saveButton.click();
    await page.waitForTimeout(2000);

    // Reset to defaults
    const resetButton = page
      .locator("button")
      .filter({ hasText: "Reset to Defaults" });
    await resetButton.click();
    await page.waitForTimeout(2000);

    // Get settings after reset to verify they returned to defaults
    const resetSettings = await page.evaluate(async () => {
      const response = await fetch("/get-settings");
      return await response.json();
    });

    console.log("Settings after reset:", resetSettings);

    // Check that values returned to defaults (should be different from what we set)
    const distanceCompValue = await distanceCompSlider.inputValue();
    const ambientTempValue = await ambientTempSlider.inputValue();

    // Values should match the reset API response (indicating reset worked)
    expect(parseFloat(distanceCompValue)).toBeCloseTo(
      resetSettings.distanceComp,
      1
    );
    expect(parseFloat(ambientTempValue)).toBeCloseTo(
      resetSettings.ambientTemp,
      1
    );

    console.log(
      `✅ Reset worked - Distance: ${distanceCompValue} (API: ${resetSettings.distanceComp}), Ambient: ${ambientTempValue} (API: ${resetSettings.ambientTemp})`
    );

    console.log("✅ Reset to defaults functionality works correctly");
  });

  test("should maintain settings consistency across all tabs", async ({
    page,
  }) => {
    console.log("🔄 Testing settings consistency across tabs...");

    await testBase.navigateToHomePage();

    // Get current settings from API to use as test values
    const currentSettings = await page.evaluate(async () => {
      const response = await fetch("/get-settings");
      return await response.json();
    });

    // Change settings in multiple tabs - only test settings that are in the API
    await testBase.clickSettingsTab("essential-tab");
    const distanceCompSlider = page.locator("#distanceComp");
    const testDistanceComp = Math.min(currentSettings.distanceComp + 1.0, 19.0);
    await distanceCompSlider.evaluate((el, value) => {
      el.value = value;
      el.dispatchEvent(new Event("input", { bubbles: true }));
      el.dispatchEvent(new Event("change", { bubbles: true }));
    }, testDistanceComp.toString());

    await testBase.clickSettingsTab("detection-alerts-tab");
    const feverThresholdSlider = page.locator("#feverThreshold");
    const testFeverThreshold = Math.min(
      currentSettings.feverThreshold + 0.2,
      4.8
    );
    await feverThresholdSlider.evaluate((el, value) => {
      el.value = value;
      el.dispatchEvent(new Event("input", { bubbles: true }));
      el.dispatchEvent(new Event("change", { bubbles: true }));
    }, testFeverThreshold.toString());

    await testBase.clickSettingsTab("advanced-tab");
    const displayBrightnessSlider = page.locator("#displayBrightness");
    const testDisplayBrightness = Math.min(
      currentSettings.displayBrightness + 20,
      250
    );
    await displayBrightnessSlider.evaluate((el, value) => {
      el.value = value;
      el.dispatchEvent(new Event("input", { bubbles: true }));
      el.dispatchEvent(new Event("change", { bubbles: true }));
    }, testDisplayBrightness.toString());

    // Save settings
    const saveButton = page
      .locator("button")
      .filter({ hasText: "Save Settings" });
    await saveButton.click();
    await page.waitForTimeout(2000);

    // Refresh and verify all settings are consistent
    await page.reload();
    await page.waitForLoadState("domcontentloaded");

    // Check essential tab
    await testBase.clickSettingsTab("essential-tab");
    const distanceCompValue = await distanceCompSlider.inputValue();
    expect(parseFloat(distanceCompValue)).toBeCloseTo(testDistanceComp, 1);

    // Check detection tab
    await testBase.clickSettingsTab("detection-alerts-tab");
    const feverThresholdValue = await feverThresholdSlider.inputValue();
    expect(parseFloat(feverThresholdValue)).toBeCloseTo(testFeverThreshold, 1);

    // Check advanced tab
    await testBase.clickSettingsTab("advanced-tab");
    const displayBrightnessValue = await displayBrightnessSlider.inputValue();
    expect(parseInt(displayBrightnessValue)).toBe(testDisplayBrightness);

    console.log(
      "✅ Settings consistency maintained across all tabs (API-persisted settings only)"
    );
  });
});
