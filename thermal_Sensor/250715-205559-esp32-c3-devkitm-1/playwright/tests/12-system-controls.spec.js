const { test, expect } = require("@playwright/test");
const { ThermalSensorTestBase } = require("./base-test");

test.describe("System Controls Tests", () => {
  let testBase;

  test.beforeEach(async ({ page }) => {
    testBase = new ThermalSensorTestBase(page);
  });

  test("should display system controls section correctly", async ({ page }) => {
    console.log("🎮 Testing system controls section...");
    
    await testBase.navigateToHomePage();
    
    // Check system controls section is visible
    const controlsSection = page.locator(".controls-section");
    await expect(controlsSection).toBeVisible();
    console.log("✅ System controls section is visible");
    
    // Check section title
    const sectionTitle = page.locator("h4").filter({ hasText: "System Controls" });
    await expect(sectionTitle).toBeVisible();
    console.log("✅ System controls title is present");
    
    // Check gamepad icon
    const gamepadIcon = sectionTitle.locator("i.fas.fa-gamepad");
    await expect(gamepadIcon).toBeVisible();
    console.log("✅ Gamepad icon is present");
  });

  test("should display reset baseline button correctly", async ({ page }) => {
    console.log("🔄 Testing reset baseline button...");
    
    await testBase.navigateToHomePage();
    
    // Check reset baseline button
    const resetButton = page.locator("button").filter({ hasText: "Reset Baseline" });
    await expect(resetButton).toBeVisible();
    await expect(resetButton).toHaveClass(/btn-primary/);
    await expect(resetButton).toHaveClass(/btn-custom/);
    await expect(resetButton).toHaveAttribute("onclick", "resetBaseline()");
    console.log("✅ Reset baseline button has correct attributes");
    
    // Check refresh icon
    const refreshIcon = resetButton.locator("i.fas.fa-refresh");
    await expect(refreshIcon).toBeVisible();
    console.log("✅ Refresh icon is present");
  });

  test("should display calibrate now button correctly", async ({ page }) => {
    console.log("🎯 Testing calibrate now button...");
    
    await testBase.navigateToHomePage();
    
    // Check calibrate now button
    const calibrateButton = page.locator("button").filter({ hasText: "Calibrate Now" });
    await expect(calibrateButton).toBeVisible();
    await expect(calibrateButton).toHaveClass(/btn-warning/);
    await expect(calibrateButton).toHaveClass(/btn-custom/);
    await expect(calibrateButton).toHaveAttribute("onclick", "calibrateNow()");
    console.log("✅ Calibrate now button has correct attributes");
    
    // Check crosshairs icon
    const crosshairsIcon = calibrateButton.locator("i.fas.fa-crosshairs");
    await expect(crosshairsIcon).toBeVisible();
    console.log("✅ Crosshairs icon is present");
  });

  test("should display test alert button correctly", async ({ page }) => {
    console.log("🔔 Testing test alert button...");
    
    await testBase.navigateToHomePage();
    
    // Check test alert button
    const testAlertButton = page.locator("button").filter({ hasText: "Test Alert" });
    await expect(testAlertButton).toBeVisible();
    await expect(testAlertButton).toHaveClass(/btn-danger/);
    await expect(testAlertButton).toHaveClass(/btn-custom/);
    await expect(testAlertButton).toHaveAttribute("onclick", "testAlert()");
    console.log("✅ Test alert button has correct attributes");
    
    // Check bell icon
    const bellIcon = testAlertButton.locator("i.fas.fa-bell");
    await expect(bellIcon).toBeVisible();
    console.log("✅ Bell icon is present");
  });

  test("should display test beep button correctly", async ({ page }) => {
    console.log("🔊 Testing test beep button...");
    
    await testBase.navigateToHomePage();
    
    // Check test beep button
    const testBeepButton = page.locator("button").filter({ hasText: "Test Beep" });
    await expect(testBeepButton).toBeVisible();
    await expect(testBeepButton).toHaveClass(/btn-success/);
    await expect(testBeepButton).toHaveClass(/btn-custom/);
    await expect(testBeepButton).toHaveAttribute("onclick", "testBackgroundNotification()");
    console.log("✅ Test beep button has correct attributes");
    
    // Check volume icon
    const volumeIcon = testBeepButton.locator("i.fas.fa-volume-up");
    await expect(volumeIcon).toBeVisible();
    console.log("✅ Volume icon is present");
  });

  test("should display download data button correctly", async ({ page }) => {
    console.log("💾 Testing download data button...");
    
    await testBase.navigateToHomePage();
    
    // Check download data button
    const downloadButton = page.locator("button").filter({ hasText: "Download Data" });
    await expect(downloadButton).toBeVisible();
    await expect(downloadButton).toHaveClass(/btn-info/);
    await expect(downloadButton).toHaveClass(/btn-custom/);
    await expect(downloadButton).toHaveAttribute("onclick", "downloadData()");
    console.log("✅ Download data button has correct attributes");
    
    // Check download icon
    const downloadIcon = downloadButton.locator("i.fas.fa-download");
    await expect(downloadIcon).toBeVisible();
    console.log("✅ Download icon is present");
  });

  test("should display settings action buttons correctly", async ({ page }) => {
    console.log("⚙️ Testing settings action buttons...");
    
    await testBase.navigateToHomePage();
    
    // Check save settings button
    const saveButton = page.locator("button").filter({ hasText: "Save Settings" });
    await expect(saveButton).toBeVisible();
    await expect(saveButton).toHaveClass(/btn-success/);
    await expect(saveButton).toHaveClass(/btn-custom/);
    await expect(saveButton).toHaveAttribute("onclick", "saveSettings()");
    console.log("✅ Save settings button has correct attributes");
    
    // Check reset to defaults button
    const resetDefaultsButton = page.locator("button").filter({ hasText: "Reset to Defaults" });
    await expect(resetDefaultsButton).toBeVisible();
    await expect(resetDefaultsButton).toHaveClass(/btn-warning/);
    await expect(resetDefaultsButton).toHaveClass(/btn-custom/);
    await expect(resetDefaultsButton).toHaveAttribute("onclick", "resetSettings()");
    console.log("✅ Reset to defaults button has correct attributes");
    
    // Check export config button
    const exportButton = page.locator("button").filter({ hasText: "Export Config" });
    await expect(exportButton).toBeVisible();
    await expect(exportButton).toHaveClass(/btn-info/);
    await expect(exportButton).toHaveClass(/btn-custom/);
    await expect(exportButton).toHaveAttribute("onclick", "exportSettings()");
    console.log("✅ Export config button has correct attributes");
    
    // Check import config button
    const importButton = page.locator("button").filter({ hasText: "Import Config" });
    await expect(importButton).toBeVisible();
    await expect(importButton).toHaveClass(/btn-secondary/);
    await expect(importButton).toHaveClass(/btn-custom/);
    await expect(importButton).toHaveAttribute("onclick", "importSettings()");
    console.log("✅ Import config button has correct attributes");
  });

  test("should handle reset baseline button click", async ({ page }) => {
    console.log("🔄 Testing reset baseline button interaction...");
    
    await testBase.navigateToHomePage();
    
    // Set up dialog handler for confirmation
    page.on('dialog', async dialog => {
      expect(dialog.type()).toBe('confirm');
      expect(dialog.message()).toContain('reset the baseline');
      await dialog.dismiss(); // Dismiss to avoid actual reset
    });
    
    // Click reset baseline button
    const resetButton = page.locator("button").filter({ hasText: "Reset Baseline" });
    await resetButton.click();
    
    console.log("✅ Reset baseline button click handled correctly");
  });

  test("should handle calibrate now button click", async ({ page }) => {
    console.log("🎯 Testing calibrate now button interaction...");
    
    await testBase.navigateToHomePage();
    
    // Click calibrate now button
    const calibrateButton = page.locator("button").filter({ hasText: "Calibrate Now" });
    await calibrateButton.click();
    
    // Wait a moment for any potential response
    await page.waitForTimeout(1000);
    
    console.log("✅ Calibrate now button click handled correctly");
  });

  test("should handle test alert button click", async ({ page }) => {
    console.log("🔔 Testing test alert button interaction...");
    
    await testBase.navigateToHomePage();
    
    // Click test alert button
    const testAlertButton = page.locator("button").filter({ hasText: "Test Alert" });
    await testAlertButton.click();
    
    // Wait a moment for any potential response
    await page.waitForTimeout(1000);
    
    console.log("✅ Test alert button click handled correctly");
  });

  test("should handle test beep button click", async ({ page }) => {
    console.log("🔊 Testing test beep button interaction...");
    
    await testBase.navigateToHomePage();
    
    // Click test beep button
    const testBeepButton = page.locator("button").filter({ hasText: "Test Beep" });
    await testBeepButton.click();
    
    // Wait a moment for any potential response
    await page.waitForTimeout(1000);
    
    console.log("✅ Test beep button click handled correctly");
  });

  test("should handle download data button click", async ({ page }) => {
    console.log("💾 Testing download data button interaction...");
    
    await testBase.navigateToHomePage();
    
    // Set up download handler
    const downloadPromise = page.waitForEvent('download', { timeout: 5000 }).catch(() => null);
    
    // Click download data button
    const downloadButton = page.locator("button").filter({ hasText: "Download Data" });
    await downloadButton.click();
    
    // Wait for potential download or timeout
    const download = await downloadPromise;
    
    if (download) {
      console.log("✅ Download initiated successfully");
    } else {
      console.log("✅ Download button click handled (no download in test environment)");
    }
  });

  test("should have proper system controls structure", async ({ page }) => {
    console.log("🏗️ Testing system controls structure...");
    
    await testBase.navigateToHomePage();
    
    // Check that all system control buttons are present
    const systemControlButtons = [
      "Reset Baseline",
      "Calibrate Now", 
      "Test Alert",
      "Test Beep",
      "Download Data"
    ];
    
    for (const buttonText of systemControlButtons) {
      const button = page.locator("button").filter({ hasText: buttonText });
      await expect(button).toBeVisible();
      console.log(`✅ Found system control button: ${buttonText}`);
    }
    
    // Check that all settings action buttons are present
    const settingsActionButtons = [
      "Save Settings",
      "Reset to Defaults",
      "Export Config", 
      "Import Config"
    ];
    
    for (const buttonText of settingsActionButtons) {
      const button = page.locator("button").filter({ hasText: buttonText });
      await expect(button).toBeVisible();
      console.log(`✅ Found settings action button: ${buttonText}`);
    }
    
    console.log("✅ All system controls are properly structured");
  });
});
