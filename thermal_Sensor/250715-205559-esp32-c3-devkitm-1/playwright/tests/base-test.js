const { expect } = require("@playwright/test");

/**
 * Base test utilities for thermal sensor web interface testing
 */
class ThermalSensorTestBase {
  constructor(page) {
    this.page = page;
  }

  /**
   * Navigate to the thermal sensor home page and wait for it to load
   */
  async navigateToHomePage() {
    await this.page.goto("/", { waitUntil: "domcontentloaded" });
    // Wait for the main container to be visible (this indicates the page structure is loaded)
    await this.page.waitForSelector(".main-container", { timeout: 15000 });
    // Wait a bit more for JavaScript to initialize
    await this.page.waitForTimeout(2000);
  }

  /**
   * Wait for the page to be fully loaded with all essential elements
   */
  async waitForPageLoad() {
    // Wait for header section
    await this.page.waitForSelector(".header-section", { timeout: 10000 });

    // Wait for stats grid
    await this.page.waitForSelector(".stats-grid", { timeout: 10000 });

    // Wait for settings panel
    await this.page.waitForSelector(".settings-panel", { timeout: 10000 });

    // Wait for controls section
    await this.page.waitForSelector(".controls-section", { timeout: 10000 });
  }

  /**
   * Click on a settings tab and wait for it to be active
   */
  async clickSettingsTab(tabId) {
    await this.page.click(`#${tabId}`);
    await this.page.waitForTimeout(500); // Allow tab transition

    // Verify tab is active
    const tabElement = this.page.locator(`#${tabId}`);
    await expect(tabElement).toHaveClass(/active/);
  }

  /**
   * Get the value of a range input and its display
   */
  async getRangeInputValue(inputId, displayId) {
    const inputValue = await this.page.inputValue(`#${inputId}`);
    const displayValue = await this.page.textContent(`#${displayId}`);
    return { inputValue: parseFloat(inputValue), displayValue };
  }

  /**
   * Set a range input value and verify it updates
   */
  async setRangeInputValue(inputId, displayId, value) {
    await this.page.fill(`#${inputId}`, value.toString());
    await this.page.waitForTimeout(100); // Allow UI update

    const result = await this.getRangeInputValue(inputId, displayId);
    return result;
  }

  /**
   * Check if an element is visible
   */
  async isElementVisible(selector) {
    try {
      await this.page.waitForSelector(selector, { timeout: 1000 });
      return await this.page.isVisible(selector);
    } catch {
      return false;
    }
  }

  /**
   * Get all visible stat cards and their values
   */
  async getStatCards() {
    const statCards = await this.page.locator(".stat-card").all();
    const stats = [];

    for (const card of statCards) {
      const label = await card.locator(".stat-label").textContent();
      const value = await card.locator(".stat-value").textContent();
      const unit = await card.locator(".stat-unit").textContent();
      stats.push({
        label: label.trim(),
        value: value.trim(),
        unit: unit.trim(),
      });
    }

    return stats;
  }

  /**
   * Save settings and wait for completion
   */
  async saveSettings() {
    await this.page.click('button:has-text("Save Settings")');
    await this.page.waitForTimeout(2000); // Allow save operation
  }

  /**
   * Reset settings to defaults
   */
  async resetSettings() {
    await this.page.click('button:has-text("Reset to Defaults")');
    await this.page.waitForTimeout(2000); // Allow reset operation
  }

  /**
   * Check if the page shows connection status
   */
  async getConnectionStatus() {
    return await this.page.textContent("#connectionStatus");
  }

  /**
   * Get device time and date
   */
  async getDeviceTimeInfo() {
    const time = await this.page.textContent("#deviceTime");
    const date = await this.page.textContent("#deviceDate");
    const status = await this.page.textContent("#timeStatus");
    return { time, date, status };
  }

  /**
   * Check if fever alert is visible
   */
  async isFeverAlertVisible() {
    return await this.isElementVisible("#feverAlert");
  }

  /**
   * Get current status display
   */
  async getCurrentStatus() {
    return await this.page.textContent("#currentStatus");
  }

  /**
   * Take a screenshot with a descriptive name
   */
  async takeScreenshot(name) {
    await this.page.screenshot({
      path: `test-results/screenshots/${name}-${Date.now()}.png`,
      fullPage: true,
    });
  }

  /**
   * Fetch current settings from the API instead of using hardcoded values
   */
  async getCurrentSettings() {
    try {
      const response = await this.page.evaluate(async () => {
        const response = await fetch("/get-settings");
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return await response.json();
      });
      return response;
    } catch (error) {
      console.error("Failed to fetch current settings:", error);
      // Return default values as fallback
      return {
        feverThreshold: 2.0,
        distanceComp: 4.5,
        ambientTemp: 72.0,
        personThreshold: 4.0,
        baselineMin: 90.0,
        displayBrightness: 255,
      };
    }
  }

  /**
   * Fetch current fever status from the API
   */
  async getCurrentFeverStatus() {
    try {
      const response = await this.page.evaluate(async () => {
        const response = await fetch("/fever-status");
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return await response.json();
      });
      return response;
    } catch (error) {
      console.error("Failed to fetch current fever status:", error);
      // Return default values as fallback
      return {
        currentTemp: 0.0,
        baselineTemp: 0.0,
        feverDetected: false,
        baselineEstablished: false,
        status: "Unknown",
        feverDuration: 0,
        ambient: {
          available: false,
          temperature: null,
          temperatureF: null,
          humidity: null,
          lastUpdate: null,
        },
      };
    }
  }
}

module.exports = { ThermalSensorTestBase };
