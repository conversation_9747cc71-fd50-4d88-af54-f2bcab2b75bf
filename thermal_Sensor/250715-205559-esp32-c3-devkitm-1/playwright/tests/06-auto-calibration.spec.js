const { test, expect } = require("@playwright/test");
const { ThermalSensorTestBase } = require("./base-test");

test.describe("Auto Calibration Tests", () => {
  let testBase;

  test.beforeEach(async ({ page }) => {
    testBase = new ThermalSensorTestBase(page);
  });

  test("should display auto calibration tab content correctly", async ({
    page,
  }) => {
    console.log("🎯 Testing auto calibration tab content...");

    await testBase.navigateToHomePage();

    // Auto calibration should be active by default
    const autoCalibContent = page.locator("#auto-calibrate");
    await expect(autoCalibContent).toBeVisible();
    await expect(autoCalibContent).toHaveClass(/show/);
    await expect(autoCalibContent).toHaveClass(/active/);
    console.log("✅ Auto calibration content is visible and active");

    // Check info alert
    const infoAlert = autoCalibContent.locator(".alert.alert-info");
    await expect(infoAlert).toBeVisible();
    await expect(infoAlert).toContainText("Auto Calibration:");
    console.log("✅ Auto calibration info alert is present");

    // Check info icon in alert
    const alertIcon = infoAlert.locator("i.fas.fa-info-circle");
    await expect(alertIcon).toBeVisible();
    console.log("✅ Info circle icon is present in alert");
  });

  test("should display body temperature input correctly", async ({ page }) => {
    console.log("🌡️ Testing body temperature input...");

    await testBase.navigateToHomePage();

    // Check body temperature input
    const bodyTempInput = page.locator("#childBodyTemp");
    await expect(bodyTempInput).toBeVisible();
    await expect(bodyTempInput).toHaveAttribute("type", "number");
    await expect(bodyTempInput).toHaveAttribute("step", "0.1");
    await expect(bodyTempInput).toHaveAttribute("min", "95.0");
    await expect(bodyTempInput).toHaveAttribute("max", "105.0");
    console.log("✅ Body temperature input has correct attributes");

    // Check label (not using for attribute, just text content)
    const bodyTempLabel = page
      .locator("label")
      .filter({ hasText: "Child's Current Body Temperature" });
    await expect(bodyTempLabel).toBeVisible();
    console.log("✅ Body temperature label is correct");

    // Check child icon
    const childIcon = bodyTempLabel.locator("i.fas.fa-child");
    await expect(childIcon).toBeVisible();
    console.log("✅ Child icon is present");
  });

  test("should display ambient temperature input correctly", async ({
    page,
  }) => {
    console.log("🏠 Testing ambient temperature input...");

    await testBase.navigateToHomePage();

    // Check ambient temperature input
    const ambientTempInput = page.locator("#ambientRoomTemp");
    await expect(ambientTempInput).toBeVisible();
    await expect(ambientTempInput).toHaveAttribute("type", "number");
    await expect(ambientTempInput).toHaveAttribute("step", "0.5");
    await expect(ambientTempInput).toHaveAttribute("min", "60.0");
    await expect(ambientTempInput).toHaveAttribute("max", "85.0");
    console.log("✅ Ambient temperature input has correct attributes");

    // Check label
    const ambientTempLabel = page
      .locator("label")
      .filter({ hasText: "Current Ambient Room Temperature" });
    await expect(ambientTempLabel).toBeVisible();
    console.log("✅ Ambient temperature label is correct");

    // Check home icon
    const homeIcon = ambientTempLabel.locator("i.fas.fa-home");
    await expect(homeIcon).toBeVisible();
    console.log("✅ Home icon is present");
  });

  test("should display calibration preview section", async ({ page }) => {
    console.log("👁️ Testing calibration preview section...");

    await testBase.navigateToHomePage();

    // Check calculated settings section
    const calculatedSection = page
      .locator("h6")
      .filter({ hasText: "Calculated Settings" });
    await expect(calculatedSection).toBeVisible();
    console.log("✅ Calculated settings section is visible");

    // Check calculator icon in title
    const calculatorIcon = calculatedSection.locator("i.fas.fa-calculator");
    await expect(calculatorIcon).toBeVisible();
    console.log("✅ Calculator icon is present in title");
  });

  test("should display calibration preview values", async ({ page }) => {
    console.log("📊 Testing calibration preview values...");

    await testBase.navigateToHomePage();

    // Check distance compensation preview
    const distanceCompPreview = page.locator("#calculatedDistanceComp");
    await expect(distanceCompPreview).toBeVisible();
    console.log("✅ Distance compensation preview is visible");

    // Check baseline min preview
    const baselineMinPreview = page.locator("#calculatedBaselineMin");
    await expect(baselineMinPreview).toBeVisible();
    console.log("✅ Baseline minimum preview is visible");

    // Check ambient temp preview
    const ambientTempPreview = page.locator("#calculatedAmbientTemp");
    await expect(ambientTempPreview).toBeVisible();
    console.log("✅ Ambient temperature preview is visible");

    // Check preview labels
    const previewLabels = [
      "Distance Compensation:",
      "Baseline Minimum:",
      "Ambient Temperature:",
    ];

    for (const labelText of previewLabels) {
      const label = page.locator("strong").filter({ hasText: labelText });
      await expect(label).toBeVisible();
      console.log(`✅ Found preview label: ${labelText}`);
    }
  });

  test("should calculate calibration values when inputs are provided", async ({
    page,
  }) => {
    console.log("🧮 Testing calibration calculations...");

    await testBase.navigateToHomePage();

    // Get current fever status to use realistic values
    const currentStatus = await testBase.getCurrentFeverStatus();
    console.log("Current fever status from API:", currentStatus);

    // Use realistic body temperature (normal range 97-99°F)
    const bodyTemp =
      currentStatus.currentTemp > 0 ? currentStatus.currentTemp : 98.6;
    const bodyTempInput = page.locator("#childBodyTemp");
    await bodyTempInput.fill(bodyTemp.toString());
    console.log(`✅ Entered body temperature: ${bodyTemp}°F`);

    // Use ambient temperature from AHT10 sensor if available, otherwise use a reasonable default
    const ambientTemp =
      currentStatus.ambient.available && currentStatus.ambient.temperatureF
        ? currentStatus.ambient.temperatureF
        : 72.0;
    const ambientTempInput = page.locator("#ambientRoomTemp");
    await ambientTempInput.fill(ambientTemp.toString());
    console.log(`✅ Set ambient temperature: ${ambientTemp}°F`);

    // Wait for calculations to update
    await page.waitForTimeout(1000);

    // Check that preview values are updated (should not be empty)
    const distanceCompPreview = page.locator("#calculatedDistanceComp");
    const distanceCompValue = await distanceCompPreview.textContent();
    expect(distanceCompValue.trim()).not.toBe("");
    expect(distanceCompValue.trim()).not.toBe("--");
    console.log(`✅ Distance compensation calculated: ${distanceCompValue}`);

    const baselineMinPreview = page.locator("#calculatedBaselineMin");
    const baselineMinValue = await baselineMinPreview.textContent();
    expect(baselineMinValue.trim()).not.toBe("");
    expect(baselineMinValue.trim()).not.toBe("--");
    console.log(`✅ Baseline minimum calculated: ${baselineMinValue}`);
  });

  test("should display calibrate button correctly", async ({ page }) => {
    console.log("🎯 Testing calibrate button...");

    await testBase.navigateToHomePage();

    // Check calibrate button
    const calibrateButton = page.locator("#performAutoCalibration");
    await expect(calibrateButton).toBeVisible();
    await expect(calibrateButton).toHaveClass(/btn/);
    await expect(calibrateButton).toHaveClass(/btn-primary/);
    await expect(calibrateButton).toContainText("Calculate Auto Calibration");
    console.log("✅ Calibrate button is visible with correct styling");

    // Check magic icon in button
    const magicIcon = calibrateButton.locator("i.fas.fa-magic");
    await expect(magicIcon).toBeVisible();
    console.log("✅ Magic icon is present in calibrate button");

    // Check preview button
    const previewButton = page.locator("#previewCalculations");
    await expect(previewButton).toBeVisible();
    await expect(previewButton).toHaveClass(/btn/);
    await expect(previewButton).toHaveClass(/btn-secondary/);
    await expect(previewButton).toContainText("Preview Calculations");
    console.log("✅ Preview button is visible with correct styling");
  });

  test("should validate input ranges", async ({ page }) => {
    console.log("✅ Testing input validation...");

    await testBase.navigateToHomePage();

    // Test body temperature validation
    const bodyTempInput = page.locator("#childBodyTemp");

    // Test minimum value
    await bodyTempInput.fill("90");
    const bodyTempValue1 = await bodyTempInput.inputValue();
    console.log(`Body temp input accepts: ${bodyTempValue1}`);

    // Test maximum value
    await bodyTempInput.fill("115");
    const bodyTempValue2 = await bodyTempInput.inputValue();
    console.log(`Body temp input accepts: ${bodyTempValue2}`);

    // Test valid value
    await bodyTempInput.fill("98.6");
    const bodyTempValue3 = await bodyTempInput.inputValue();
    expect(bodyTempValue3).toBe("98.6");
    console.log("✅ Body temperature input accepts valid values");

    // Test ambient temperature validation
    const ambientTempInput = page.locator("#ambientRoomTemp");

    // Test valid value
    await ambientTempInput.fill("72.5");
    const ambientTempValue = await ambientTempInput.inputValue();
    expect(ambientTempValue).toBe("72.5");
    console.log("✅ Ambient temperature input accepts valid values");
  });

  test("should handle calibration button interaction", async ({ page }) => {
    console.log("🔄 Testing calibration button interaction...");

    await testBase.navigateToHomePage();

    // Get current fever status to use realistic values
    const currentStatus = await testBase.getCurrentFeverStatus();

    // Use realistic body temperature
    const bodyTemp =
      currentStatus.currentTemp > 0 ? currentStatus.currentTemp : 98.6;

    // Use ambient temperature from sensor if available
    const ambientTemp =
      currentStatus.ambient.available && currentStatus.ambient.temperatureF
        ? currentStatus.ambient.temperatureF
        : 72.0;

    // Fill in required values
    await page.locator("#childBodyTemp").fill(bodyTemp.toString());
    await page.locator("#ambientRoomTemp").fill(ambientTemp.toString());
    await page.waitForTimeout(500);

    console.log(
      `✅ Filled body temp: ${bodyTemp}°F, ambient temp: ${ambientTemp}°F`
    );

    // Click calibrate button
    const calibrateButton = page.locator("#performAutoCalibration");
    await calibrateButton.click();
    console.log("✅ Clicked calibrate button");

    // Wait for any potential response
    await page.waitForTimeout(2000);

    // Button should remain visible (no errors thrown)
    await expect(calibrateButton).toBeVisible();
    console.log("✅ Calibration button interaction completed successfully");
  });

  test("should have proper form structure and accessibility", async ({
    page,
  }) => {
    console.log("♿ Testing form accessibility...");

    await testBase.navigateToHomePage();

    // Check form structure
    const autoCalibForm = page.locator("#auto-calibrate .row").first();
    await expect(autoCalibForm).toBeVisible();
    console.log("✅ Form structure is present");

    // Check that inputs exist and are accessible
    const bodyTempInput = page.locator("#childBodyTemp");
    await expect(bodyTempInput).toBeVisible();
    const bodyTempInputId = await bodyTempInput.getAttribute("id");
    expect(bodyTempInputId).toBe("childBodyTemp");
    console.log("✅ Body temperature input has proper ID");

    const ambientTempInput = page.locator("#ambientRoomTemp");
    await expect(ambientTempInput).toBeVisible();
    const ambientTempInputId = await ambientTempInput.getAttribute("id");
    expect(ambientTempInputId).toBe("ambientRoomTemp");
    console.log("✅ Ambient temperature input has proper ID");
  });

  test("should update preview in real-time", async ({ page }) => {
    console.log("⚡ Testing real-time preview updates...");

    await testBase.navigateToHomePage();

    // Get initial preview values
    const distanceCompPreview = page.locator("#calculatedDistanceComp");
    const baselineMinPreview = page.locator("#calculatedBaselineMin");

    const initialDistanceComp = await distanceCompPreview.textContent();
    const initialBaselineMin = await baselineMinPreview.textContent();
    console.log(
      `Initial values - Distance: ${initialDistanceComp}, Baseline: ${initialBaselineMin}`
    );

    // Get current fever status to use realistic values
    const currentStatus = await testBase.getCurrentFeverStatus();

    // Use realistic body temperature (slightly elevated for testing)
    const bodyTemp =
      currentStatus.currentTemp > 0 ? currentStatus.currentTemp + 0.4 : 99.0;

    // Use ambient temperature from sensor if available
    const ambientTemp =
      currentStatus.ambient.available && currentStatus.ambient.temperatureF
        ? Math.max(currentStatus.ambient.temperatureF - 2, 68.0) // Slightly lower for testing
        : 70.0;

    // Enter first value
    await page.locator("#childBodyTemp").fill(bodyTemp.toString());
    await page.waitForTimeout(500);

    // Enter second value
    await page.locator("#ambientRoomTemp").fill(ambientTemp.toString());
    await page.waitForTimeout(500);

    // Check that values have updated
    const updatedDistanceComp = await distanceCompPreview.textContent();
    const updatedBaselineMin = await baselineMinPreview.textContent();
    console.log(
      `Updated values - Distance: ${updatedDistanceComp}, Baseline: ${updatedBaselineMin}`
    );

    // Values should be different from initial (assuming they were empty/default)
    if (
      initialDistanceComp.trim() === "" ||
      initialDistanceComp.includes("--")
    ) {
      expect(updatedDistanceComp.trim()).not.toBe("");
      expect(updatedDistanceComp.trim()).not.toBe("--");
      console.log("✅ Distance compensation preview updated");
    }

    if (initialBaselineMin.trim() === "" || initialBaselineMin.includes("--")) {
      expect(updatedBaselineMin.trim()).not.toBe("");
      expect(updatedBaselineMin.trim()).not.toBe("--");
      console.log("✅ Baseline minimum preview updated");
    }
  });
});
