const { test, expect } = require("@playwright/test");
const { ThermalSensorTestBase } = require("./base-test");

test.describe("Essential Settings Tests", () => {
  let testBase;

  test.beforeEach(async ({ page }) => {
    testBase = new ThermalSensorTestBase(page);
  });

  test("should display essential settings tab content correctly", async ({
    page,
  }) => {
    console.log("⚙️ Testing essential settings tab content...");

    await testBase.navigateToHomePage();

    // Navigate to Essential Settings tab
    await testBase.clickSettingsTab("essential-tab");

    // Check essential settings content is visible
    const essentialContent = page.locator("#essential");
    await expect(essentialContent).toBeVisible();
    await expect(essentialContent).toHaveClass(/show/);
    await expect(essentialContent).toHaveClass(/active/);
    console.log("✅ Essential settings content is visible and active");
  });

  test("should display distance compensation control correctly", async ({
    page,
  }) => {
    console.log("📏 Testing distance compensation control...");

    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("essential-tab");

    // Check distance compensation input
    const distanceCompInput = page.locator("#distanceComp");
    await expect(distanceCompInput).toBeVisible();
    await expect(distanceCompInput).toHaveAttribute("type", "range");
    await expect(distanceCompInput).toHaveAttribute("min", "0");
    await expect(distanceCompInput).toHaveAttribute("max", "20");
    await expect(distanceCompInput).toHaveAttribute("step", "0.1");
    console.log("✅ Distance compensation input has correct attributes");

    // Check label
    const distanceLabel = page
      .locator("label")
      .filter({ hasText: "Distance Compensation" });
    await expect(distanceLabel).toBeVisible();
    console.log("✅ Distance compensation label is present");

    // Check ruler icon
    const rulerIcon = distanceLabel.locator("i.fas.fa-ruler");
    await expect(rulerIcon).toBeVisible();
    console.log("✅ Ruler icon is present");

    // Check value display
    const distanceValue = page.locator("#distanceCompValue");
    await expect(distanceValue).toBeVisible();
    console.log("✅ Distance compensation value display is present");
  });

  test("should display ambient temperature control correctly", async ({
    page,
  }) => {
    console.log("🌡️ Testing ambient temperature control...");

    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("essential-tab");

    // Check ambient temperature input
    const ambientTempInput = page.locator("#ambientTemp");
    await expect(ambientTempInput).toBeVisible();
    await expect(ambientTempInput).toHaveAttribute("type", "range");
    await expect(ambientTempInput).toHaveAttribute("min", "65");
    await expect(ambientTempInput).toHaveAttribute("max", "85");
    await expect(ambientTempInput).toHaveAttribute("step", "0.5");
    console.log("✅ Ambient temperature input has correct attributes");

    // Check label
    const ambientLabel = page
      .locator("label")
      .filter({ hasText: "Ambient Temperature" });
    await expect(ambientLabel).toBeVisible();
    console.log("✅ Ambient temperature label is present");

    // Check home icon
    const homeIcon = ambientLabel.locator("i.fas.fa-home");
    await expect(homeIcon).toBeVisible();
    console.log("✅ Home icon is present");

    // Check value display
    const ambientValue = page.locator("#ambientTempValue");
    await expect(ambientValue).toBeVisible();
    console.log("✅ Ambient temperature value display is present");
  });

  test("should display person detection threshold control correctly", async ({
    page,
  }) => {
    console.log("👤 Testing person detection threshold control...");

    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("essential-tab");

    // Check person detection threshold slider
    const personDetectionInput = page.locator("#personThreshold");
    await expect(personDetectionInput).toBeVisible();
    await expect(personDetectionInput).toHaveAttribute("type", "range");
    console.log("✅ Person detection threshold slider has correct attributes");

    // Check label
    const personLabel = page
      .locator("label")
      .filter({ hasText: "Person Detection Threshold" });
    await expect(personLabel).toBeVisible();
    console.log("✅ Person detection threshold label is present");

    // Check user-check icon
    const userIcon = personLabel.locator("i.fas.fa-user-check");
    await expect(userIcon).toBeVisible();
    console.log("✅ User-check icon is present");

    // Check value display
    const personValue = page.locator("#personThresholdValue");
    await expect(personValue).toBeVisible();
    console.log("✅ Person detection threshold value display is present");
  });

  test("should display baseline minimum threshold control correctly", async ({
    page,
  }) => {
    console.log("📊 Testing baseline minimum threshold control...");

    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("essential-tab");

    // Check baseline min threshold input
    const baselineMinInput = page.locator("#baselineMin");
    await expect(baselineMinInput).toBeVisible();
    await expect(baselineMinInput).toHaveAttribute("type", "range");
    await expect(baselineMinInput).toHaveAttribute("min", "85");
    await expect(baselineMinInput).toHaveAttribute("max", "99");
    await expect(baselineMinInput).toHaveAttribute("step", "0.5");
    console.log("✅ Baseline minimum threshold input has correct attributes");

    // Check label
    const baselineLabel = page
      .locator("label")
      .filter({ hasText: "Baseline Min Threshold" });
    await expect(baselineLabel).toBeVisible();
    console.log("✅ Baseline minimum threshold label is present");

    // Check user-check icon
    const userCheckIcon = baselineLabel.locator("i.fas.fa-user-check");
    await expect(userCheckIcon).toBeVisible();
    console.log("✅ User-check icon is present");

    // Check value display
    const baselineValue = page.locator("#baselineMinValue");
    await expect(baselineValue).toBeVisible();
    console.log("✅ Baseline minimum threshold value display is present");
  });

  test("should update distance compensation value when slider changes", async ({
    page,
  }) => {
    console.log("🔄 Testing distance compensation value updates...");

    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("essential-tab");

    // Get current settings from API
    const currentSettings = await testBase.getCurrentSettings();
    console.log(
      `Current distance compensation from API: ${currentSettings.distanceComp}`
    );

    // Get initial value from UI
    const distanceValue = page.locator("#distanceCompValue");
    const initialValue = await distanceValue.textContent();
    console.log(`Initial distance compensation from UI: ${initialValue}`);

    // Choose a test value different from current
    const testValue =
      currentSettings.distanceComp < 15
        ? currentSettings.distanceComp + 1
        : currentSettings.distanceComp - 1;

    // Change slider value
    const distanceSlider = page.locator("#distanceComp");
    await distanceSlider.fill(testValue.toString());
    await page.waitForTimeout(500);

    // Check that value updated
    const updatedValue = await distanceValue.textContent();
    console.log(`Updated distance compensation: ${updatedValue}`);
    expect(updatedValue).toContain(testValue.toFixed(1));
    console.log("✅ Distance compensation value updates correctly");
  });

  test("should update ambient temperature value when slider changes", async ({
    page,
  }) => {
    console.log("🌡️ Testing ambient temperature value updates...");

    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("essential-tab");

    // Get current settings from API
    const currentSettings = await testBase.getCurrentSettings();
    console.log(
      `Current ambient temperature from API: ${currentSettings.ambientTemp}`
    );

    // Get initial value from UI
    const ambientValue = page.locator("#ambientTempValue");
    const initialValue = await ambientValue.textContent();
    console.log(`Initial ambient temperature from UI: ${initialValue}`);

    // Choose a test value different from current (within valid range 65-85)
    const testValue =
      currentSettings.ambientTemp < 80
        ? currentSettings.ambientTemp + 2
        : currentSettings.ambientTemp - 2;

    // Change slider value
    const ambientSlider = page.locator("#ambientTemp");
    await ambientSlider.fill(testValue.toString());
    await page.waitForTimeout(500);

    // Check that value updated
    const updatedValue = await ambientValue.textContent();
    console.log(`Updated ambient temperature: ${updatedValue}`);
    expect(updatedValue).toContain(testValue.toString());
    console.log("✅ Ambient temperature value updates correctly");
  });

  test("should update person detection threshold value when slider changes", async ({
    page,
  }) => {
    console.log("✅ Testing person detection threshold value updates...");

    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("essential-tab");

    // Get current settings from API
    const currentSettings = await testBase.getCurrentSettings();
    console.log(
      `Current person detection threshold from API: ${currentSettings.personThreshold}`
    );

    // Get initial value from UI
    const personValue = page.locator("#personThresholdValue");
    const initialValue = await personValue.textContent();
    console.log(`Initial person detection threshold from UI: ${initialValue}`);

    // Choose a test value different from current (within valid range 1-10)
    const testValue =
      currentSettings.personThreshold < 8
        ? currentSettings.personThreshold + 1
        : currentSettings.personThreshold - 1;

    // Change slider value
    const personSlider = page.locator("#personThreshold");
    await personSlider.fill(testValue.toString());
    await page.waitForTimeout(500);

    // Check that value updated
    const updatedValue = await personValue.textContent();
    console.log(`Updated person detection threshold: ${updatedValue}`);
    expect(updatedValue).toContain(testValue.toString());
    console.log("✅ Person detection threshold value updates correctly");
  });

  test("should update baseline minimum threshold value when slider changes", async ({
    page,
  }) => {
    console.log("📊 Testing baseline minimum threshold value updates...");

    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("essential-tab");

    // Get current settings from API
    const currentSettings = await testBase.getCurrentSettings();
    console.log(
      `Current baseline minimum threshold from API: ${currentSettings.baselineMin}`
    );

    // Get initial value from UI
    const baselineValue = page.locator("#baselineMinValue");
    const initialValue = await baselineValue.textContent();
    console.log(`Initial baseline minimum threshold from UI: ${initialValue}`);

    // Choose a test value different from current (within valid range 85-99)
    const testValue =
      currentSettings.baselineMin < 95
        ? currentSettings.baselineMin + 2
        : currentSettings.baselineMin - 2;

    // Change slider value
    const baselineSlider = page.locator("#baselineMin");
    await baselineSlider.fill(testValue.toString());
    await page.waitForTimeout(500);

    // Check that value updated
    const updatedValue = await baselineValue.textContent();
    console.log(`Updated baseline minimum threshold: ${updatedValue}`);
    expect(updatedValue).toContain(testValue.toString());
    console.log("✅ Baseline minimum threshold value updates correctly");
  });

  test("should have proper essential settings structure", async ({ page }) => {
    console.log("🏗️ Testing essential settings structure...");

    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("essential-tab");

    // Check that all main sections exist
    const sections = [
      "Distance Compensation",
      "Ambient Temperature",
      "Person Detection Threshold",
      "Baseline Min Threshold",
    ];

    for (const sectionName of sections) {
      const section = page.locator("label").filter({ hasText: sectionName });
      await expect(section).toBeVisible();
      console.log(`✅ Found section: ${sectionName}`);
    }

    // Check that all controls are within the essential tab content
    const essentialContent = page.locator("#essential");
    const distanceControl = essentialContent.locator("#distanceComp");
    const ambientControl = essentialContent.locator("#ambientTemp");
    const personControl = essentialContent.locator("#personThreshold");
    const baselineControl = essentialContent.locator("#baselineMin");

    await expect(distanceControl).toBeVisible();
    await expect(ambientControl).toBeVisible();
    await expect(personControl).toBeVisible();
    await expect(baselineControl).toBeVisible();

    console.log("✅ All essential settings controls are properly contained");
  });

  test("should maintain settings values during tab navigation", async ({
    page,
  }) => {
    console.log("🔒 Testing settings persistence during navigation...");

    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("essential-tab");

    // Get current settings from API to use as base values
    const currentSettings = await testBase.getCurrentSettings();

    // Set specific test values (different from current)
    const testDistanceComp =
      currentSettings.distanceComp < 15
        ? currentSettings.distanceComp + 1.5
        : currentSettings.distanceComp - 1.5;
    const testAmbientTemp =
      currentSettings.ambientTemp < 80
        ? currentSettings.ambientTemp + 3
        : currentSettings.ambientTemp - 3;

    await page.locator("#distanceComp").fill(testDistanceComp.toString());
    await page.locator("#ambientTemp").fill(testAmbientTemp.toString());
    await page.waitForTimeout(500);

    // Navigate to another tab and back
    await testBase.clickSettingsTab("detection-alerts-tab");
    await page.waitForTimeout(500);
    await testBase.clickSettingsTab("essential-tab");
    await page.waitForTimeout(500);

    // Check that values are maintained
    const distanceValue = await page.locator("#distanceComp").inputValue();
    const ambientValue = await page.locator("#ambientTemp").inputValue();

    expect(parseFloat(distanceValue)).toBeCloseTo(testDistanceComp, 1);
    expect(parseFloat(ambientValue)).toBeCloseTo(testAmbientTemp, 1);

    console.log("✅ Settings values persist during tab navigation");
  });
});
