const { test, expect } = require("@playwright/test");
const { ThermalSensorTestBase } = require("./base-test");

test.describe("Stats Grid Tests", () => {
  let testBase;

  test.beforeEach(async ({ page }) => {
    testBase = new ThermalSensorTestBase(page);
  });

  test("should display all four stat cards with correct structure", async ({
    page,
  }) => {
    console.log("📊 Testing stats grid structure...");

    await testBase.navigateToHomePage();

    // Check stats grid container
    const statsGrid = page.locator(".stats-grid");
    await expect(statsGrid).toBeVisible();
    console.log("✅ Stats grid container is visible");

    // Get all stat cards
    const statCards = await testBase.getStatCards();
    expect(statCards).toHaveLength(4);
    console.log(`✅ Found ${statCards.length} stat cards`);

    // Verify each stat card has the required structure
    for (const stat of statCards) {
      expect(stat.label).toBeTruthy();
      expect(stat.value).toBeTruthy();
      expect(stat.unit).toBeTruthy();
      console.log(`✅ Stat card "${stat.label}" has complete structure`);
    }
  });

  test("should display current body temperature stat card correctly", async ({
    page,
  }) => {
    console.log("🌡️ Testing current body temperature stat card...");

    await testBase.navigateToHomePage();

    // Find the current temperature stat card
    const tempCard = page
      .locator(".stat-card")
      .filter({ hasText: "Current Body Temperature" });
    await expect(tempCard).toBeVisible();

    // Check card structure
    const label = tempCard.locator(".stat-label");
    await expect(label).toContainText("Current Body Temperature");

    const value = tempCard.locator(".stat-value");
    await expect(value).toBeVisible();

    const unit = tempCard.locator(".stat-unit");
    await expect(unit).toContainText("°F");

    // Check thermometer icon
    const icon = tempCard.locator("i.fas.fa-thermometer-full");
    await expect(icon).toBeVisible();

    console.log("✅ Current body temperature card structure is correct");
  });

  test("should display baseline temperature stat card correctly", async ({
    page,
  }) => {
    console.log("📏 Testing baseline temperature stat card...");

    await testBase.navigateToHomePage();

    // Find the baseline temperature stat card
    const baselineCard = page
      .locator(".stat-card")
      .filter({ hasText: "Baseline Temperature" });
    await expect(baselineCard).toBeVisible();

    // Check card structure
    const label = baselineCard.locator(".stat-label");
    await expect(label).toContainText("Baseline Temperature");

    const value = baselineCard.locator(".stat-value");
    await expect(value).toBeVisible();

    const unit = baselineCard.locator(".stat-unit");
    await expect(unit).toContainText("°F");

    // Check chart icon
    const icon = baselineCard.locator("i.fas.fa-chart-line");
    await expect(icon).toBeVisible();

    console.log("✅ Baseline temperature card structure is correct");
  });

  test("should display temperature difference stat card correctly", async ({
    page,
  }) => {
    console.log("📈 Testing temperature difference stat card...");

    await testBase.navigateToHomePage();

    // Find the temperature difference stat card
    const diffCard = page
      .locator(".stat-card")
      .filter({ hasText: "Temperature Difference" });
    await expect(diffCard).toBeVisible();

    // Check card structure
    const label = diffCard.locator(".stat-label");
    await expect(label).toContainText("Temperature Difference");

    const value = diffCard.locator(".stat-value");
    await expect(value).toBeVisible();

    const unit = diffCard.locator(".stat-unit");
    await expect(unit).toContainText("°F");

    // Check arrows icon
    const icon = diffCard.locator("i.fas.fa-arrows-alt-v");
    await expect(icon).toBeVisible();

    console.log("✅ Temperature difference card structure is correct");
  });

  test("should display monitoring duration stat card correctly", async ({
    page,
  }) => {
    console.log("⏱️ Testing monitoring duration stat card...");

    await testBase.navigateToHomePage();

    // Find the monitoring duration stat card
    const durationCard = page
      .locator(".stat-card")
      .filter({ hasText: "Monitoring Duration" });
    await expect(durationCard).toBeVisible();

    // Check card structure
    const label = durationCard.locator(".stat-label");
    await expect(label).toContainText("Monitoring Duration");

    const value = durationCard.locator(".stat-value");
    await expect(value).toBeVisible();

    const unit = durationCard.locator(".stat-unit");
    await expect(unit).toContainText("minutes");

    // Check clock icon
    const icon = durationCard.locator("i.fas.fa-clock");
    await expect(icon).toBeVisible();

    console.log("✅ Monitoring duration card structure is correct");
  });

  test("should have proper stat card styling", async ({ page }) => {
    console.log("🎨 Testing stat card styling...");

    await testBase.navigateToHomePage();

    const statCards = page.locator(".stat-card");
    const cardCount = await statCards.count();

    for (let i = 0; i < cardCount; i++) {
      const card = statCards.nth(i);

      // Check stat card class
      await expect(card).toHaveClass(/stat-card/);

      // Check that card has proper structure
      const statIcon = card.locator(".stat-icon");
      await expect(statIcon).toBeVisible();

      const statLabel = card.locator(".stat-label");
      await expect(statLabel).toBeVisible();

      const statValue = card.locator(".stat-value");
      await expect(statValue).toBeVisible();

      const statUnit = card.locator(".stat-unit");
      await expect(statUnit).toBeVisible();

      console.log(`✅ Stat card ${i + 1} has proper styling`);
    }
  });

  test("should display stat values in correct format", async ({ page }) => {
    console.log("🔢 Testing stat value formats...");

    await testBase.navigateToHomePage();

    const stats = await testBase.getStatCards();

    for (const stat of stats) {
      if (stat.label.includes("Temperature")) {
        // Temperature values should be either "--" or a number
        expect(stat.value).toMatch(/^(--|\d+(\.\d+)?)$/);
        expect(stat.unit).toMatch(/°F|Â°F/); // Handle encoding variations
        console.log(
          `✅ ${stat.label}: ${stat.value} ${stat.unit} (valid temperature format)`
        );
      } else if (stat.label.includes("Duration")) {
        // Duration should be either "--" or a number
        expect(stat.value).toMatch(/^(--|\d+(\.\d+)?)$/);
        expect(stat.unit).toBe("minutes");
        console.log(
          `✅ ${stat.label}: ${stat.value} ${stat.unit} (valid duration format)`
        );
      }
    }
  });

  test("should update stat values over time", async ({ page }) => {
    console.log("🔄 Testing stat value updates...");

    await testBase.navigateToHomePage();

    // Get initial stat values
    const initialStats = await testBase.getStatCards();
    console.log("📊 Initial stat values captured");

    // Wait for potential updates (thermal sensor data refreshes)
    await page.waitForTimeout(5000);

    // Get updated stat values
    const updatedStats = await testBase.getStatCards();
    console.log("📊 Updated stat values captured");

    // Verify structure remains consistent
    expect(updatedStats).toHaveLength(initialStats.length);

    for (let i = 0; i < initialStats.length; i++) {
      expect(updatedStats[i].label).toBe(initialStats[i].label);
      expect(updatedStats[i].unit).toBe(initialStats[i].unit);
      console.log(`✅ ${updatedStats[i].label} structure remains consistent`);
    }

    console.log("✅ Stat values maintain consistent structure over time");
  });

  test("should have responsive grid layout", async ({ page }) => {
    console.log("📱 Testing responsive grid layout...");

    await testBase.navigateToHomePage();

    // Check that stats grid uses CSS Grid layout
    const statsGrid = page.locator(".stats-grid");
    const computedStyle = await statsGrid.evaluate((el) => {
      return window.getComputedStyle(el).display;
    });
    expect(computedStyle).toBe("grid");
    console.log("✅ Stats grid uses CSS Grid layout");

    // Check that all stat cards are direct children of stats grid
    const statCards = page.locator(".stats-grid > .stat-card");
    const cardCount = await statCards.count();
    expect(cardCount).toBe(4);
    console.log(`✅ Found ${cardCount} stat cards in grid`);
  });

  test("should display stat icons with correct colors", async ({ page }) => {
    console.log("🎨 Testing stat icon colors...");

    await testBase.navigateToHomePage();

    // Check current temperature icon (should be danger based on HTML)
    const tempIconDiv = page
      .locator(".stat-card")
      .filter({ hasText: "Current Body Temperature" })
      .locator(".stat-icon");
    await expect(tempIconDiv).toHaveClass(/text-danger/);
    console.log("✅ Current temperature icon has danger color");

    // Check baseline icon (should be success based on HTML)
    const baselineIconDiv = page
      .locator(".stat-card")
      .filter({ hasText: "Baseline Temperature" })
      .locator(".stat-icon");
    await expect(baselineIconDiv).toHaveClass(/text-success/);
    console.log("✅ Baseline temperature icon has success color");

    // Check difference icon (should be warning based on HTML)
    const diffIconDiv = page
      .locator(".stat-card")
      .filter({ hasText: "Temperature Difference" })
      .locator(".stat-icon");
    await expect(diffIconDiv).toHaveClass(/text-warning/);
    console.log("✅ Temperature difference icon has warning color");

    // Check duration icon (should be info based on HTML)
    const durationIconDiv = page
      .locator(".stat-card")
      .filter({ hasText: "Monitoring Duration" })
      .locator(".stat-icon");
    await expect(durationIconDiv).toHaveClass(/text-info/);
    console.log("✅ Monitoring duration icon has info color");
  });
});
