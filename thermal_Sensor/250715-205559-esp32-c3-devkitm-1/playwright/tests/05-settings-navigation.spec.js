const { test, expect } = require("@playwright/test");
const { ThermalSensorTestBase } = require("./base-test");

test.describe("Settings Panel Navigation Tests", () => {
  let testBase;

  test.beforeEach(async ({ page }) => {
    testBase = new ThermalSensorTestBase(page);
  });

  test("should display settings panel with correct structure", async ({
    page,
  }) => {
    console.log("⚙️ Testing settings panel structure...");

    await testBase.navigateToHomePage();

    // Check settings panel container
    const settingsPanel = page.locator(".settings-panel");
    await expect(settingsPanel).toBeVisible();
    console.log("✅ Settings panel container is visible");

    // Check settings title
    const settingsTitle = page
      .locator("h4")
      .filter({ hasText: "Fever Detection Settings" });
    await expect(settingsTitle).toBeVisible();
    await expect(settingsTitle).toContainText("Fever Detection Settings");
    console.log("✅ Settings panel title is correct");

    // Check settings icon in title
    const titleIcon = settingsTitle.locator("i.fas.fa-cogs");
    await expect(titleIcon).toBeVisible();
    console.log("✅ Settings cogs icon is present");
  });

  test("should display all settings tabs", async ({ page }) => {
    console.log("🗂️ Testing all settings tabs presence...");

    await testBase.navigateToHomePage();

    const expectedTabs = [
      { id: "auto-calibrate-tab", text: "Auto Calibration", icon: "fa-magic" },
      { id: "essential-tab", text: "Essential Settings", icon: "fa-cog" },
      {
        id: "detection-alerts-tab",
        text: "Detection & Alerts",
        icon: "fa-thermometer-half",
      },
      { id: "advanced-tab", text: "Advanced", icon: "fa-microscope" },
      { id: "time-tab", text: "Time & Schedule", icon: "fa-clock" },
    ];

    for (const tab of expectedTabs) {
      const tabElement = page.locator(`#${tab.id}`);
      await expect(tabElement).toBeVisible();
      await expect(tabElement).toContainText(tab.text);

      // Check tab icon
      const tabIcon = tabElement.locator(`i.fas.${tab.icon}`);
      await expect(tabIcon).toBeVisible();

      console.log(`✅ Found tab: ${tab.text} with ${tab.icon} icon`);
    }
  });

  test("should have auto calibration tab active by default", async ({
    page,
  }) => {
    console.log("🎯 Testing default active tab...");

    await testBase.navigateToHomePage();

    // Check that Auto Calibration tab is active by default
    const autoCalibTab = page.locator("#auto-calibrate-tab");
    await expect(autoCalibTab).toHaveClass(/active/);
    console.log("✅ Auto Calibration tab is active by default");

    // Check that corresponding tab content is visible
    const autoCalibContent = page.locator("#auto-calibrate");
    await expect(autoCalibContent).toBeVisible();
    await expect(autoCalibContent).toHaveClass(/show/);
    await expect(autoCalibContent).toHaveClass(/active/);
    console.log("✅ Auto Calibration content is visible and active");
  });

  test("should navigate to essential settings tab", async ({ page }) => {
    console.log("🔧 Testing essential settings tab navigation...");

    await testBase.navigateToHomePage();

    // Click on Essential Settings tab
    await testBase.clickSettingsTab("essential-tab");

    // Verify tab is active
    const essentialTab = page.locator("#essential-tab");
    await expect(essentialTab).toHaveClass(/active/);
    console.log("✅ Essential Settings tab is now active");

    // Verify content is visible
    const essentialContent = page.locator("#essential");
    await expect(essentialContent).toBeVisible();
    await expect(essentialContent).toHaveClass(/show/);
    await expect(essentialContent).toHaveClass(/active/);
    console.log("✅ Essential Settings content is visible");

    // Verify other tabs are not active
    const autoCalibTab = page.locator("#auto-calibrate-tab");
    await expect(autoCalibTab).not.toHaveClass(/active/);
    console.log("✅ Other tabs are properly deactivated");
  });

  test("should navigate to detection & alerts tab", async ({ page }) => {
    console.log("🔔 Testing detection & alerts tab navigation...");

    await testBase.navigateToHomePage();

    // Click on Detection & Alerts tab
    await testBase.clickSettingsTab("detection-alerts-tab");

    // Verify tab is active
    const alertsTab = page.locator("#detection-alerts-tab");
    await expect(alertsTab).toHaveClass(/active/);
    console.log("✅ Detection & Alerts tab is now active");

    // Verify content is visible
    const alertsContent = page.locator("#detection-alerts");
    await expect(alertsContent).toBeVisible();
    await expect(alertsContent).toHaveClass(/show/);
    await expect(alertsContent).toHaveClass(/active/);
    console.log("✅ Detection & Alerts content is visible");
  });

  test("should navigate to advanced settings tab", async ({ page }) => {
    console.log("⚡ Testing advanced settings tab navigation...");

    await testBase.navigateToHomePage();

    // Click on Advanced tab
    await testBase.clickSettingsTab("advanced-tab");

    // Verify tab is active
    const advancedTab = page.locator("#advanced-tab");
    await expect(advancedTab).toHaveClass(/active/);
    console.log("✅ Advanced tab is now active");

    // Verify content is visible
    const advancedContent = page.locator("#advanced");
    await expect(advancedContent).toBeVisible();
    await expect(advancedContent).toHaveClass(/show/);
    await expect(advancedContent).toHaveClass(/active/);
    console.log("✅ Advanced content is visible");
  });

  test("should navigate to time & schedule tab", async ({ page }) => {
    console.log("🕒 Testing time & schedule tab navigation...");

    await testBase.navigateToHomePage();

    // Click on Time & Schedule tab
    await testBase.clickSettingsTab("time-tab");

    // Verify tab is active
    const timeTab = page.locator("#time-tab");
    await expect(timeTab).toHaveClass(/active/);
    console.log("✅ Time & Schedule tab is now active");

    // Verify content is visible
    const timeContent = page.locator("#time-settings");
    await expect(timeContent).toBeVisible();
    await expect(timeContent).toHaveClass(/show/);
    await expect(timeContent).toHaveClass(/active/);
    console.log("✅ Time & Schedule content is visible");
  });

  test("should navigate between multiple tabs correctly", async ({ page }) => {
    console.log("🔄 Testing multiple tab navigation...");

    await testBase.navigateToHomePage();

    const tabSequence = [
      {
        id: "essential-tab",
        contentId: "essential",
        name: "Essential Settings",
      },
      {
        id: "detection-alerts-tab",
        contentId: "detection-alerts",
        name: "Detection & Alerts",
      },
      { id: "advanced-tab", contentId: "advanced", name: "Advanced" },
      { id: "time-tab", contentId: "time-settings", name: "Time & Schedule" },
      {
        id: "auto-calibrate-tab",
        contentId: "auto-calibrate",
        name: "Auto Calibration",
      },
    ];

    for (const tab of tabSequence) {
      // Click on tab
      await testBase.clickSettingsTab(tab.id);

      // Verify tab is active
      const tabElement = page.locator(`#${tab.id}`);
      await expect(tabElement).toHaveClass(/active/);

      // Verify content is visible
      const contentElement = page.locator(`#${tab.contentId}`);
      await expect(contentElement).toBeVisible();
      await expect(contentElement).toHaveClass(/show/);
      await expect(contentElement).toHaveClass(/active/);

      console.log(`✅ Successfully navigated to ${tab.name}`);
    }
  });

  test("should maintain tab state during page interactions", async ({
    page,
  }) => {
    console.log("🔒 Testing tab state persistence...");

    await testBase.navigateToHomePage();

    // Navigate to Essential Settings
    await testBase.clickSettingsTab("essential-tab");

    // Perform some other page interactions
    const statsGrid = page.locator(".stats-grid");
    await statsGrid.click();

    // Wait a moment
    await page.waitForTimeout(1000);

    // Verify Essential Settings tab is still active
    const essentialTab = page.locator("#essential-tab");
    await expect(essentialTab).toHaveClass(/active/);

    const essentialContent = page.locator("#essential");
    await expect(essentialContent).toBeVisible();
    await expect(essentialContent).toHaveClass(/active/);

    console.log("✅ Tab state persists during other page interactions");
  });

  test("should have proper tab styling and accessibility", async ({ page }) => {
    console.log("♿ Testing tab accessibility and styling...");

    await testBase.navigateToHomePage();

    // Check nav-tabs container
    const navTabs = page.locator(".nav-tabs");
    await expect(navTabs).toBeVisible();
    console.log("✅ Nav tabs container is present");

    // Check tab content container
    const tabContent = page.locator(".tab-content");
    await expect(tabContent).toBeVisible();
    console.log("✅ Tab content container is present");

    // Test each tab for proper attributes
    const tabIds = [
      "auto-calibrate-tab",
      "essential-tab",
      "detection-alerts-tab",
      "advanced-tab",
      "time-tab",
    ];

    for (const tabId of tabIds) {
      const tab = page.locator(`#${tabId}`);

      // Check that tab has nav-link class
      await expect(tab).toHaveClass(/nav-link/);

      // Check that tab has proper role
      const role = await tab.getAttribute("role");
      expect(role).toBe("tab");

      // Check that tab has data-bs-toggle attribute
      const toggle = await tab.getAttribute("data-bs-toggle");
      expect(toggle).toBe("tab");

      console.log(`✅ Tab ${tabId} has proper accessibility attributes`);
    }
  });

  test("should handle rapid tab switching", async ({ page }) => {
    console.log("⚡ Testing rapid tab switching...");

    await testBase.navigateToHomePage();

    const tabs = [
      "essential-tab",
      "detection-alerts-tab",
      "advanced-tab",
      "time-tab",
      "auto-calibrate-tab",
    ];

    // Rapidly switch between tabs
    for (let i = 0; i < 3; i++) {
      for (const tabId of tabs) {
        await page.click(`#${tabId}`);
        await page.waitForTimeout(100); // Small delay to simulate rapid clicking
      }
    }

    // Verify final state is stable
    const finalTab = page.locator("#auto-calibrate-tab");
    await expect(finalTab).toHaveClass(/active/);

    const finalContent = page.locator("#auto-calibrate");
    await expect(finalContent).toBeVisible();
    await expect(finalContent).toHaveClass(/active/);

    console.log("✅ Rapid tab switching handled correctly");
  });
});
