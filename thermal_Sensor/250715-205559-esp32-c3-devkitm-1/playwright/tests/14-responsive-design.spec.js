const { test, expect, devices } = require("@playwright/test");
const { ThermalSensorTestBase } = require("./base-test");

test.describe("Responsive Design Tests", () => {
  let testBase;

  test.beforeEach(async ({ page }) => {
    testBase = new ThermalSensorTestBase(page);
  });

  test("should display correctly on desktop (1920x1080)", async ({ page }) => {
    console.log("🖥️ Testing desktop display (1920x1080)...");

    await page.setViewportSize({ width: 1920, height: 1080 });
    await testBase.navigateToHomePage();

    // Check main layout elements are visible
    const header = page
      .locator("h1")
      .filter({ hasText: "Child Fever Monitor" });
    const temperatureDisplay = page.locator("#currentTemp");
    const thermalView = page.locator("#heatmapContainer");
    const settingsTabs = page.locator(".nav-tabs");

    await expect(header).toBeVisible();
    await expect(temperatureDisplay).toBeVisible();
    await expect(thermalView).toBeVisible();
    await expect(settingsTabs).toBeVisible();

    console.log("✅ Desktop display works correctly");
  });

  test("should display correctly on tablet (768x1024)", async ({ page }) => {
    console.log("📱 Testing tablet display (768x1024)...");

    await page.setViewportSize({ width: 768, height: 1024 });
    await testBase.navigateToHomePage();

    // Check main layout elements are visible and responsive
    const header = page
      .locator("h1")
      .filter({ hasText: "Child Fever Monitor" });
    const temperatureDisplay = page.locator("#currentTemp");
    const thermalView = page.locator("#heatmapContainer");
    const settingsTabs = page.locator(".nav-tabs");

    await expect(header).toBeVisible();
    await expect(temperatureDisplay).toBeVisible();
    await expect(thermalView).toBeVisible();
    await expect(settingsTabs).toBeVisible();

    // Check that tabs are still clickable on tablet
    await testBase.clickSettingsTab("essential-tab");
    const essentialContent = page.locator("#essential");
    await expect(essentialContent).toBeVisible();

    console.log("✅ Tablet display works correctly");
  });

  test("should display correctly on mobile (375x667)", async ({ page }) => {
    console.log("📱 Testing mobile display (375x667)...");

    await page.setViewportSize({ width: 375, height: 667 });
    await testBase.navigateToHomePage();

    // Check main layout elements are visible on mobile
    const header = page
      .locator("h1")
      .filter({ hasText: "Child Fever Monitor" });
    const temperatureDisplay = page.locator("#currentTemp");
    const thermalView = page.locator("#heatmapContainer");

    await expect(header).toBeVisible();
    await expect(temperatureDisplay).toBeVisible();
    await expect(thermalView).toBeVisible();

    // Check that navigation still works on mobile
    const settingsTabs = page.locator(".nav-tabs");
    await expect(settingsTabs).toBeVisible();

    console.log("✅ Mobile display works correctly");
  });

  test("should handle settings tabs responsively", async ({ page }) => {
    console.log("📋 Testing responsive settings tabs...");

    // Test on different screen sizes
    const screenSizes = [
      { width: 1920, height: 1080, name: "Desktop" },
      { width: 768, height: 1024, name: "Tablet" },
      { width: 375, height: 667, name: "Mobile" },
    ];

    for (const size of screenSizes) {
      console.log(`Testing ${size.name} (${size.width}x${size.height})`);

      await page.setViewportSize({ width: size.width, height: size.height });
      await testBase.navigateToHomePage();

      // Test each settings tab
      const tabs = [
        "essential-tab",
        "detection-alerts-tab",
        "advanced-tab",
        "time-tab",
      ];

      for (const tabId of tabs) {
        await testBase.clickSettingsTab(tabId);
        let contentId = tabId.replace("-tab", "");
        // Special case for time tab
        if (contentId === "time") {
          contentId = "time-settings";
        }
        const tabContent = page.locator(`#${contentId}`);
        await expect(tabContent).toBeVisible();
      }
    }

    console.log("✅ Settings tabs work responsively across all screen sizes");
  });

  test("should handle form controls responsively", async ({ page }) => {
    console.log("🎛️ Testing responsive form controls...");

    await page.setViewportSize({ width: 375, height: 667 }); // Mobile size
    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("essential-tab");

    // Check that form controls are accessible on mobile
    const distanceCompSlider = page.locator("#distanceComp");
    const ambientTempSlider = page.locator("#ambientTemp");
    const personThresholdSlider = page.locator("#personThreshold");

    await expect(distanceCompSlider).toBeVisible();
    await expect(ambientTempSlider).toBeVisible();
    await expect(personThresholdSlider).toBeVisible();

    // Test that sliders are interactive on mobile
    await distanceCompSlider.evaluate((el) => {
      el.value = "5.0";
      el.dispatchEvent(new Event("input"));
    });

    const distanceCompValue = await distanceCompSlider.inputValue();
    expect(parseFloat(distanceCompValue)).toBeCloseTo(5.0, 1);

    console.log("✅ Form controls work responsively on mobile");
  });

  test("should handle thermal view responsively", async ({ page }) => {
    console.log("🌡️ Testing responsive thermal view...");

    const screenSizes = [
      { width: 1920, height: 1080, name: "Desktop" },
      { width: 768, height: 1024, name: "Tablet" },
      { width: 375, height: 667, name: "Mobile" },
    ];

    for (const size of screenSizes) {
      console.log(
        `Testing thermal view on ${size.name} (${size.width}x${size.height})`
      );

      await page.setViewportSize({ width: size.width, height: size.height });
      await testBase.navigateToHomePage();

      // Check thermal view elements
      const thermalView = page.locator("#heatmapContainer");
      const temperatureScale = page.locator("#colorbar");

      await expect(thermalView).toBeVisible();
      await expect(temperatureScale).toBeVisible();

      // Check that thermal view has reasonable dimensions
      const thermalViewBox = await thermalView.boundingBox();
      expect(thermalViewBox.width).toBeGreaterThan(100);
      expect(thermalViewBox.height).toBeGreaterThan(100);
    }

    console.log(
      "✅ Thermal view displays responsively across all screen sizes"
    );
  });

  test("should handle navigation responsively", async ({ page }) => {
    console.log("🧭 Testing responsive navigation...");

    await page.setViewportSize({ width: 375, height: 667 }); // Mobile size
    await testBase.navigateToHomePage();

    // Check that all navigation tabs are accessible
    const navTabs = page.locator(".nav-tabs .nav-link");
    const tabCount = await navTabs.count();

    expect(tabCount).toBeGreaterThan(4); // Should have at least 5 tabs

    // Check that each tab is clickable
    for (let i = 0; i < tabCount; i++) {
      const tab = navTabs.nth(i);
      await expect(tab).toBeVisible();

      // Check if tab is clickable (not checking actual click to avoid state issues)
      const isEnabled = await tab.isEnabled();
      expect(isEnabled).toBe(true);
    }

    console.log("✅ Navigation works responsively on mobile");
  });

  test("should handle buttons responsively", async ({ page }) => {
    console.log("🔘 Testing responsive buttons...");

    await page.setViewportSize({ width: 375, height: 667 }); // Mobile size
    await testBase.navigateToHomePage();

    // Check system control buttons
    const systemButtons = [
      "Reset Baseline",
      "Calibrate Now",
      "Test Alert",
      "Test Beep",
      "Download Data",
    ];

    for (const buttonText of systemButtons) {
      const button = page.locator("button").filter({ hasText: buttonText });
      await expect(button).toBeVisible();

      // Check button has reasonable size for mobile
      const buttonBox = await button.boundingBox();
      expect(buttonBox.height).toBeGreaterThan(30); // Minimum touch target
    }

    // Check settings action buttons
    const settingsButtons = [
      "Save Settings",
      "Reset to Defaults",
      "Export Config",
      "Import Config",
    ];

    for (const buttonText of settingsButtons) {
      const button = page.locator("button").filter({ hasText: buttonText });
      await expect(button).toBeVisible();
    }

    console.log("✅ Buttons display responsively on mobile");
  });

  test("should handle text readability across screen sizes", async ({
    page,
  }) => {
    console.log("📖 Testing text readability...");

    const screenSizes = [
      { width: 1920, height: 1080, name: "Desktop" },
      { width: 768, height: 1024, name: "Tablet" },
      { width: 375, height: 667, name: "Mobile" },
    ];

    for (const size of screenSizes) {
      console.log(`Testing text readability on ${size.name}`);

      await page.setViewportSize({ width: size.width, height: size.height });
      await testBase.navigateToHomePage();

      // Check main temperature display
      const tempDisplay = page.locator("#currentTemp");
      await expect(tempDisplay).toBeVisible();

      // Check that text is not too small
      const fontSize = await tempDisplay.evaluate((el) => {
        return window.getComputedStyle(el).fontSize;
      });

      const fontSizeNum = parseFloat(fontSize);
      expect(fontSizeNum).toBeGreaterThan(12); // Minimum readable font size

      // Check header text
      const header = page
        .locator("h1")
        .filter({ hasText: "Child Fever Monitor" });
      await expect(header).toBeVisible();
    }

    console.log("✅ Text remains readable across all screen sizes");
  });

  test("should handle scrolling on small screens", async ({ page }) => {
    console.log("📜 Testing scrolling behavior...");

    await page.setViewportSize({ width: 375, height: 667 }); // Mobile size
    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("advanced-tab");

    // Check that content is scrollable if needed
    const advancedContent = page.locator("#advanced");
    await expect(advancedContent).toBeVisible();

    // Try scrolling to bottom of settings
    await page.evaluate(() => {
      window.scrollTo(0, document.body.scrollHeight);
    });

    // Check that we can still interact with elements after scrolling
    const displayBrightnessSlider = page.locator("#displayBrightness");
    await expect(displayBrightnessSlider).toBeVisible();

    console.log("✅ Scrolling works correctly on small screens");
  });

  test("should maintain functionality across viewport changes", async ({
    page,
  }) => {
    console.log("🔄 Testing functionality across viewport changes...");

    await testBase.navigateToHomePage();

    // Start with desktop
    await page.setViewportSize({ width: 1920, height: 1080 });
    await testBase.clickSettingsTab("essential-tab");

    // Change to mobile
    await page.setViewportSize({ width: 375, height: 667 });

    // Check that the tab is still active and functional
    const essentialContent = page.locator("#essential");
    await expect(essentialContent).toBeVisible();

    // Test that controls still work
    const distanceCompSlider = page.locator("#distanceComp");
    await expect(distanceCompSlider).toBeVisible();

    // Change back to desktop
    await page.setViewportSize({ width: 1920, height: 1080 });

    // Check that everything still works
    await expect(essentialContent).toBeVisible();
    await expect(distanceCompSlider).toBeVisible();

    console.log("✅ Functionality maintained across viewport changes");
  });
});
