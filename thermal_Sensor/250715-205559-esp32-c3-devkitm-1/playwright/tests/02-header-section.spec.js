const { test, expect } = require("@playwright/test");
const { ThermalSensorTestBase } = require("./base-test");

test.describe("Header Section Tests", () => {
  let testBase;

  test.beforeEach(async ({ page }) => {
    testBase = new ThermalSensorTestBase(page);
  });

  test("should display main title and subtitle correctly", async ({ page }) => {
    console.log("🏷️ Testing main title and subtitle...");

    await testBase.navigateToHomePage();

    // Check main title
    const mainTitle = page.locator(".header-section h1");
    await expect(mainTitle).toBeVisible();
    await expect(mainTitle).toContainText("Child Fever Monitor");
    console.log("✅ Main title is correct");

    // Check that title has thermometer icon
    const titleIcon = page.locator(
      ".header-section h1 i.fas.fa-thermometer-half"
    );
    await expect(titleIcon).toBeVisible();
    console.log("✅ Title thermometer icon is present");
  });

  test("should display device information correctly", async ({ page }) => {
    console.log("📊 Testing device information display...");

    await testBase.navigateToHomePage();

    // Check distance information
    const distanceInfo = page.locator(".header-section p").first();
    await expect(distanceInfo).toContainText("Distance: 4 feet");
    console.log("✅ Distance information is correct");

    // Check ruler icon for distance
    const rulerIcon = page.locator(".header-section i.fas.fa-ruler");
    await expect(rulerIcon).toBeVisible();
    console.log("✅ Distance ruler icon is present");
  });

  test("should display compensation value", async ({ page }) => {
    console.log("🔧 Testing compensation value display...");

    await testBase.navigateToHomePage();

    // Check compensation display
    const compensationDisplay = page.locator("#compensationDisplay");
    await expect(compensationDisplay).toBeVisible();

    const compensationValue = await compensationDisplay.textContent();
    expect(compensationValue).toMatch(/^\d+(\.\d+)?$/); // Should be a number
    console.log(`✅ Compensation value displayed: +${compensationValue}°F`);

    // Check compensation text
    const compensationText = page.locator(".header-section p").first();
    await expect(compensationText).toContainText("Compensation:");
    console.log("✅ Compensation label is present");
  });

  test("should display connection status", async ({ page }) => {
    console.log("🌐 Testing connection status display...");

    await testBase.navigateToHomePage();

    // Check connection status element
    const connectionStatus = page.locator("#connectionStatus");
    await expect(connectionStatus).toBeVisible();

    const statusText = await connectionStatus.textContent();
    expect(statusText).toBeTruthy();
    console.log(`✅ Connection status displayed: ${statusText}`);

    // Check WiFi icon in connection status
    const wifiIcon = page.locator("#connectionStatus i.fas.fa-wifi");
    await expect(wifiIcon).toBeVisible();
    console.log("✅ WiFi icon is present");
  });

  test("should display device time and date", async ({ page }) => {
    console.log("🕒 Testing device time and date display...");

    await testBase.navigateToHomePage();

    // Check device time element
    const deviceTime = page.locator("#deviceTime");
    await expect(deviceTime).toBeVisible();

    const timeText = await deviceTime.textContent();
    console.log(`✅ Device time displayed: ${timeText}`);

    // Check device date element
    const deviceDate = page.locator("#deviceDate");
    await expect(deviceDate).toBeVisible();

    const dateText = await deviceDate.textContent();
    console.log(`✅ Device date displayed: ${dateText}`);

    // Check clock icon
    const clockIcon = page.locator(".header-section i.fas.fa-clock");
    await expect(clockIcon).toBeVisible();
    console.log("✅ Clock icon is present");

    // Check calendar icon
    const calendarIcon = page.locator(".header-section i.fas.fa-calendar");
    await expect(calendarIcon).toBeVisible();
    console.log("✅ Calendar icon is present");
  });

  test("should display time status badge", async ({ page }) => {
    console.log("🏷️ Testing time status badge...");

    await testBase.navigateToHomePage();

    // Check time status badge
    const timeStatus = page.locator("#timeStatus");
    await expect(timeStatus).toBeVisible();
    await expect(timeStatus).toHaveClass(/badge/);

    const statusText = await timeStatus.textContent();
    expect(statusText).toBeTruthy();
    console.log(`✅ Time status badge displayed: ${statusText}`);
  });

  test("should have proper header styling and layout", async ({ page }) => {
    console.log("🎨 Testing header styling and layout...");

    await testBase.navigateToHomePage();

    // Check header section has proper class
    const headerSection = page.locator(".header-section");
    await expect(headerSection).toHaveClass(/text-center/);
    console.log("✅ Header has center alignment");

    // Check that header contains multiple info paragraphs
    const infoParagraphs = page.locator(".header-section p");
    const paragraphCount = await infoParagraphs.count();
    expect(paragraphCount).toBeGreaterThanOrEqual(2);
    console.log(`✅ Header contains ${paragraphCount} info paragraphs`);

    // Check that paragraphs have light text styling
    const firstParagraph = infoParagraphs.first();
    await expect(firstParagraph).toHaveClass(/text-light/);
    console.log("✅ Header paragraphs have light text styling");
  });

  test("should display all required icons", async ({ page }) => {
    console.log("🎯 Testing all required header icons...");

    await testBase.navigateToHomePage();

    const requiredIcons = [
      { selector: "i.fas.fa-thermometer-half", name: "Thermometer" },
      { selector: "i.fas.fa-ruler", name: "Ruler" },
      { selector: "i.fas.fa-adjust", name: "Adjust" },
      { selector: "#connectionStatus i.fas.fa-wifi", name: "WiFi" },
      { selector: "i.fas.fa-clock", name: "Clock" },
      { selector: "i.fas.fa-calendar", name: "Calendar" },
    ];

    for (const icon of requiredIcons) {
      // Use selector as-is if it starts with #, otherwise prefix with .header-section
      const selector = icon.selector.startsWith("#")
        ? icon.selector
        : `.header-section ${icon.selector}`;
      const iconElement = page.locator(selector);
      await expect(iconElement).toBeVisible();
      console.log(`✅ ${icon.name} icon is present`);
    }
  });

  test("should update compensation value dynamically", async ({ page }) => {
    console.log("🔄 Testing dynamic compensation value updates...");

    await testBase.navigateToHomePage();

    // Get initial compensation value
    const compensationDisplay = page.locator("#compensationDisplay");
    const initialValue = await compensationDisplay.textContent();
    console.log(`📊 Initial compensation value: +${initialValue}°F`);

    // Navigate to Essential Settings tab to change compensation
    await testBase.clickSettingsTab("essential-tab");

    // Change the distance compensation slider using evaluate for range input
    const distanceCompSlider = page.locator("#distanceComp");
    await distanceCompSlider.evaluate((el) => {
      el.value = "5.0";
      el.dispatchEvent(new Event("input", { bubbles: true }));
    });

    // Wait for UI to update
    await page.waitForTimeout(500);

    // Check if the header value updates (this might require saving settings)
    const updatedValue = await compensationDisplay.textContent();
    console.log(`📊 Compensation value after change: +${updatedValue}°F`);

    // The value should be a valid number
    expect(updatedValue).toMatch(/^\d+(\.\d+)?$/);
    console.log("✅ Compensation value remains valid after changes");
  });
});
