const { test, expect } = require("@playwright/test");
const { ThermalSensorTestBase } = require("./base-test");

test.describe("Time & Schedule Settings Tests", () => {
  let testBase;

  test.beforeEach(async ({ page }) => {
    testBase = new ThermalSensorTestBase(page);
  });

  test("should display time & schedule tab content correctly", async ({
    page,
  }) => {
    console.log("🕐 Testing time & schedule tab content...");

    await testBase.navigateToHomePage();

    // Navigate to Time & Schedule tab
    await testBase.clickSettingsTab("time-tab");

    // Check time settings content is visible
    const timeContent = page.locator("#time-settings");
    await expect(timeContent).toBeVisible();
    await expect(timeContent).toHaveClass(/show/);
    await expect(timeContent).toHaveClass(/active/);
    console.log("✅ Time & schedule content is visible and active");
  });

  test("should display NTP server control correctly", async ({ page }) => {
    console.log("🌐 Testing NTP server control...");

    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("time-tab");

    // Check NTP server input
    const ntpServerInput = page.locator("#ntpServer");
    await expect(ntpServerInput).toBeVisible();
    await expect(ntpServerInput).toHaveAttribute("type", "text");
    await expect(ntpServerInput).toHaveAttribute("placeholder", "pool.ntp.org");
    console.log("✅ NTP server input has correct attributes");

    // Check label
    const ntpLabel = page.locator("label").filter({ hasText: "NTP Server" });
    await expect(ntpLabel).toBeVisible();
    console.log("✅ NTP server label is present");

    // Check server icon
    const serverIcon = ntpLabel.locator("i.fas.fa-server");
    await expect(serverIcon).toBeVisible();
    console.log("✅ Server icon is present");
  });

  test("should display timezone control correctly", async ({ page }) => {
    console.log("🌍 Testing timezone control...");

    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("time-tab");

    // Check timezone select
    const timezoneSelect = page.locator("#timezone");
    await expect(timezoneSelect).toBeVisible();
    await expect(timezoneSelect).toHaveClass(/form-select/);
    console.log("✅ Timezone select has correct attributes");

    // Check label
    const timezoneLabel = page.locator("label").filter({ hasText: "Timezone" });
    await expect(timezoneLabel).toBeVisible();
    console.log("✅ Timezone label is present");

    // Check globe icon
    const globeIcon = timezoneLabel.locator("i.fas.fa-globe");
    await expect(globeIcon).toBeVisible();
    console.log("✅ Globe icon is present");

    // Check timezone options
    const options = await timezoneSelect.locator("option").all();
    expect(options.length).toBeGreaterThan(5);
    console.log(`✅ Timezone has ${options.length} options`);
  });

  test("should display auto time sync control correctly", async ({ page }) => {
    console.log("🔄 Testing auto time sync control...");

    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("time-tab");

    // Check auto sync checkbox
    const autoSyncInput = page.locator("#autoTimeSync");
    await expect(autoSyncInput).toBeVisible();
    await expect(autoSyncInput).toHaveAttribute("type", "checkbox");
    await expect(autoSyncInput).toHaveClass(/form-check-input/);
    console.log("✅ Auto sync checkbox has correct attributes");

    // Check label
    const autoSyncLabel = page
      .locator("label")
      .filter({ hasText: "Auto Time Sync" });
    await expect(autoSyncLabel).toBeVisible();
    console.log("✅ Auto sync label is present");

    // Check sync icon
    const syncIcon = autoSyncLabel.locator("i.fas.fa-sync");
    await expect(syncIcon).toBeVisible();
    console.log("✅ Sync icon is present");
  });

  test("should display time sync buttons correctly", async ({ page }) => {
    console.log("⏰ Testing time sync buttons...");

    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("time-tab");

    // Check sync time now button
    const syncButton = page
      .locator("button")
      .filter({ hasText: "Sync Time Now" });
    await expect(syncButton).toBeVisible();
    await expect(syncButton).toHaveClass(/btn-primary/);
    console.log("✅ Sync time now button is present");

    // Check set time manually button
    const manualButton = page
      .locator("button")
      .filter({ hasText: "Set Time Manually" });
    await expect(manualButton).toBeVisible();
    await expect(manualButton).toHaveClass(/btn-secondary/);
    console.log("✅ Set time manually button is present");
  });

  test("should display night mode enabled control correctly", async ({
    page,
  }) => {
    console.log("🌙 Testing night mode enabled control...");

    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("time-tab");

    // Check night mode checkbox
    const nightModeInput = page.locator("#nightModeEnabled");
    await expect(nightModeInput).toBeVisible();
    await expect(nightModeInput).toHaveAttribute("type", "checkbox");
    await expect(nightModeInput).toHaveClass(/form-check-input/);
    console.log("✅ Night mode checkbox has correct attributes");

    // Check label
    const nightModeLabel = page
      .locator("label")
      .filter({ hasText: "Enable Night Mode" });
    await expect(nightModeLabel).toBeVisible();
    console.log("✅ Night mode label is present");

    // Check moon icon
    const moonIcon = nightModeLabel.locator("i.fas.fa-moon");
    await expect(moonIcon).toBeVisible();
    console.log("✅ Moon icon is present");
  });

  test("should display night start time controls correctly", async ({
    page,
  }) => {
    console.log("🌆 Testing night start time controls...");

    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("time-tab");

    // Check night start hour select
    const nightStartHour = page.locator("#nightStartHour");
    await expect(nightStartHour).toBeVisible();
    await expect(nightStartHour).toHaveClass(/form-select/);
    console.log("✅ Night start hour select is present");

    // Check night start minute select
    const nightStartMinute = page.locator("#nightStartMinute");
    await expect(nightStartMinute).toBeVisible();
    await expect(nightStartMinute).toHaveClass(/form-select/);
    console.log("✅ Night start minute select is present");

    // Check label
    const startLabel = page
      .locator("label")
      .filter({ hasText: "Night Start Time" });
    await expect(startLabel).toBeVisible();
    console.log("✅ Night start time label is present");

    // Check clock icon
    const clockIcon = startLabel.locator("i.fas.fa-clock");
    await expect(clockIcon).toBeVisible();
    console.log("✅ Clock icon is present");
  });

  test("should display night end time controls correctly", async ({ page }) => {
    console.log("🌅 Testing night end time controls...");

    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("time-tab");

    // Check night end hour select
    const nightEndHour = page.locator("#nightEndHour");
    await expect(nightEndHour).toBeVisible();
    await expect(nightEndHour).toHaveClass(/form-select/);
    console.log("✅ Night end hour select is present");

    // Check night end minute select
    const nightEndMinute = page.locator("#nightEndMinute");
    await expect(nightEndMinute).toBeVisible();
    await expect(nightEndMinute).toHaveClass(/form-select/);
    console.log("✅ Night end minute select is present");

    // Check label
    const endLabel = page
      .locator("label")
      .filter({ hasText: "Night End Time" });
    await expect(endLabel).toBeVisible();
    console.log("✅ Night end time label is present");

    // Check sun icon
    const sunIcon = endLabel.locator("i.fas.fa-sun");
    await expect(sunIcon).toBeVisible();
    console.log("✅ Sun icon is present");
  });

  test("should display display off at night control correctly", async ({
    page,
  }) => {
    console.log("📺 Testing display off at night control...");

    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("time-tab");

    // Check display off checkbox
    const displayOffInput = page.locator("#displayOffAtNight");
    await expect(displayOffInput).toBeVisible();
    await expect(displayOffInput).toHaveAttribute("type", "checkbox");
    await expect(displayOffInput).toHaveClass(/form-check-input/);
    console.log("✅ Display off checkbox has correct attributes");

    // Check label
    const displayOffLabel = page
      .locator("label")
      .filter({ hasText: "Turn Off Display at Night" });
    await expect(displayOffLabel).toBeVisible();
    console.log("✅ Display off label is present");

    // Check eye slash icon
    const eyeSlashIcon = displayOffLabel.locator("i.fas.fa-eye-slash");
    await expect(eyeSlashIcon).toBeVisible();
    console.log("✅ Eye slash icon is present");
  });

  test("should interact with NTP server input correctly", async ({ page }) => {
    console.log("📝 Testing NTP server input interaction...");

    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("time-tab");

    // Test NTP server input
    const ntpServerInput = page.locator("#ntpServer");
    await ntpServerInput.fill("time.google.com");
    await page.waitForTimeout(500);

    const inputValue = await ntpServerInput.inputValue();
    expect(inputValue).toBe("time.google.com");
    console.log("✅ NTP server input accepts text correctly");
  });

  test("should interact with timezone select correctly", async ({ page }) => {
    console.log("🌐 Testing timezone select interaction...");

    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("time-tab");

    // Test timezone selection
    const timezoneSelect = page.locator("#timezone");
    await timezoneSelect.selectOption("PST8PDT");
    await page.waitForTimeout(500);

    const selectedValue = await timezoneSelect.inputValue();
    expect(selectedValue).toBe("PST8PDT");
    console.log("✅ Timezone select works correctly");
  });

  test("should have proper time & schedule structure", async ({ page }) => {
    console.log("🏗️ Testing time & schedule structure...");

    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("time-tab");

    // Check section titles
    const timeSyncTitle = page
      .locator("h5")
      .filter({ hasText: "Time Synchronization" });
    const nightModeTitle = page
      .locator("h5")
      .filter({ hasText: "Night Mode Schedule" });

    await expect(timeSyncTitle).toBeVisible();
    await expect(nightModeTitle).toBeVisible();
    console.log("✅ Section titles are present");

    // Check that all controls are within the time-settings tab content
    const timeContent = page.locator("#time-settings");
    const ntpControl = timeContent.locator("#ntpServer");
    const timezoneControl = timeContent.locator("#timezone");
    const autoSyncControl = timeContent.locator("#autoTimeSync");
    const nightModeControl = timeContent.locator("#nightModeEnabled");
    const nightStartHourControl = timeContent.locator("#nightStartHour");
    const nightStartMinuteControl = timeContent.locator("#nightStartMinute");
    const nightEndHourControl = timeContent.locator("#nightEndHour");
    const nightEndMinuteControl = timeContent.locator("#nightEndMinute");
    const displayOffControl = timeContent.locator("#displayOffAtNight");

    await expect(ntpControl).toBeVisible();
    await expect(timezoneControl).toBeVisible();
    await expect(autoSyncControl).toBeVisible();
    await expect(nightModeControl).toBeVisible();
    await expect(nightStartHourControl).toBeVisible();
    await expect(nightStartMinuteControl).toBeVisible();
    await expect(nightEndHourControl).toBeVisible();
    await expect(nightEndMinuteControl).toBeVisible();
    await expect(displayOffControl).toBeVisible();

    console.log("✅ All time & schedule controls are properly contained");
  });
});
