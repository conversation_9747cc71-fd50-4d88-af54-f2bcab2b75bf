const { test, expect } = require("@playwright/test");
const { ThermalSensorTestBase } = require("./base-test");

test.describe("Basic Page Load Tests", () => {
  let testBase;

  test.beforeEach(async ({ page }) => {
    testBase = new ThermalSensorTestBase(page);
  });

  test("should load the home page successfully", async ({ page }) => {
    console.log("🚀 Testing basic page load...");

    await testBase.navigateToHomePage();

    // Check page title
    await expect(page).toHaveTitle(/Child Fever Monitor/);
    console.log("✅ Page title is correct");

    // Check main container is visible
    await expect(page.locator(".main-container")).toBeVisible();
    console.log("✅ Main container is visible");
  });

  test("should display all main sections", async ({ page }) => {
    console.log("🔍 Testing main sections visibility...");

    await testBase.navigateToHomePage();
    await testBase.waitForPageLoad();

    // Header section
    await expect(page.locator(".header-section")).toBeVisible();
    await expect(page.locator(".header-section h1")).toContainText(
      "Child Fever Monitor"
    );
    console.log("✅ Header section is visible");

    // Status display
    await expect(page.locator("#statusDisplay")).toBeVisible();
    console.log("✅ Status display is visible");

    // Stats grid
    await expect(page.locator(".stats-grid")).toBeVisible();
    console.log("✅ Stats grid is visible");

    // Temperature chart container
    await expect(page.locator(".temp-chart-container")).toBeVisible();
    console.log("✅ Temperature chart container is visible");

    // Thermal section
    await expect(page.locator(".thermal-section")).toBeVisible();
    console.log("✅ Thermal section is visible");

    // Settings panel
    await expect(page.locator(".settings-panel")).toBeVisible();
    console.log("✅ Settings panel is visible");

    // Controls section
    await expect(page.locator(".controls-section")).toBeVisible();
    console.log("✅ Controls section is visible");
  });

  test("should display all stat cards with correct structure", async ({
    page,
  }) => {
    console.log("📊 Testing stat cards structure...");

    await testBase.navigateToHomePage();
    await testBase.waitForPageLoad();

    const stats = await testBase.getStatCards();

    // Should have 4 stat cards
    expect(stats).toHaveLength(4);
    console.log(`✅ Found ${stats.length} stat cards`);

    // Check expected stat cards
    const expectedStats = [
      "Current Body Temperature",
      "Baseline Temperature",
      "Temperature Difference",
      "Monitoring Duration",
    ];

    for (const expectedStat of expectedStats) {
      const found = stats.some((stat) => stat.label === expectedStat);
      expect(found).toBeTruthy();
      console.log(`✅ Found stat card: ${expectedStat}`);
    }

    // Check that all stat cards have values and units
    for (const stat of stats) {
      expect(stat.value).toBeTruthy();
      expect(stat.unit).toBeTruthy();
      console.log(
        `✅ Stat card "${stat.label}" has value: ${stat.value} ${stat.unit}`
      );
    }
  });

  test("should display all settings tabs", async ({ page }) => {
    console.log("🗂️ Testing settings tabs...");

    await testBase.navigateToHomePage();
    await testBase.waitForPageLoad();

    const expectedTabs = [
      { id: "auto-calibrate-tab", text: "Auto Calibration" },
      { id: "essential-tab", text: "Essential Settings" },
      { id: "detection-alerts-tab", text: "Detection & Alerts" },
      { id: "advanced-tab", text: "Advanced" },
      { id: "time-tab", text: "Time & Schedule" },
    ];

    for (const tab of expectedTabs) {
      await expect(page.locator(`#${tab.id}`)).toBeVisible();
      await expect(page.locator(`#${tab.id}`)).toContainText(tab.text);
      console.log(`✅ Found tab: ${tab.text}`);
    }

    // Check that Auto Calibration tab is active by default
    await expect(page.locator("#auto-calibrate-tab")).toHaveClass(/active/);
    console.log("✅ Auto Calibration tab is active by default");
  });

  test("should display all control buttons", async ({ page }) => {
    console.log("🎮 Testing control buttons...");

    await testBase.navigateToHomePage();
    await testBase.waitForPageLoad();

    const expectedButtons = [
      "Reset Baseline",
      "Calibrate Now",
      "Test Alert",
      "Test Beep",
      "Download Data",
    ];

    for (const buttonText of expectedButtons) {
      await expect(
        page.locator(`button:has-text("${buttonText}")`)
      ).toBeVisible();
      console.log(`✅ Found button: ${buttonText}`);
    }

    // Check settings buttons
    const settingsButtons = [
      "Save Settings",
      "Reset to Defaults",
      "Export Config",
      "Import Config",
    ];

    for (const buttonText of settingsButtons) {
      await expect(
        page.locator(`button:has-text("${buttonText}")`)
      ).toBeVisible();
      console.log(`✅ Found settings button: ${buttonText}`);
    }
  });

  test("should load external resources successfully", async ({ page }) => {
    console.log("🌐 Testing external resources...");

    await testBase.navigateToHomePage();

    // Check that Bootstrap CSS is loaded
    const bootstrapLink = page.locator('link[href*="bootstrap"]');
    await expect(bootstrapLink).toHaveCount(1);
    console.log("✅ Bootstrap CSS link found");

    // Check that Font Awesome CSS is loaded
    const fontAwesomeLink = page.locator('link[href*="font-awesome"]');
    await expect(fontAwesomeLink).toHaveCount(1);
    console.log("✅ Font Awesome CSS link found");

    // Check that Chart.js script is loaded (may have fallbacks)
    const chartScript = page.locator('script[src*="chart.js"]');
    await expect(chartScript).toHaveCount(2); // Main + fallback
    console.log("✅ Chart.js script found");

    // Check that heatmap.js script is loaded (may have fallbacks)
    const heatmapScript = page.locator('script[src*="heatmap"]');
    await expect(heatmapScript).toHaveCount(2); // Main + fallback
    console.log("✅ Heatmap.js script found");
  });

  test("should have responsive viewport meta tag", async ({ page }) => {
    console.log("📱 Testing responsive design setup...");

    await testBase.navigateToHomePage();

    // Check viewport meta tag
    const viewportMeta = page.locator('meta[name="viewport"]');
    await expect(viewportMeta).toHaveAttribute(
      "content",
      "width=device-width, initial-scale=1"
    );
    console.log("✅ Responsive viewport meta tag is correct");
  });
});
