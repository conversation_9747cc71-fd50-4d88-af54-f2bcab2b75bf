const { test, expect } = require("@playwright/test");
const { ThermalSensorTestBase } = require("./base-test");

test.describe("Comprehensive Integration Tests", () => {
  let testBase;

  test.beforeEach(async ({ page }) => {
    testBase = new ThermalSensorTestBase(page);
  });

  test("should load complete thermal sensor interface successfully", async ({
    page,
  }) => {
    console.log("🌟 Testing complete thermal sensor interface loading...");

    await testBase.navigateToHomePage();

    // Verify all major sections are present
    const headerSection = page.locator(".header-section");
    const statsGrid = page.locator(".stats-grid");
    const thermalSection = page.locator(".thermal-section");
    const settingsPanel = page.locator(".settings-panel");

    await expect(headerSection).toBeVisible();
    await expect(statsGrid).toBeVisible();
    await expect(thermalSection).toBeVisible();
    await expect(settingsPanel).toBeVisible();

    console.log("✅ All major interface sections loaded successfully");
  });

  test("should display all header elements correctly", async ({ page }) => {
    console.log("📋 Testing header section integration...");

    await testBase.navigateToHomePage();

    // Check title and status
    const title = page.locator("h1").filter({ hasText: "Child Fever Monitor" });
    const statusBadge = page
      .locator(".badge")
      .filter({ hasText: /Connected|Disconnected|Synced/ });

    await expect(title).toBeVisible();
    await expect(statusBadge).toBeVisible();

    // Check specific header icons
    const headerSection = page.locator(".header-section");

    // Check thermometer icon in title
    const thermometerIcon = headerSection.locator(
      "h1 i.fas.fa-thermometer-half"
    );
    await expect(thermometerIcon).toBeVisible();

    // Check WiFi icon in connection status
    const wifiIcon = headerSection.locator("#connectionStatus i.fas.fa-wifi");
    await expect(wifiIcon).toBeVisible();

    // Check clock icon
    const clockIcon = headerSection.locator("i.fas.fa-clock");
    await expect(clockIcon).toBeVisible();

    console.log("✅ Header section integration working correctly");
  });

  test("should display all stats cards with live data", async ({ page }) => {
    console.log("📊 Testing stats grid integration...");

    await testBase.navigateToHomePage();

    // Check all 4 stat cards
    const statCards = page.locator(".stat-card");
    const cardCount = await statCards.count();
    expect(cardCount).toBe(4);

    // Verify each card has complete structure
    for (let i = 0; i < cardCount; i++) {
      const card = statCards.nth(i);

      const icon = card.locator(".stat-icon");
      const label = card.locator(".stat-label");
      const value = card.locator(".stat-value");
      const unit = card.locator(".stat-unit");

      await expect(icon).toBeVisible();
      await expect(label).toBeVisible();
      await expect(value).toBeVisible();
      await expect(unit).toBeVisible();
    }

    console.log("✅ Stats grid integration working correctly");
  });

  test("should display thermal visualization components", async ({ page }) => {
    console.log("🔥 Testing thermal visualization integration...");

    await testBase.navigateToHomePage();

    // Check thermal view title
    const thermalTitle = page.locator("h4").filter({ hasText: "Thermal View" });
    await expect(thermalTitle).toBeVisible();

    // Check heatmap container
    const heatmapContainer = page.locator("#heatmapContainer");
    await expect(heatmapContainer).toBeVisible();

    // Check temperature scale
    const scaleTitle = page
      .locator("h5")
      .filter({ hasText: "Temperature Scale" });
    const colorbar = page.locator("#colorbar");

    await expect(scaleTitle).toBeVisible();
    await expect(colorbar).toBeVisible();

    console.log("✅ Thermal visualization integration working correctly");
  });

  test("should navigate through all settings tabs successfully", async ({
    page,
  }) => {
    console.log("⚙️ Testing complete settings navigation integration...");

    await testBase.navigateToHomePage();

    const tabs = [
      {
        id: "auto-calibrate-tab",
        contentId: "auto-calibrate",
        name: "Auto Calibration",
      },
      {
        id: "essential-tab",
        contentId: "essential",
        name: "Essential Settings",
      },
      {
        id: "detection-alerts-tab",
        contentId: "detection-alerts",
        name: "Detection & Alerts",
      },
      { id: "advanced-tab", contentId: "advanced", name: "Advanced" },
      { id: "time-tab", contentId: "time-settings", name: "Time & Schedule" },
    ];

    for (const tab of tabs) {
      await testBase.clickSettingsTab(tab.id);

      // Verify tab is active
      const tabElement = page.locator(`#${tab.id}`);
      await expect(tabElement).toHaveClass(/active/);

      // Verify content is visible
      const contentElement = page.locator(`#${tab.contentId}`);
      await expect(contentElement).toBeVisible();
      await expect(contentElement).toHaveClass(/active/);

      console.log(`✅ Successfully navigated to ${tab.name}`);
    }

    console.log("✅ Settings navigation integration working correctly");
  });

  test("should perform auto calibration workflow", async ({ page }) => {
    console.log("🎯 Testing auto calibration integration...");

    await testBase.navigateToHomePage();

    // Navigate to auto calibration (should be default)
    const autoCalibContent = page.locator("#auto-calibrate");
    await expect(autoCalibContent).toBeVisible();

    // Get current fever status to use realistic values
    const currentStatus = await testBase.getCurrentFeverStatus();
    const bodyTemp =
      currentStatus.currentTemp > 0 ? currentStatus.currentTemp : 98.6;
    const ambientTemp =
      currentStatus.ambient.available && currentStatus.ambient.temperatureF
        ? currentStatus.ambient.temperatureF
        : 72.0;

    // Fill in calibration values
    await page.locator("#childBodyTemp").fill(bodyTemp.toString());
    await page.locator("#ambientRoomTemp").fill(ambientTemp.toString());
    await page.waitForTimeout(1000);

    // Check that calculations are updated
    const distanceComp = page.locator("#calculatedDistanceComp");
    const baselineMin = page.locator("#calculatedBaselineMin");

    const distanceValue = await distanceComp.textContent();
    const baselineValue = await baselineMin.textContent();

    expect(distanceValue.trim()).not.toBe("");
    expect(baselineValue.trim()).not.toBe("");

    // Test calibration button
    const calibrateButton = page.locator("#performAutoCalibration");
    await expect(calibrateButton).toBeVisible();
    await calibrateButton.click();

    console.log("✅ Auto calibration integration working correctly");
  });

  test("should interact with essential settings controls", async ({ page }) => {
    console.log("🔧 Testing essential settings integration...");

    await testBase.navigateToHomePage();
    await testBase.clickSettingsTab("essential-tab");

    // Test distance compensation
    const distanceSlider = page.locator("#distanceComp");
    const distanceValue = page.locator("#distanceCompValue");

    const initialDistance = await distanceValue.textContent();
    await distanceSlider.evaluate((el, value) => {
      el.value = value;
      el.dispatchEvent(new Event("input", { bubbles: true }));
      el.dispatchEvent(new Event("change", { bubbles: true }));
    }, "10");
    await page.waitForTimeout(1000);

    const updatedDistance = await distanceValue.textContent();
    // Check that value changed or contains expected format
    const isUpdated =
      updatedDistance !== initialDistance || updatedDistance.includes("°F");
    expect(isUpdated).toBe(true);

    // Test ambient temperature
    const ambientSlider = page.locator("#ambientTemp");
    const ambientValue = page.locator("#ambientTempValue");

    await ambientSlider.evaluate((el, value) => {
      el.value = value;
      el.dispatchEvent(new Event("input", { bubbles: true }));
      el.dispatchEvent(new Event("change", { bubbles: true }));
    }, "75");
    await page.waitForTimeout(500);

    const updatedAmbient = await ambientValue.textContent();
    expect(updatedAmbient).toContain("75");

    // Test person detection threshold
    const personSlider = page.locator("#personThreshold");
    const personValue = page.locator("#personThresholdValue");

    await personSlider.evaluate((el, value) => {
      el.value = value;
      el.dispatchEvent(new Event("input", { bubbles: true }));
      el.dispatchEvent(new Event("change", { bubbles: true }));
    }, "8");
    await page.waitForTimeout(500);

    const updatedPerson = await personValue.textContent();
    expect(updatedPerson).toContain("8");

    console.log("✅ Essential settings integration working correctly");
  });

  test("should maintain state across tab navigation", async ({ page }) => {
    console.log("🔄 Testing state persistence integration...");

    await testBase.navigateToHomePage();

    // Get current fever status to use realistic values
    const currentStatus = await testBase.getCurrentFeverStatus();
    const bodyTemp =
      currentStatus.currentTemp > 0 ? currentStatus.currentTemp + 0.9 : 99.5;
    const ambientTemp =
      currentStatus.ambient.available && currentStatus.ambient.temperatureF
        ? Math.max(currentStatus.ambient.temperatureF - 1.5, 68.0)
        : 70.5;

    // Set values in auto calibration
    await page.locator("#childBodyTemp").fill(bodyTemp.toString());
    await page.locator("#ambientRoomTemp").fill(ambientTemp.toString());
    await page.waitForTimeout(500);

    // Navigate to essential settings and set values
    await testBase.clickSettingsTab("essential-tab");
    const initialDistanceValue = await page
      .locator("#distanceComp")
      .inputValue();
    const initialAmbientValue = await page.locator("#ambientTemp").inputValue();

    await page.locator("#distanceComp").evaluate((el, value) => {
      el.value = value;
      el.dispatchEvent(new Event("input", { bubbles: true }));
      el.dispatchEvent(new Event("change", { bubbles: true }));
    }, "15");
    await page.locator("#ambientTemp").evaluate((el, value) => {
      el.value = value;
      el.dispatchEvent(new Event("input", { bubbles: true }));
      el.dispatchEvent(new Event("change", { bubbles: true }));
    }, "78");
    await page.waitForTimeout(500);

    // Navigate back to auto calibration and check values
    await testBase.clickSettingsTab("auto-calibrate-tab");

    const bodyTempValue = await page.locator("#childBodyTemp").inputValue();
    const ambientRoomValue = await page
      .locator("#ambientRoomTemp")
      .inputValue();

    // Verify the values match what we set (using the dynamic values)
    expect(parseFloat(bodyTempValue)).toBeCloseTo(bodyTemp, 1);
    expect(parseFloat(ambientRoomValue)).toBeCloseTo(ambientTemp, 1);

    // Navigate back to essential settings and verify interface works
    await testBase.clickSettingsTab("essential-tab");

    // Just verify that the controls are still functional
    const distanceSlider = page.locator("#distanceComp");
    const ambientSlider = page.locator("#ambientTemp");

    await expect(distanceSlider).toBeVisible();
    await expect(ambientSlider).toBeVisible();

    // Test that we can still interact with the controls
    await distanceSlider.evaluate((el, value) => {
      el.value = value;
      el.dispatchEvent(new Event("input", { bubbles: true }));
      el.dispatchEvent(new Event("change", { bubbles: true }));
    }, "12");
    await ambientSlider.evaluate((el, value) => {
      el.value = value;
      el.dispatchEvent(new Event("input", { bubbles: true }));
      el.dispatchEvent(new Event("change", { bubbles: true }));
    }, "76");
    await page.waitForTimeout(500);

    // Verify the sliders accepted the values
    const finalDistanceValue = await distanceSlider.inputValue();
    const finalAmbientValue = await ambientSlider.inputValue();

    expect(finalDistanceValue).toBe("12");
    expect(finalAmbientValue).toBe("76");

    console.log("✅ State persistence integration working correctly");
  });

  test("should handle responsive design across viewports", async ({ page }) => {
    console.log("📱 Testing responsive design integration...");

    await testBase.navigateToHomePage();

    const viewports = [
      { width: 1200, height: 800, name: "Desktop" },
      { width: 768, height: 1024, name: "Tablet" },
      { width: 375, height: 667, name: "Mobile" },
    ];

    for (const viewport of viewports) {
      await page.setViewportSize({
        width: viewport.width,
        height: viewport.height,
      });
      await page.waitForTimeout(500);

      // Check that all major sections remain visible
      const headerSection = page.locator(".header-section");
      const statsGrid = page.locator(".stats-grid");
      const thermalSection = page.locator(".thermal-section");
      const settingsPanel = page.locator(".settings-panel");

      await expect(headerSection).toBeVisible();
      await expect(statsGrid).toBeVisible();
      await expect(thermalSection).toBeVisible();
      await expect(settingsPanel).toBeVisible();

      console.log(`✅ Responsive design working on ${viewport.name}`);
    }

    // Reset to default viewport
    await page.setViewportSize({ width: 1280, height: 720 });

    console.log("✅ Responsive design integration working correctly");
  });

  test("should demonstrate complete user workflow", async ({ page }) => {
    console.log("🎬 Testing complete user workflow integration...");

    await testBase.navigateToHomePage();

    // Step 1: User views current status
    const statusBadge = page.locator(".badge").first();
    const statusText = await statusBadge.textContent();
    console.log(`Current system status: ${statusText}`);

    // Step 2: User checks current stats
    const statCards = page.locator(".stat-card");
    const cardCount = await statCards.count();
    console.log(`Viewing ${cardCount} stat cards`);

    // Step 3: User performs auto calibration
    // Get current fever status to use realistic values
    const currentStatus = await testBase.getCurrentFeverStatus();
    const bodyTemp =
      currentStatus.currentTemp > 0 ? currentStatus.currentTemp : 98.6;
    const ambientTemp =
      currentStatus.ambient.available && currentStatus.ambient.temperatureF
        ? currentStatus.ambient.temperatureF
        : 72.0;

    await page.locator("#childBodyTemp").fill(bodyTemp.toString());
    await page.locator("#ambientRoomTemp").fill(ambientTemp.toString());
    await page.waitForTimeout(1000);

    const calibrateButton = page.locator("#performAutoCalibration");
    await calibrateButton.click();
    console.log("✅ Performed auto calibration");

    // Step 4: User adjusts essential settings
    await testBase.clickSettingsTab("essential-tab");
    await page.locator("#distanceComp").evaluate((el, value) => {
      el.value = value;
      el.dispatchEvent(new Event("input", { bubbles: true }));
      el.dispatchEvent(new Event("change", { bubbles: true }));
    }, "8");
    await page.locator("#ambientTemp").evaluate((el, value) => {
      el.value = value;
      el.dispatchEvent(new Event("input", { bubbles: true }));
      el.dispatchEvent(new Event("change", { bubbles: true }));
    }, "74");
    await page.waitForTimeout(500);
    console.log("✅ Adjusted essential settings");

    // Step 5: User views thermal visualization
    const heatmapContainer = page.locator("#heatmapContainer");
    await expect(heatmapContainer).toBeVisible();
    console.log("✅ Viewed thermal visualization");

    // Step 6: User navigates through other settings
    await testBase.clickSettingsTab("detection-alerts-tab");
    await testBase.clickSettingsTab("advanced-tab");
    await testBase.clickSettingsTab("time-tab");
    console.log("✅ Explored all settings sections");

    console.log("✅ Complete user workflow integration working correctly");
  });
});
