#ifndef AHT10_SENSOR_H
#define AHT10_SENSOR_H

#include <Adafruit_AHTX0.h>
#include <Wire.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>

// AHT10 uses shared I2C bus (SDA=GPIO9, SCL=GPIO4)

// AHT10 sensor data structure
struct AHT10Data {
  float temperature;    // Temperature in Celsius
  float humidity;       // Relative humidity in %
  float temperatureF;   // Temperature in Fahrenheit
  bool isValid;         // Data validity flag
  unsigned long lastUpdate; // Last update timestamp
};

// Global variables
extern AHT10Data aht10Data;
extern Adafruit_AHTX0 aht10;
extern TaskHandle_t aht10TaskHandle;
extern bool aht10Initialized;

// Function declarations
void initAHT10();
void aht10Task(void *pvParameters);
void startAHT10Monitoring();
void stopAHT10Monitoring();
bool isAHT10Available();
AHT10Data getAHT10Data();
void updateAHT10Reading();

// Utility functions
float celsiusToFahrenheit(float celsius);
String getAHT10StatusString();

#endif
