#include "aht10_sensor.h"

// Global variables
AHT10Data aht10Data = {0.0, 0.0, 0.0, false, 0};
Adafruit_AHTX0 aht10;
TaskHandle_t aht10TaskHandle = NULL;
bool aht10Initialized = false;

void initAHT10() {
  Serial.println("Initializing AHT10 sensor on shared I2C bus...");

  // AHT10 uses the shared I2C bus (already initialized in thermal_sensor.cpp)
  // No need to initialize I2C bus again - just initialize the sensor

  delay(100); // Allow I2C bus to stabilize

  // Initialize AHT10 sensor on shared Wire bus
  if (aht10.begin()) {
    aht10Initialized = true;
    Serial.println("AHT10 sensor initialized successfully on shared I2C bus!");
    Serial.println("AHT10 I2C pins - SDA: GPIO9, SCL: GPIO4 (shared with <PERSON>G8833)");

    // Take initial reading
    updateAHT10Reading();

    // Start continuous monitoring task
    startAHT10Monitoring();
  } else {
    aht10Initialized = false;
    Serial.println("Failed to initialize AHT10 sensor!");
    Serial.println("Check wiring:");
    Serial.println("  AHT10 VIN -> ESP32 3.3V");
    Serial.println("  AHT10 GND -> ESP32 GND");
    Serial.println("  AHT10 SDA -> ESP32 GPIO9 (shared with AMG8833)");
    Serial.println("  AHT10 SCL -> ESP32 GPIO4 (shared with AMG8833)");
  }
}

void updateAHT10Reading() {
  if (!aht10Initialized) {
    return;
  }
  
  sensors_event_t humidity, temp;
  if (aht10.getEvent(&humidity, &temp)) {
    aht10Data.temperature = temp.temperature;
    aht10Data.humidity = humidity.relative_humidity;
    aht10Data.temperatureF = celsiusToFahrenheit(temp.temperature);
    aht10Data.isValid = true;
    aht10Data.lastUpdate = millis();
    
    // Debug output - print every reading since we're only reading every 30 seconds
    Serial.print("AHT10 - Temp: ");
    Serial.print(aht10Data.temperatureF, 1);
    Serial.print("°F (");
    Serial.print(aht10Data.temperature, 1);
    Serial.print("°C), Humidity: ");
    Serial.print(aht10Data.humidity, 1);
    Serial.println("%");
  } else {
    Serial.println("Failed to read from AHT10 sensor");
    aht10Data.isValid = false;
  }
}

void aht10Task(void *pvParameters) {
  Serial.println("AHT10 monitoring task started");

  const TickType_t readInterval = pdMS_TO_TICKS(30000); // Read every 30 seconds to reduce I2C bus contention
  TickType_t lastWakeTime = xTaskGetTickCount();
  
  while (true) {
    // Update sensor reading
    updateAHT10Reading();
    
    // Wait for next reading interval
    vTaskDelayUntil(&lastWakeTime, readInterval);
    
    // Yield to other tasks
    yield();
  }
}

void startAHT10Monitoring() {
  if (!aht10Initialized) {
    Serial.println("Cannot start AHT10 monitoring - sensor not initialized");
    return;
  }
  
  if (aht10TaskHandle != NULL) {
    Serial.println("AHT10 monitoring task already running");
    return;
  }
  
  // Create the monitoring task on Core 0 (opposite of main loop)
  BaseType_t result = xTaskCreatePinnedToCore(
    aht10Task,              // Function to run
    "AHT10Task",            // Name
    4096,                   // Stack size
    NULL,                   // Parameter
    2,                      // Priority (higher than animation task)
    &aht10TaskHandle,       // Task handle
    0                       // Core 0
  );
  
  if (result == pdPASS && aht10TaskHandle != NULL) {
    Serial.println("AHT10 monitoring task started successfully");
  } else {
    Serial.println("Failed to create AHT10 monitoring task");
    aht10TaskHandle = NULL;
  }
}

void stopAHT10Monitoring() {
  if (aht10TaskHandle != NULL) {
    vTaskDelete(aht10TaskHandle);
    aht10TaskHandle = NULL;
    Serial.println("AHT10 monitoring task stopped");
  }
}

bool isAHT10Available() {
  return aht10Initialized && aht10Data.isValid;
}

AHT10Data getAHT10Data() {
  return aht10Data;
}

float celsiusToFahrenheit(float celsius) {
  return (celsius * 9.0 / 5.0) + 32.0;
}

String getAHT10StatusString() {
  if (!aht10Initialized) {
    return "AHT10: Not initialized";
  }
  
  if (!aht10Data.isValid) {
    return "AHT10: No valid data";
  }
  
  unsigned long timeSinceUpdate = millis() - aht10Data.lastUpdate;
  if (timeSinceUpdate > 10000) { // More than 10 seconds old
    return "AHT10: Data stale";
  }
  
  String status = "AHT10: ";
  status += String(aht10Data.temperatureF, 1);
  status += "°F, ";
  status += String(aht10Data.humidity, 1);
  status += "%";
  
  return status;
}
