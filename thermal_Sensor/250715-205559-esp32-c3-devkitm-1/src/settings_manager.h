#ifndef SETTINGS_MANAGER_H
#define SETTINGS_MANAGER_H

#include <Preferences.h>

// Settings structure to organize all persistent configuration
struct ThermalSettings {
  // Fever detection settings
  float feverThreshold;        // Temperature difference for fever detection (°F)
  float distanceCompensation;  // Distance compensation factor (°F)
  float ambientTemp;          // Ambient/room temperature (°F)
  float personThreshold;      // Temperature above ambient to detect person presence (°F)
  bool dynamicCompensation;   // Enable dynamic ambient-based compensation
  float compensationSensitivity; // Sensitivity for dynamic compensation adjustments (0-100%)
  
  // Baseline settings
  float baselineTemp;         // Learned baseline temperature (°F)
  bool baselineEstablished;   // Whether baseline has been established

  // Advanced settings
  float medicalThreshold;     // Medical fever threshold (°F)
  int sustainedTime;          // Sustained fever time (minutes)
  int baselineDuration;       // Baseline learning duration (minutes)
  float baselineMax;          // Maximum acceptable baseline temp (°F)
  float baselineMin;          // Minimum temp to start baseline learning (°F)
  
  // System settings
  bool debugMode;             // Enable debug output
  int readInterval;           // Sensor read interval (ms)
  int logInterval;            // Temperature logging interval (seconds)

  // Display settings
  int displayBrightness;      // OLED display brightness (0-255)

  // Audio settings
  bool buzzerEnabled;         // Enable/disable buzzer sounds

  // Time and schedule settings
  char ntpServer[64];         // NTP server URL (e.g., "pool.ntp.org")
  char timezone[32];          // Timezone string (e.g., "EST5EDT", "PST8PDT")
  int gmtOffsetSec;           // GMT offset in seconds
  int daylightOffsetSec;      // Daylight saving offset in seconds
  bool autoTimeSync;          // Enable automatic NTP time synchronization
  int timeSyncInterval;       // Time sync interval in hours

  // Night mode settings
  bool nightModeEnabled;      // Enable automatic night mode
  int nightStartHour;         // Night mode start hour (0-23)
  int nightStartMinute;       // Night mode start minute (0-59)
  int nightEndHour;           // Night mode end hour (0-23)
  int nightEndMinute;         // Night mode end minute (0-59)
  bool displayOffAtNight;     // Turn off display during night mode

  // Volume settings (0-100%)
  int dayVolume;              // Volume during day mode
  int nightVolume;            // Volume during night mode (non-fever sounds)
  int feverVolume;            // Volume for fever alerts (day and night)

  // Version for settings migration
  int version;
};

// Default settings values
extern ThermalSettings DEFAULT_SETTINGS;

// Settings manager class
class SettingsManager {
private:
  Preferences preferences;
  ThermalSettings currentSettings;
  static const char* NAMESPACE;
  static const int CURRENT_VERSION;
  
public:
  SettingsManager();
  ~SettingsManager();
  
  // Core functions
  bool begin();
  void end();
  
  // Settings operations
  bool loadSettings();
  bool saveSettings();
  bool resetToDefaults();
  
  // Getters
  ThermalSettings getSettings() const;
  float getFeverThreshold() const;
  float getDistanceCompensation() const;
  float getAmbientTemp() const;
  float getPersonThreshold() const;
  float getBaselineTemp() const;
  bool isBaselineEstablished() const;
  float getBaselineMin() const;
  bool isBuzzerEnabled() const;
  bool isDynamicCompensationEnabled() const;
  float getCompensationSensitivity() const;
  int getReadInterval() const;
  int getLogInterval() const;
  bool isDebugMode() const;
  int getDisplayBrightness() const;

  // Time settings getters
  const char* getNtpServer() const;
  const char* getTimezone() const;
  int getGmtOffsetSec() const;
  int getDaylightOffsetSec() const;
  bool isAutoTimeSyncEnabled() const;
  int getTimeSyncInterval() const;
  bool isNightModeEnabled() const;
  int getNightStartHour() const;
  int getNightStartMinute() const;
  int getNightEndHour() const;
  int getNightEndMinute() const;
  bool isDisplayOffAtNight() const;

  // Volume settings getters
  int getDayVolume() const;
  int getNightVolume() const;
  int getFeverVolume() const;

  // Setters
  void setFeverThreshold(float value);
  void setDistanceCompensation(float value);
  void setAmbientTemp(float value);
  void setPersonThreshold(float value);
  void setBaselineTemp(float value);
  void setBaselineEstablished(bool value);
  void setMedicalThreshold(float value);
  void setDynamicCompensation(bool enabled);
  void setCompensationSensitivity(float value);
  void setSustainedTime(int value);
  void setDebugMode(bool value);
  void setBaselineMin(float value);
  void setDisplayBrightness(int value);
  void setBuzzerEnabled(bool enabled);
  void setReadInterval(int value);
  void setLogInterval(int value);

  // Time settings setters
  void setNtpServer(const char* server);
  void setTimezone(const char* tz);
  void setGmtOffsetSec(int offset);
  void setDaylightOffsetSec(int offset);
  void setAutoTimeSync(bool enabled);
  void setTimeSyncInterval(int hours);
  void setNightModeEnabled(bool enabled);
  void setNightStartTime(int hour, int minute);
  void setNightEndTime(int hour, int minute);
  void setDisplayOffAtNight(bool enabled);

  // Volume settings setters
  void setDayVolume(int volume);
  void setNightVolume(int volume);
  void setFeverVolume(int volume);

  // Bulk operations
  void updateSettings(const ThermalSettings& settings);
  void applySettingsToGlobals();
  void collectSettingsFromGlobals();
  
  // Utility functions
  bool hasStoredSettings();
  void printSettings();
  String getSettingsAsJson();
  bool setSettingsFromJson(const String& json);
  
  // Migration support
  bool migrateSettings(int fromVersion);
};

// Global settings manager instance
extern SettingsManager settingsManager;

// Convenience functions for easy access
void initSettingsManager();
void saveCurrentSettings();
void loadStoredSettings();
void resetSettingsToDefaults();

#endif
