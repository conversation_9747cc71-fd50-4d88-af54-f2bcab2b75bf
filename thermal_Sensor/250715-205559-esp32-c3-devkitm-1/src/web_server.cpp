#include <WebServer.h>
#include <SPIFFS.h>
#include "thermal_sensor.h"
#include "web_server.h"
#include "web_handlers.h"

WebServer server(80);

// Root handler is now in web_handlers.cpp




void initWebServer() {
  // Initialize SPIFFS for serving static files
  if (!SPIFFS.begin(true)) {
    Serial.println("An error occurred while mounting SPIFFS");
    return;
  }
  Serial.println("SPIFFS mounted successfully");

  // Static file routes
  server.on("/", handleRoot);
  server.on("/debug", handleDebug);
  server.on("/css/styles.css", handleCSS);
  server.on("/js/fever-monitor.js", handleJS);
  server.on("/sw.js", handleServiceWorker);

  // API routes
  server.on("/pixels", handlePixels);
  server.on("/fever-status", handleFeverStatus);
  server.on("/ambient-data", handleAmbientData);
  server.on("/get-settings", handleGetSettings);
  server.on("/reset-baseline", HTTP_POST, handleResetBaseline);
  server.on("/save-settings", HTTP_POST, handleSaveSettings);
  server.on("/calibrate", HTTP_POST, handleCalibrate);
  server.on("/auto-calibrate", HTTP_POST, handleAutoCalibrate);
  server.on("/reset-settings", HTTP_POST, handleResetSettings);
  server.on("/settings-debug", handleSettingsDebug);
  server.on("/test-save", HTTP_POST, handleTestSave);
  server.on("/test-load", HTTP_POST, handleTestLoad);
  server.on("/test-buzzer", HTTP_POST, handleTestBuzzer);
  server.on("/set-display-brightness", HTTP_POST, handleSetDisplayBrightness);

  // Time management endpoints
  server.on("/time-status", handleGetTimeStatus);
  server.on("/sync-time", HTTP_POST, handleSyncTime);
  server.on("/set-time", HTTP_POST, handleSetTime);
  server.on("/save-time-settings", HTTP_POST, handleSaveTimeSettings);
  server.on("/reset-time-settings", HTTP_POST, handleResetTimeSettings);

  // SSE endpoint removed - using polling instead

  // Handle file requests that don't match specific routes
  server.onNotFound([]() {
    if (!handleFileRead(server.uri())) {
      handleNotFound();
    }
  });

  server.begin();
  Serial.println("HTTP server started with modular web interface");
  Serial.println("Available endpoints:");
  Serial.println("  GET  / - Main dashboard (served from SPIFFS)");
  Serial.println("  GET  /debug - Debug page for troubleshooting");
  Serial.println("  GET  /css/styles.css - Stylesheet");
  Serial.println("  GET  /js/fever-monitor.js - JavaScript module");
  Serial.println("  GET  /sw.js - Service Worker for background notifications");
  Serial.println("  GET  /pixels - Thermal data API");
  Serial.println("  GET  /fever-status - Fever monitoring status API");
  Serial.println("  GET  /fever-status - Fever monitoring status API (polling)");
  Serial.println("  GET  /settings-debug - Settings debug information");
  Serial.println("  POST /reset-baseline - Reset temperature baseline");
  Serial.println("  POST /save-settings - Save configuration");
  Serial.println("  POST /calibrate - Calibrate sensor");
  Serial.println("  POST /auto-calibrate - Perform auto calibration with child body temperature");
  Serial.println("  POST /reset-settings - Reset all settings to factory defaults");
}

void handleWebServer() {
  server.handleClient();
}
