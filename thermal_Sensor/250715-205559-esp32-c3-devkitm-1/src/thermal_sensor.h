#ifndef THERMAL_SENSOR_H
#define THERMAL_SENSOR_H

#include <Adafruit_AMG88xx.h>

extern Adafruit_AMG88xx amg;
extern float pixels[AMG88xx_PIXEL_ARRAY_SIZE];
extern float compensatedPixels[AMG88xx_PIXEL_ARRAY_SIZE]; // Compensated pixel data for web display
extern float maxTemp, minTemp;
// Global var for last pixel read time in millis
extern unsigned long lastPixelReadTime;

// Fever detection variables
extern float baselineTemp;
extern float currentBodyTemp;
extern bool feverDetected;
extern unsigned long feverStartTime;
extern float feverThreshold;
extern bool baselineEstablished;
extern float baselineMin;
extern float tempHistory[100];
extern int historyIndex;
extern unsigned long lastTempLog;

// Sustained fever monitoring variables
extern unsigned long feverConditionStartTime;
extern bool wasFeverCondition;

// Distance compensation for 4 feet
extern float distanceCompensation;
extern float ambientTemp;
extern float personThreshold;

// Function to get current ambient temperature (AHT10 if available, otherwise stored setting)
float getCurrentAmbientTemp();

void initSensor();
void readPixels();
void calculateTempRange();
void orientPixels();
void applyCompensationToPixels(); // Apply distance compensation to all pixels

// New fever detection functions
void initFeverDetection();
float applySmartCompensation(float rawTempF); // Centralized compensation logic
float calculateDynamicCompensation(float rawTempF, float ambientTempF); // Dynamic ambient-based compensation
float calculateBodyTemperature();
void updateBaseline();
void resetBaselineVariables(); // New function to reset static variables
void checkBaselineUnlearning(); // Check if baseline should be unlearned
bool checkForFever();
void logTemperature(float temp);
float getAverageTemp(int samples);
void calibrateForDistance();
String getFeverStatus();

#endif
