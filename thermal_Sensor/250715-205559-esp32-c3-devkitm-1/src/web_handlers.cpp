#include "web_handlers.h"
#include "thermal_sensor.h"
#include "settings_manager.h"
#include "oled_display.h"
#include "aht10_sensor.h"
#include "time_manager.h"
#include "buzzer.h"
#include <SPIFFS.h>
#include <ArduinoJson.h>

// Static file handlers
void handleRoot() {
  if (!handleFileRead("/html/index.html")) {
    // Fallback: serve basic HTML if SPIFFS file not found
    String html = "<!DOCTYPE html><html><head><title>Thermal Sensor Monitor</title>";
    html += "<meta name='viewport' content='width=device-width, initial-scale=1'>";
    html += "<style>body{font-family:Arial;margin:20px;background:#f0f0f0;}";
    html += ".container{max-width:800px;margin:0 auto;background:white;padding:20px;border-radius:10px;box-shadow:0 2px 10px rgba(0,0,0,0.1);}";
    html += ".status{padding:15px;margin:10px 0;border-radius:5px;text-align:center;font-weight:bold;}";
    html += ".normal{background:#d4edda;color:#155724;border:1px solid #c3e6cb;}";
    html += ".fever{background:#f8d7da;color:#721c24;border:1px solid #f5c6cb;}";
    html += ".stats{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:15px;margin:20px 0;}";
    html += ".stat{background:#e9ecef;padding:15px;border-radius:5px;text-align:center;}";
    html += ".controls{text-align:center;margin:20px 0;}";
    html += "button{background:#007bff;color:white;border:none;padding:10px 20px;margin:5px;border-radius:5px;cursor:pointer;}";
    html += "button:hover{background:#0056b3;}</style></head><body>";
    html += "<div class='container'><h1>🌡️ Thermal Sensor Monitor</h1>";
    html += "<div id='status' class='status normal'>System Loading...</div>";
    html += "<div class='stats'>";
    html += "<div class='stat'><h3>Current Temp</h3><div id='currentTemp'>--°F</div></div>";
    html += "<div class='stat'><h3>Baseline Temp</h3><div id='baselineTemp'>--°F</div></div>";
    html += "<div class='stat'><h3>Fever Status</h3><div id='feverStatus'>Normal</div></div>";
    html += "<div class='stat'><h3>Duration</h3><div id='duration'>--</div></div>";
    html += "</div>";
    html += "<div class='controls'>";
    html += "<button onclick='resetBaseline()'>Reset Baseline</button>";
    html += "<button onclick='calibrate()'>Calibrate</button>";
    html += "<button onclick='location.reload()'>Refresh</button>";
    html += "</div>";
    html += "<p><strong>Note:</strong> SPIFFS filesystem not uploaded. Upload with: <code>pio run --target uploadfs</code></p>";
    html += "<script>";
    html += "function updateData(){fetch('/fever-status').then(r=>r.json()).then(d=>{";
    html += "document.getElementById('currentTemp').textContent=d.currentTemp.toFixed(1)+'°F';";
    html += "document.getElementById('baselineTemp').textContent=d.baselineTemp.toFixed(1)+'°F';";
    html += "document.getElementById('feverStatus').textContent=d.feverDetected?'FEVER':'Normal';";
    html += "document.getElementById('duration').textContent=d.feverDuration+' min';";
    html += "document.getElementById('status').textContent=d.status;";
    html += "document.getElementById('status').className='status '+(d.feverDetected?'fever':'normal');";
    html += "}).catch(e=>console.error('Error:',e));}";
    html += "function resetBaseline(){fetch('/reset-baseline',{method:'POST'}).then(()=>alert('Baseline reset'));}";
    html += "function calibrate(){fetch('/calibrate',{method:'POST'}).then(()=>alert('Calibrated'));}";
    html += "setInterval(updateData,2000);updateData();";
    html += "</script></div></body></html>";
    server.send(200, "text/html", html);
  }
}

void handleCSS() {
  if (!handleFileRead("/css/styles.css")) {
    server.send(404, "text/plain", "CSS file not found");
  }
}

void handleJS() {
  if (!handleFileRead("/js/fever-monitor.js")) {
    server.send(404, "text/plain", "JavaScript file not found");
  }
}

void handleServiceWorker() {
  if (!handleFileRead("/sw.js")) {
    server.send(404, "text/plain", "Service Worker file not found");
  }
}

void handleDebug() {
  if (!handleFileRead("/html/debug.html")) {
    server.send(404, "text/plain", "Debug page not found");
  }
}

void handleNotFound() {
  String message = "File Not Found\n\n";
  message += "URI: ";
  message += server.uri();
  message += "\nMethod: ";
  message += (server.method() == HTTP_GET) ? "GET" : "POST";
  message += "\nArguments: ";
  message += server.args();
  message += "\n";
  
  for (uint8_t i = 0; i < server.args(); i++) {
    message += " " + server.argName(i) + ": " + server.arg(i) + "\n";
  }
  
  server.send(404, "text/plain", message);
}

// API handlers
void handlePixels() {
  // Debug: Log when pixels endpoint is called
  static unsigned long lastPixelRequest = 0;
  if (millis() - lastPixelRequest > 5000) { // Log every 5 seconds max
    Serial.println("=== /pixels endpoint called ===");
    Serial.print("Raw pixel data sample: ");
    for (int i = 0; i < 8; i++) {
      Serial.print(pixels[i], 2);
      Serial.print("°C ");
    }
    Serial.println();
    Serial.print("Compensated pixel data sample: ");
    for (int i = 0; i < 8; i++) {
      Serial.print(compensatedPixels[i], 2);
      Serial.print("°F ");
    }
    Serial.println();
    Serial.print("Raw range: ");
    Serial.print(minTemp, 2);
    Serial.print("°C to ");
    Serial.print(maxTemp, 2);
    Serial.println("°C");
    lastPixelRequest = millis();
  }

  // Return compensated pixel data in Fahrenheit for consistent thermal visualization
  // This matches the same compensation applied to body temperature readings
  String json = "[";
  for (int i = 0; i < AMG88xx_PIXEL_ARRAY_SIZE; i++) {
    json += String(compensatedPixels[i], 2);
    if (i < AMG88xx_PIXEL_ARRAY_SIZE - 1) json += ",";
  }
  json += "]";

  // Add CORS headers for debugging
  server.sendHeader("Access-Control-Allow-Origin", "*");
  server.sendHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
  server.sendHeader("Access-Control-Allow-Headers", "Content-Type");

  server.send(200, "application/json", json);
}

void handleFeverStatus() {
  String json = "{";
  json += "\"currentTemp\":" + String(currentBodyTemp, 2) + ",";
  json += "\"baselineTemp\":" + String(baselineTemp, 2) + ",";
  json += "\"feverDetected\":" + String(feverDetected ? "true" : "false") + ",";
  json += "\"baselineEstablished\":" + String(baselineEstablished ? "true" : "false") + ",";
  json += "\"status\":\"" + getFeverStatus() + "\",";

  if (feverDetected && feverStartTime > 0) {
    unsigned long feverDuration = (millis() - feverStartTime) / 1000 / 60; // minutes
    json += "\"feverDuration\":" + String(feverDuration) + ",";
  } else {
    json += "\"feverDuration\":0,";
  }

  // Add current compensation information
  float currentAmbient = getCurrentAmbientTemp();
  float activeCompensation = calculateDynamicCompensation(maxTemp * 9/5 + 32, currentAmbient);
  json += "\"activeCompensation\":" + String(activeCompensation, 2) + ",";
  json += "\"baseCompensation\":" + String(distanceCompensation, 2) + ",";
  json += "\"currentAmbient\":" + String(currentAmbient, 2) + ",";
  json += "\"dynamicEnabled\":" + String(settingsManager.isDynamicCompensationEnabled() ? "true" : "false") + ",";

  // Add AHT10 ambient sensor data
  AHT10Data aht10Data = getAHT10Data();
  json += "\"ambient\":{";
  json += "\"available\":" + String(isAHT10Available() ? "true" : "false") + ",";
  if (isAHT10Available()) {
    json += "\"temperature\":" + String(aht10Data.temperature, 2) + ",";
    json += "\"temperatureF\":" + String(aht10Data.temperatureF, 2) + ",";
    json += "\"humidity\":" + String(aht10Data.humidity, 1) + ",";
    json += "\"lastUpdate\":" + String(aht10Data.lastUpdate);
  } else {
    json += "\"temperature\":null,";
    json += "\"temperatureF\":null,";
    json += "\"humidity\":null,";
    json += "\"lastUpdate\":null";
  }
  json += "}";

  json += "}";
  server.send(200, "application/json", json);
}

void handleAmbientData() {
  AHT10Data aht10Data = getAHT10Data();

  String json = "{";
  json += "\"available\":" + String(isAHT10Available() ? "true" : "false") + ",";
  json += "\"initialized\":" + String(aht10Initialized ? "true" : "false") + ",";

  if (isAHT10Available()) {
    json += "\"temperature\":" + String(aht10Data.temperature, 2) + ",";
    json += "\"temperatureF\":" + String(aht10Data.temperatureF, 2) + ",";
    json += "\"humidity\":" + String(aht10Data.humidity, 1) + ",";
    json += "\"lastUpdate\":" + String(aht10Data.lastUpdate) + ",";

    // Calculate data age
    unsigned long dataAge = millis() - aht10Data.lastUpdate;
    json += "\"dataAge\":" + String(dataAge) + ",";
    json += "\"isStale\":" + String(dataAge > 10000 ? "true" : "false");
  } else {
    json += "\"temperature\":null,";
    json += "\"temperatureF\":null,";
    json += "\"humidity\":null,";
    json += "\"lastUpdate\":null,";
    json += "\"dataAge\":null,";
    json += "\"isStale\":true";
  }

  json += ",\"status\":\"" + getAHT10StatusString() + "\"";
  json += "}";

  // Add CORS headers
  server.sendHeader("Access-Control-Allow-Origin", "*");
  server.sendHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
  server.sendHeader("Access-Control-Allow-Headers", "Content-Type");

  server.send(200, "application/json", json);
}

void handleResetBaseline() {
  // Reset baseline learning
  baselineEstablished = false;
  baselineTemp = 0.0;
  feverDetected = false;
  feverStartTime = 0;

  // Clear temperature history
  for (int i = 0; i < 100; i++) {
    tempHistory[i] = 0.0;
  }
  historyIndex = 0;

  // Reset the static variables in updateBaseline function
  resetBaselineVariables();

  // Update settings manager and save to persistent storage
  settingsManager.setBaselineTemp(0.0);
  settingsManager.setBaselineEstablished(false);
  settingsManager.collectSettingsFromGlobals();
  settingsManager.saveSettings();

  Serial.println("Baseline reset - learning new baseline...");
  server.send(200, "text/plain", "Baseline reset successfully");
}

void handleGetSettings() {
  ThermalSettings current = settingsManager.getSettings();
  String json = "{";
  json += "\"feverThreshold\":" + String(current.feverThreshold, 2) + ",";
  json += "\"currentAmbientTemp\":" + String(getCurrentAmbientTemp(), 2) + ",";
  json += "\"baselineMin\":" + String(current.baselineMin, 2) + ",";
  json += "\"displayBrightness\":" + String(current.displayBrightness) + ",";
  json += "\"dayVolume\":" + String(current.dayVolume) + ",";
  json += "\"nightVolume\":" + String(current.nightVolume) + ",";
  json += "\"feverVolume\":" + String(current.feverVolume) + ",";
  json += "\"buzzerEnabled\":" + String(current.buzzerEnabled ? "true" : "false") + ",";
  json += "\"dynamicCompensation\":" + String(current.dynamicCompensation ? "true" : "false") + ",";
  json += "\"compensationSensitivity\":" + String(current.compensationSensitivity, 1) + ",";
  json += "\"readInterval\":" + String(current.readInterval) + ",";
  json += "\"logInterval\":" + String(current.logInterval) + ",";
  json += "\"debugMode\":" + String(current.debugMode ? "true" : "false");
  json += "}";
  server.send(200, "application/json", json);
}

// Helper function to extract JSON values
String extractJsonValue(const String& json, const String& key) {
  String searchKey = "\"" + key + "\":";
  int keyIndex = json.indexOf(searchKey);
  if (keyIndex == -1) return "";

  int valueStart = json.indexOf(":", keyIndex) + 1;
  int valueEnd = json.indexOf(",", valueStart);
  if (valueEnd == -1) valueEnd = json.indexOf("}", valueStart);

  String value = json.substring(valueStart, valueEnd);
  value.trim();

  // Remove quotes if it's a string value
  if (value.startsWith("\"") && value.endsWith("\"")) {
    value = value.substring(1, value.length() - 1);
  }

  return value;
}

void handleSaveSettings() {
  if (server.hasArg("plain")) {
    String body = server.arg("plain");
    Serial.println("Received settings: " + body);

    // Parse and apply all settings using helper function
    String value;

    // Float values
    value = extractJsonValue(body, "feverThreshold");
    if (value.length() > 0) {
      feverThreshold = value.toFloat();
      settingsManager.setFeverThreshold(feverThreshold);
      Serial.println("Set feverThreshold: " + String(feverThreshold));
    }



    value = extractJsonValue(body, "baselineMin");
    if (value.length() > 0) {
      baselineMin = value.toFloat();
      settingsManager.setBaselineMin(baselineMin);
      Serial.println("Set baselineMin: " + String(baselineMin));
    }

    value = extractJsonValue(body, "medicalThreshold");
    if (value.length() > 0) {
      float medicalThreshold = value.toFloat();
      settingsManager.setMedicalThreshold(medicalThreshold);
      Serial.println("Set medicalThreshold: " + String(medicalThreshold));
    }

    // Integer values
    value = extractJsonValue(body, "sustainedTime");
    if (value.length() > 0) {
      int sustainedTime = value.toInt();
      settingsManager.setSustainedTime(sustainedTime);
      Serial.println("Set sustainedTime: " + String(sustainedTime));
    }

    value = extractJsonValue(body, "displayBrightness");
    if (value.length() > 0) {
      int brightness = value.toInt();
      // Validate brightness range
      if (brightness < 0) brightness = 0;
      if (brightness > 255) brightness = 255;
      settingsManager.setDisplayBrightness(brightness);
      setDisplayBrightness((uint8_t)brightness);
      Serial.println("Set displayBrightness: " + String(brightness));
    }

    // Volume settings
    value = extractJsonValue(body, "dayVolume");
    if (value.length() > 0) {
      int dayVolume = value.toInt();
      settingsManager.setDayVolume(dayVolume);
      Serial.println("Set dayVolume: " + String(dayVolume));
    }

    value = extractJsonValue(body, "nightVolume");
    if (value.length() > 0) {
      int nightVolume = value.toInt();
      settingsManager.setNightVolume(nightVolume);
      Serial.println("Set nightVolume: " + String(nightVolume));
    }

    value = extractJsonValue(body, "feverVolume");
    if (value.length() > 0) {
      int feverVolume = value.toInt();
      settingsManager.setFeverVolume(feverVolume);
      Serial.println("Set feverVolume: " + String(feverVolume));
    }

    // Boolean values
    value = extractJsonValue(body, "debugMode");
    if (value.length() > 0) {
      bool debugMode = (value == "true");
      settingsManager.setDebugMode(debugMode);
      Serial.println("Set debugMode: " + String(debugMode ? "true" : "false"));
    }

    value = extractJsonValue(body, "buzzerEnabled");
    if (value.length() > 0) {
      bool buzzerEnabled = (value == "true");
      settingsManager.setBuzzerEnabled(buzzerEnabled);
      enableBuzzer(buzzerEnabled);
      Serial.println("Set buzzerEnabled: " + String(buzzerEnabled ? "true" : "false"));
    }

    value = extractJsonValue(body, "dynamicCompensation");
    if (value.length() > 0) {
      bool dynamicCompensation = (value == "true");
      settingsManager.setDynamicCompensation(dynamicCompensation);
      Serial.println("Set dynamicCompensation: " + String(dynamicCompensation ? "true" : "false"));
    }

    value = extractJsonValue(body, "compensationSensitivity");
    if (value.length() > 0) {
      float sensitivity = value.toFloat();
      settingsManager.setCompensationSensitivity(sensitivity);
      Serial.println("Set compensationSensitivity: " + String(sensitivity));
    }

    value = extractJsonValue(body, "readInterval");
    if (value.length() > 0) {
      int interval = value.toInt();
      settingsManager.setReadInterval(interval);
      Serial.println("Set readInterval: " + String(interval));
    }

    value = extractJsonValue(body, "logInterval");
    if (value.length() > 0) {
      int interval = value.toInt();
      settingsManager.setLogInterval(interval);
      Serial.println("Set logInterval: " + String(interval));
    }

    value = extractJsonValue(body, "debugMode");
    if (value.length() > 0) {
      bool debugMode = (value == "true");
      settingsManager.setDebugMode(debugMode);
      Serial.println("Set debugMode: " + String(debugMode ? "true" : "false"));
    }

    // Save settings to persistent storage
    settingsManager.collectSettingsFromGlobals();
    settingsManager.saveSettings();

    Serial.println("Settings applied to device and saved to flash");
    server.send(200, "text/plain", "Settings saved and applied successfully");
  } else {
    server.send(400, "text/plain", "No settings data received");
  }
}

void handleCalibrate() {
  Serial.println("Performing sensor calibration...");

  // Perform calibration routine
  delay(100);
  amg.readPixels(pixels);
  calculateTempRange();
  ambientTemp = minTemp * 9/5 + 32; // Update ambient temperature

  Serial.print("Calibration complete. New ambient temp: ");
  Serial.print(ambientTemp);
  Serial.println("°F");

  // Save calibration results to persistent storage
  settingsManager.setAmbientTemp(ambientTemp);
  settingsManager.collectSettingsFromGlobals();
  settingsManager.saveSettings();

  String response = "Calibration complete. Ambient temperature updated to " + String(ambientTemp, 1) + "°F and saved";
  server.send(200, "text/plain", response);
}

void handleAutoCalibrate() {
  Serial.println("Performing auto calibration...");

  if (server.method() != HTTP_POST) {
    server.send(405, "text/plain", "Method not allowed");
    return;
  }

  String body = server.arg("plain");
  Serial.println("Auto calibration request body: " + body);

  // Extract child body temperature from JSON
  String childBodyTempStr = extractJsonValue(body, "childBodyTemp");
  if (childBodyTempStr.length() == 0) {
    server.send(400, "application/json", "{\"error\":\"Missing childBodyTemp parameter\"}");
    return;
  }

  float childBodyTemp = childBodyTempStr.toFloat();
  if (childBodyTemp < 95.0 || childBodyTemp > 105.0) {
    server.send(400, "application/json", "{\"error\":\"Child body temperature must be between 95.0°F and 105.0°F\"}");
    return;
  }

  // Get current thermal sensor reading (raw, uncompensated)
  delay(100);
  amg.readPixels(pixels);
  calculateTempRange();
  float rawThermalReading = maxTemp * 9/5 + 32; // Convert to Fahrenheit

  // Calculate distance compensation needed
  // This is the difference between actual body temp and what sensor reads
  float calculatedCompensation = childBodyTemp - rawThermalReading;

  // Clamp compensation to reasonable range (-10°F to +15°F)
  calculatedCompensation = constrain(calculatedCompensation, -10.0, 15.0);

  // Get current ambient temperature for reference
  float currentAmbient = getCurrentAmbientTemp();

  // Update global variables for physics-based dynamic compensation system
  distanceCompensation = calculatedCompensation;  // Base compensation for dynamic system
  ambientTemp = currentAmbient;                   // Reference ambient for dynamic system

  // Save to persistent storage
  settingsManager.setDistanceCompensation(calculatedCompensation);
  settingsManager.setAmbientTemp(currentAmbient);
  settingsManager.collectSettingsFromGlobals();
  settingsManager.saveSettings();

  Serial.printf("Auto calibration completed:\n");
  Serial.printf("- Child body temp (actual): %.1f°F\n", childBodyTemp);
  Serial.printf("- Raw thermal reading: %.1f°F\n", rawThermalReading);
  Serial.printf("- Calculated compensation: %.1f°F\n", calculatedCompensation);
  Serial.printf("- Reference ambient temp: %.1f°F\n", currentAmbient);
  Serial.printf("- Settings saved to persistent storage\n");

  // Return calculated values as JSON
  String json = "{";
  json += "\"success\":true,";
  json += "\"childBodyTemp\":" + String(childBodyTemp, 1) + ",";
  json += "\"rawThermalReading\":" + String(rawThermalReading, 1) + ",";
  json += "\"distanceCompensation\":" + String(calculatedCompensation, 1) + ",";
  json += "\"currentAmbient\":" + String(currentAmbient, 1) + ",";
  json += "\"message\":\"Distance compensation calculated and saved successfully\"";
  json += "}";

  server.send(200, "application/json", json);
}

void handleResetSettings() {
  Serial.println("Resetting all settings to factory defaults...");

  // Reset settings to defaults
  settingsManager.resetToDefaults();
  settingsManager.applySettingsToGlobals();

  // Also reset baseline learning
  baselineEstablished = false;
  baselineTemp = 0.0;
  feverDetected = false;
  feverStartTime = 0;

  // Clear temperature history
  for (int i = 0; i < 100; i++) {
    tempHistory[i] = 0.0;
  }
  historyIndex = 0;

  // Reset the static variables in updateBaseline function
  resetBaselineVariables();

  Serial.println("All settings reset to factory defaults");
  server.send(200, "text/plain", "Settings reset to factory defaults successfully");
}

void handleSettingsDebug() {
  String debug = "=== Settings Debug Info ===\n";

  // Check if settings manager is working
  debug += "Settings Manager Status: ";
  debug += settingsManager.hasStoredSettings() ? "Has stored settings" : "No stored settings";
  debug += "\n";

  // Get current settings
  ThermalSettings current = settingsManager.getSettings();
  debug += "Current Settings:\n";
  debug += "- Fever Threshold: " + String(current.feverThreshold, 2) + "°F\n";
  debug += "- Distance Compensation: " + String(current.distanceCompensation, 2) + "°F\n";
  debug += "- Ambient Temperature (stored): " + String(current.ambientTemp, 2) + "°F\n";
  debug += "- Ambient Temperature (current): " + String(getCurrentAmbientTemp(), 2) + "°F\n";
  debug += "- Baseline Temperature: " + String(current.baselineTemp, 2) + "°F\n";
  debug += "- Baseline Established: " + String(current.baselineEstablished ? "YES" : "NO") + "\n";
  debug += "- Baseline Minimum: " + String(current.baselineMin, 2) + "°F\n";
  debug += "- Day Volume: " + String(current.dayVolume) + "%\n";
  debug += "- Night Volume: " + String(current.nightVolume) + "%\n";
  debug += "- Fever Volume: " + String(current.feverVolume) + "%\n";
  debug += "- Medical Threshold: " + String(current.medicalThreshold, 2) + "°F\n";
  debug += "- Sustained Time: " + String(current.sustainedTime) + " minutes\n";
  debug += "- Debug Mode: " + String(current.debugMode ? "ENABLED" : "DISABLED") + "\n";
  debug += "- Version: " + String(current.version) + "\n";

  // Compare with global variables
  debug += "\nGlobal Variables:\n";
  debug += "- feverThreshold: " + String(feverThreshold, 2) + "°F\n";
  debug += "- distanceCompensation: " + String(distanceCompensation, 2) + "°F\n";
  debug += "- ambientTemp (stored): " + String(ambientTemp, 2) + "°F\n";
  debug += "- ambientTemp (current): " + String(getCurrentAmbientTemp(), 2) + "°F\n";
  debug += "- baselineTemp: " + String(baselineTemp, 2) + "°F\n";
  debug += "- baselineEstablished: " + String(baselineEstablished ? "YES" : "NO") + "\n";
  debug += "- baselineMin: " + String(baselineMin, 2) + "°F\n";

  debug += "\n=== End Debug Info ===";

  server.send(200, "text/plain", debug);
}

void handleTestSave() {
  // Manually trigger settings save for testing
  settingsManager.collectSettingsFromGlobals();
  bool success = settingsManager.saveSettings();

  String response = "Manual save test: ";
  response += success ? "SUCCESS" : "FAILED";
  response += "\nCurrent settings collected and save attempted.";

  server.send(200, "text/plain", response);
}

void handleTestLoad() {
  // Manually trigger settings load for testing
  bool success = settingsManager.loadSettings();
  if (success) {
    settingsManager.applySettingsToGlobals();
  }

  String response = "Manual load test: ";
  response += success ? "SUCCESS" : "NO_STORED_SETTINGS";
  response += "\nSettings load attempted and applied to globals.";

  server.send(200, "text/plain", response);
}

void handleTestBuzzer() {
  Serial.println("Testing buzzer volume control via web interface");

  if (!isBuzzerEnabled()) {
    server.send(400, "text/plain", "Buzzer is disabled - cannot test volume");
    return;
  }

  // Run the buzzer volume test
  testBuzzerVolume();

  String response = "Buzzer volume test completed successfully!\n";
  response += "Played tones at:\n";
  response += "- 100% volume (full)\n";
  response += "- 25% volume (night mode)\n";
  response += "- 50% volume (medium)\n";
  response += "Check serial monitor for detailed logs.";

  server.send(200, "text/plain", response);
}

void handleGetTimeStatus() {
  // Get values and debug them
  const char* ntpServer = settingsManager.getNtpServer();
  const char* timezone = settingsManager.getTimezone();

  Serial.printf("DEBUG - NTP Server: '%s' (len=%d)\n", ntpServer, strlen(ntpServer));
  Serial.printf("DEBUG - Timezone: '%s' (len=%d)\n", timezone, strlen(timezone));

  // Ensure we have valid strings and log the fallback behavior
  String ntpServerStr = (ntpServer && strlen(ntpServer) > 0) ? String(ntpServer) : "pool.ntp.org";
  String timezoneStr = (timezone && strlen(timezone) > 0) ? String(timezone) : "EST5EDT";

  if (!ntpServer || strlen(ntpServer) == 0) {
    Serial.println("WARNING: NTP Server is empty, using default: pool.ntp.org");
  }
  if (!timezone || strlen(timezone) == 0) {
    Serial.println("WARNING: Timezone is empty, using default: EST5EDT");
  }

  String json = "{";
  json += "\"timeSet\":" + String(timeManager.isTimeSet() ? "true" : "false") + ",";
  json += "\"status\":\"" + timeManager.getStatusString() + "\",";
  json += "\"currentTime\":\"" + String(timeManager.getCurrentTimeString()) + "\",";
  json += "\"currentDate\":\"" + String(timeManager.getCurrentDateString()) + "\",";
  json += "\"timeSinceSync\":" + String(timeManager.getTimeSinceLastSync()) + ",";
  json += "\"nightMode\":" + String(isNightModeTime() ? "true" : "false") + ",";
  json += "\"ntpServer\":\"" + ntpServerStr + "\",";
  json += "\"timezone\":\"" + timezoneStr + "\",";
  json += "\"autoSync\":" + String(settingsManager.isAutoTimeSyncEnabled() ? "true" : "false") + ",";
  json += "\"nightModeEnabled\":" + String(settingsManager.isNightModeEnabled() ? "true" : "false") + ",";
  json += "\"nightStart\":\"" + String(settingsManager.getNightStartHour()) + ":" + String(settingsManager.getNightStartMinute()) + "\",";
  json += "\"nightEnd\":\"" + String(settingsManager.getNightEndHour()) + ":" + String(settingsManager.getNightEndMinute()) + "\",";
  json += "\"displayOffAtNight\":" + String(settingsManager.isDisplayOffAtNight() ? "true" : "false");
  json += "}";

  Serial.println("Time status JSON: " + json);
  server.send(200, "application/json", json);
}

void handleSyncTime() {
  Serial.println("Manual time sync requested via web interface");
  bool success = timeManager.syncTime();

  String response = success ? "Time synchronized successfully" : "Time synchronization failed";
  server.send(200, "text/plain", response);
}

void handleSetTime() {
  if (server.hasArg("plain")) {
    String body = server.arg("plain");
    Serial.println("Received time setting request: " + body);

    // Parse JSON manually (simplified)
    int year = 0, month = 0, day = 0, hour = 0, minute = 0, second = 0;

    // Extract year
    int yearIndex = body.indexOf("\"year\":");
    if (yearIndex != -1) {
      int valueStart = body.indexOf(":", yearIndex) + 1;
      int valueEnd = body.indexOf(",", valueStart);
      if (valueEnd == -1) valueEnd = body.indexOf("}", valueStart);
      year = body.substring(valueStart, valueEnd).toInt();
    }

    // Extract month
    int monthIndex = body.indexOf("\"month\":");
    if (monthIndex != -1) {
      int valueStart = body.indexOf(":", monthIndex) + 1;
      int valueEnd = body.indexOf(",", valueStart);
      if (valueEnd == -1) valueEnd = body.indexOf("}", valueStart);
      month = body.substring(valueStart, valueEnd).toInt();
    }

    // Extract day
    int dayIndex = body.indexOf("\"day\":");
    if (dayIndex != -1) {
      int valueStart = body.indexOf(":", dayIndex) + 1;
      int valueEnd = body.indexOf(",", valueStart);
      if (valueEnd == -1) valueEnd = body.indexOf("}", valueStart);
      day = body.substring(valueStart, valueEnd).toInt();
    }

    // Extract hour
    int hourIndex = body.indexOf("\"hour\":");
    if (hourIndex != -1) {
      int valueStart = body.indexOf(":", hourIndex) + 1;
      int valueEnd = body.indexOf(",", valueStart);
      if (valueEnd == -1) valueEnd = body.indexOf("}", valueStart);
      hour = body.substring(valueStart, valueEnd).toInt();
    }

    // Extract minute
    int minuteIndex = body.indexOf("\"minute\":");
    if (minuteIndex != -1) {
      int valueStart = body.indexOf(":", minuteIndex) + 1;
      int valueEnd = body.indexOf(",", valueStart);
      if (valueEnd == -1) valueEnd = body.indexOf("}", valueStart);
      minute = body.substring(valueStart, valueEnd).toInt();
    }

    // Extract second
    int secondIndex = body.indexOf("\"second\":");
    if (secondIndex != -1) {
      int valueStart = body.indexOf(":", secondIndex) + 1;
      int valueEnd = body.indexOf(",", valueStart);
      if (valueEnd == -1) valueEnd = body.indexOf("}", valueStart);
      second = body.substring(valueStart, valueEnd).toInt();
    }

    if (year > 0 && month > 0 && day > 0) {
      bool success = timeManager.setTime(year, month, day, hour, minute, second);
      String response = success ? "Time set successfully" : "Failed to set time";
      server.send(200, "text/plain", response);
    } else {
      server.send(400, "text/plain", "Invalid time data");
    }
  } else {
    server.send(400, "text/plain", "No time data received");
  }
}

void handleSaveTimeSettings() {
  if (server.hasArg("plain")) {
    String body = server.arg("plain");
    Serial.println("Received time settings: " + body);

    // Parse JSON using ArduinoJson library
    DynamicJsonDocument doc(1024);
    DeserializationError error = deserializeJson(doc, body);

    if (error) {
      Serial.print("JSON parsing failed: ");
      Serial.println(error.c_str());
      server.send(400, "text/plain", "Invalid JSON data");
      return;
    }

    // Extract and validate NTP server
    if (doc.containsKey("ntpServer")) {
      String ntpServer = doc["ntpServer"].as<String>();
      if (ntpServer.length() > 0) {
        Serial.printf("Setting NTP server to: '%s'\n", ntpServer.c_str());
        settingsManager.setNtpServer(ntpServer.c_str());
      } else {
        Serial.println("Warning: Empty NTP server value received");
      }
    }

    // Extract and validate timezone
    if (doc.containsKey("timezone")) {
      String timezone = doc["timezone"].as<String>();
      if (timezone.length() > 0) {
        Serial.printf("Setting timezone to: '%s'\n", timezone.c_str());
        settingsManager.setTimezone(timezone.c_str());

      // Set appropriate GMT offset based on timezone
      if (timezone == "IST-5:30") {
        // IST is UTC+5:30 = 19800 seconds, no daylight saving
        settingsManager.setGmtOffsetSec(19800);
        settingsManager.setDaylightOffsetSec(0);
        Serial.println("Set IST timezone: UTC+5:30 (19800 seconds)");
      } else if (timezone == "EST5EDT") {
        // EST is UTC-5 = -18000 seconds, EDT is +1 hour
        settingsManager.setGmtOffsetSec(-18000);
        settingsManager.setDaylightOffsetSec(3600);
      } else if (timezone == "CST6CDT") {
        // CST is UTC-6 = -21600 seconds, CDT is +1 hour
        settingsManager.setGmtOffsetSec(-21600);
        settingsManager.setDaylightOffsetSec(3600);
      } else if (timezone == "MST7MDT") {
        // MST is UTC-7 = -25200 seconds, MDT is +1 hour
        settingsManager.setGmtOffsetSec(-25200);
        settingsManager.setDaylightOffsetSec(3600);
      } else if (timezone == "PST8PDT") {
        // PST is UTC-8 = -28800 seconds, PDT is +1 hour
        settingsManager.setGmtOffsetSec(-28800);
        settingsManager.setDaylightOffsetSec(3600);
      } else if (timezone == "AKST9AKDT") {
        // AKST is UTC-9 = -32400 seconds, AKDT is +1 hour
        settingsManager.setGmtOffsetSec(-32400);
        settingsManager.setDaylightOffsetSec(3600);
      } else if (timezone == "HST10") {
        // HST is UTC-10 = -36000 seconds, no daylight saving
        settingsManager.setGmtOffsetSec(-36000);
        settingsManager.setDaylightOffsetSec(0);
      }

        // Apply timezone to time manager
        timeManager.setTimezone(timezone.c_str());
      } else {
        Serial.println("Warning: Empty timezone value received");
      }
    }

    // Extract auto sync setting
    if (doc.containsKey("autoSync")) {
      bool autoSync = doc["autoSync"].as<bool>();
      Serial.printf("Setting auto sync to: %s\n", autoSync ? "true" : "false");
      settingsManager.setAutoTimeSync(autoSync);
    }

    // Extract night mode enabled
    if (doc.containsKey("nightModeEnabled")) {
      bool nightMode = doc["nightModeEnabled"].as<bool>();
      Serial.printf("Setting night mode to: %s\n", nightMode ? "true" : "false");
      settingsManager.setNightModeEnabled(nightMode);
    }

    // Extract night start time
    if (doc.containsKey("nightStartHour") && doc.containsKey("nightStartMinute")) {
      int hour = doc["nightStartHour"].as<int>();
      int minute = doc["nightStartMinute"].as<int>();
      Serial.printf("Setting night start time to: %02d:%02d\n", hour, minute);
      settingsManager.setNightStartTime(hour, minute);
    }

    // Extract night end time
    if (doc.containsKey("nightEndHour") && doc.containsKey("nightEndMinute")) {
      int hour = doc["nightEndHour"].as<int>();
      int minute = doc["nightEndMinute"].as<int>();
      Serial.printf("Setting night end time to: %02d:%02d\n", hour, minute);
      settingsManager.setNightEndTime(hour, minute);
    }

    // Extract display off at night
    if (doc.containsKey("displayOffAtNight")) {
      bool displayOff = doc["displayOffAtNight"].as<bool>();
      Serial.printf("Setting display off at night to: %s\n", displayOff ? "true" : "false");
      settingsManager.setDisplayOffAtNight(displayOff);
    }

    // Save settings to persistent storage
    settingsManager.saveSettings();

    // Apply new settings to time manager without full restart
    const char* newNtpServer = settingsManager.getNtpServer();
    const char* newTimezone = settingsManager.getTimezone();

    Serial.printf("Applying new time settings - NTP: %s, Timezone: %s\n", newNtpServer, newTimezone);

    // Set timezone first
    timeManager.setTimezone(newTimezone);

    // If auto sync is enabled, sync with new NTP server
    if (settingsManager.isAutoTimeSyncEnabled()) {
      timeManager.syncTime(newNtpServer, settingsManager.getGmtOffsetSec(), settingsManager.getDaylightOffsetSec());
    }

    Serial.println("Time settings applied and saved");
    server.send(200, "text/plain", "Time settings saved successfully");
  } else {
    server.send(400, "text/plain", "No time settings data received");
  }
}

// Utility functions
String getContentType(String filename) {
  if (filename.endsWith(".html")) return "text/html";
  else if (filename.endsWith(".css")) return "text/css";
  else if (filename.endsWith(".js")) return "application/javascript";
  else if (filename.endsWith(".png")) return "image/png";
  else if (filename.endsWith(".gif")) return "image/gif";
  else if (filename.endsWith(".jpg")) return "image/jpeg";
  else if (filename.endsWith(".ico")) return "image/x-icon";
  else if (filename.endsWith(".xml")) return "text/xml";
  else if (filename.endsWith(".pdf")) return "application/pdf";
  else if (filename.endsWith(".zip")) return "application/zip";
  return "text/plain";
}

bool handleFileRead(String path) {
  Serial.println("handleFileRead: " + path);
  
  if (path.endsWith("/")) {
    path += "index.html";
  }
  
  String contentType = getContentType(path);
  String pathWithGz = path + ".gz";
  
  if (SPIFFS.exists(pathWithGz) || SPIFFS.exists(path)) {
    if (SPIFFS.exists(pathWithGz)) {
      path += ".gz";
    }
    
    File file = SPIFFS.open(path, "r");
    if (file) {
      server.streamFile(file, contentType);
      file.close();
      return true;
    }
  }
  
  Serial.println("File not found: " + path);
  return false;
}

// Global variables for SSE management
static bool lastFeverState = false;

// Server-Sent Events handler for real-time notifications
void handleSSEEvents() {
  // Set SSE headers properly
  server.sendHeader("Content-Type", "text/event-stream; charset=utf-8");
  server.sendHeader("Cache-Control", "no-cache, no-store, must-revalidate");
  server.sendHeader("Pragma", "no-cache");
  server.sendHeader("Expires", "0");
  server.sendHeader("Connection", "keep-alive");
  server.sendHeader("Access-Control-Allow-Origin", "*");
  server.sendHeader("Access-Control-Allow-Headers", "Cache-Control");
  server.sendHeader("Access-Control-Allow-Methods", "GET");

  // Send a simple initial response
  String initialData = "data: {\"type\":\"connected\",\"timestamp\":" + String(millis()) + "}\n\n";
  server.send(200, "text/event-stream", initialData);

  Serial.println("SSE: Initial response sent");
}

void handleSetDisplayBrightness() {
  if (server.hasArg("brightness")) {
    int brightness = server.arg("brightness").toInt();

    // Validate brightness range (0-255)
    if (brightness < 0) brightness = 0;
    if (brightness > 255) brightness = 255;

    Serial.print("Setting display brightness to: ");
    Serial.println(brightness);

    // Update the settings
    settingsManager.setDisplayBrightness(brightness);

    // Apply the brightness immediately
    setDisplayBrightness((uint8_t)brightness);

    // Save settings
    settingsManager.saveSettings();

    // Send response
    String json = "{\"success\":true,\"brightness\":" + String(brightness) + "}";
    server.send(200, "application/json", json);
  } else {
    server.send(400, "application/json", "{\"success\":false,\"error\":\"Missing brightness parameter\"}");
  }
}

void handleResetTimeSettings() {
  Serial.println("Resetting time settings to defaults...");

  // Reset time settings to defaults
  settingsManager.setNtpServer("pool.ntp.org");
  settingsManager.setTimezone("EST5EDT");
  settingsManager.setGmtOffsetSec(-18000);  // EST: UTC-5
  settingsManager.setDaylightOffsetSec(3600);  // EDT: +1 hour
  settingsManager.setAutoTimeSync(true);
  settingsManager.setNightModeEnabled(true);
  settingsManager.setNightStartTime(23, 0);  // 11:00 PM
  settingsManager.setNightEndTime(8, 0);     // 8:00 AM
  settingsManager.setDisplayOffAtNight(true);

  // Reset volume settings to defaults
  settingsManager.setDayVolume(100);
  settingsManager.setNightVolume(25);
  settingsManager.setFeverVolume(75);

  settingsManager.saveSettings();

  Serial.println("Time settings reset complete");
  server.send(200, "text/plain", "Time settings reset to defaults");
}
