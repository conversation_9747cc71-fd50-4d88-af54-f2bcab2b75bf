#include "buzzer.h"
#include "time_manager.h"  // For night mode checking
#include "settings_manager.h"  // For volume settings

// Global variables
BuzzerState currentBuzzerState = BUZZER_OFF;
unsigned long lastBuzzerAction = 0;
bool buzzerEnabled = true;
bool isPlaying = false;
unsigned long playingUntil = 0;
int currentVolume = 255;  // Default to full volume (100%)

// Startup melody - "Tananantan" rhythm pattern
const int startupMelody[] = {
  NOTE_C4, NOTE_C4, NOTE_C4, NOTE_REST, NOTE_C4, NOTE_C4, NOTE_REST, NOTE_C4
};

const int startupDurations[] = {
  150, 150, 200, 100, 150, 150, 100, 300  // Ta-na-nan-tan rhythm
};

void initBuzzer() {
  Serial.println("Initializing buzzer...");

  // Setup PWM for buzzer volume control
  ledcSetup(BUZZER_PWM_CHANNEL, BUZZER_PWM_FREQ, BUZZER_PWM_RES);
  ledcAttachPin(BUZZER_PIN, BUZZER_PWM_CHANNEL);
  ledcWrite(BUZZER_PWM_CHANNEL, 0); // Start with buzzer off

  // Initialize state
  currentBuzzerState = BUZZER_OFF;
  lastBuzzerAction = 0;
  isPlaying = false;
  playingUntil = 0;
  currentVolume = 255;  // Default to full volume (100%)

  Serial.print("Buzzer initialized on GPIO ");
  Serial.print(BUZZER_PIN);
  Serial.println(" with PWM volume control");
}

void setBuzzerState(BuzzerState state) {
  if (!buzzerEnabled && state != BUZZER_OFF) {
    Serial.println("Buzzer is disabled, ignoring state change");
    return;
  }

  // Night mode volume reduction is now handled in individual play functions
  // No longer silencing non-fever sounds completely during night mode

  if (currentBuzzerState != state) {
    Serial.print("Buzzer state changed from ");
    Serial.print(currentBuzzerState);
    Serial.print(" to ");
    Serial.println(state);

    currentBuzzerState = state;
    lastBuzzerAction = millis();

    // Handle immediate actions for new states
    switch (state) {
      case BUZZER_STARTUP:
        playStartupMelody();
        break;
      case BUZZER_LEARNING:
        playLearningBeeps();
        break;
      case BUZZER_FEVER_ALERT:
        // Fever alert will be handled in handleBuzzer() for continuous beeping
        break;
      case BUZZER_OFF:
        stopBuzzer();
        break;
    }
  }
}

void handleBuzzer() {
  unsigned long currentTime = millis();

  // Update playing state
  if (isPlaying && currentTime >= playingUntil) {
    isPlaying = false;
    buzzerOff();
  }

  // Handle continuous fever alert
  // NOTE: Fever alerts are NOT silenced during night mode - they are critical health alerts
  // Volume is set based on fever volume setting
  if (currentBuzzerState == BUZZER_FEVER_ALERT && buzzerEnabled) {
    // Set fever alert volume (uses fever volume setting)
    int feverVolume = getAppropriateVolume(true);
    setBuzzerVolume(feverVolume);
    Serial.printf("Fever alert volume set to %d%% (%d/255)\n",
                  settingsManager.getFeverVolume(), feverVolume);

    // Beep every 2 seconds when fever is detected
    if (currentTime - lastBuzzerAction >= 2000) {
      playTone(NOTE_A5, 200); // High pitched alert beep
      lastBuzzerAction = currentTime;
    }
  }
}

void playStartupMelody() {
  if (!buzzerEnabled) return;

  // Set appropriate volume based on day/night settings
  int volume = getAppropriateVolume(false);
  setBuzzerVolume(volume);

  if (isNightModeTime()) {
    Serial.printf("Night mode active - playing startup melody at %d%% volume (%d/255)\n",
                  settingsManager.getNightVolume(), volume);
  } else {
    Serial.printf("Playing startup melody at %d%% volume (%d/255) - Tananantan\n",
                  settingsManager.getDayVolume(), volume);
  }

  playMelody(startupMelody, startupDurations, 8);
}

void playLearningBeeps() {
  if (!buzzerEnabled) return;

  // Set appropriate volume based on day/night settings
  int volume = getAppropriateVolume(false);
  setBuzzerVolume(volume);

  if (isNightModeTime()) {
    Serial.printf("Night mode active - playing learning beeps at %d%% volume (%d/255)\n",
                  settingsManager.getNightVolume(), volume);
  } else {
    Serial.printf("Playing learning beeps at %d%% volume (%d/255)\n",
                  settingsManager.getDayVolume(), volume);
  }

  // Play two short beeps
  playTone(NOTE_C5, 150);
  delay(100);
  playTone(NOTE_C5, 150);
}

void playFeverAlert() {
  if (!buzzerEnabled) return;
  
  // This is called from handleBuzzer() for continuous beeping
  playTone(NOTE_A5, 200);
}

void stopBuzzer() {
  buzzerOff();
  isPlaying = false;
  playingUntil = 0;
  Serial.println("Buzzer stopped");
}

void playTone(int frequency, int duration) {
  if (!buzzerEnabled || frequency == NOTE_REST) {
    if (frequency == NOTE_REST) {
      delay(duration);
    }
    return;
  }

  // Set appropriate volume based on current state
  if (currentBuzzerState != BUZZER_FEVER_ALERT) {
    // Non-fever sounds use day/night volume settings
    int volume = getAppropriateVolume(false);
    setBuzzerVolume(volume);
  }
  // Note: Fever alerts volume is already set in handleBuzzer()
  // For fever alerts, volume is already set in handleBuzzer()

  // For 2-pin active buzzer, use PWM for volume control
  buzzerOn();                      // Turn buzzer ON with current volume
  delay(duration);                 // Keep it on for the duration
  buzzerOff();                     // Turn buzzer OFF

  // Mark as playing for the duration
  isPlaying = true;
  playingUntil = millis() + duration;
}

void playMelody(const int melody[], const int durations[], int length) {
  if (!buzzerEnabled) return;

  // Set appropriate volume based on current state
  if (currentBuzzerState != BUZZER_FEVER_ALERT) {
    // Non-fever sounds use day/night volume settings
    int volume = getAppropriateVolume(false);
    setBuzzerVolume(volume);
  }
  // Note: Fever alerts volume is already set in handleBuzzer()

  for (int i = 0; i < length; i++) {
    if (melody[i] == NOTE_REST) {
      // Rest note - just delay
      delay(durations[i]);
    } else {
      // Play note - for 2-pin active buzzer, use PWM for volume control
      buzzerOn();
      delay(durations[i]);
      buzzerOff();
    }

    // Small pause between notes
    delay(50);
  }
}

bool isBuzzerPlaying() {
  return isPlaying && millis() < playingUntil;
}

void enableBuzzer(bool enable) {
  buzzerEnabled = enable;
  Serial.print("Buzzer ");
  Serial.println(enable ? "enabled" : "disabled");
  
  if (!enable) {
    stopBuzzer();
    currentBuzzerState = BUZZER_OFF;
  }
}

bool isBuzzerEnabled() {
  return buzzerEnabled;
}

// PWM volume control functions
void setBuzzerVolume(int volume) {
  // Clamp volume to valid range (0-255)
  if (volume < 0) volume = 0;
  if (volume > 255) volume = 255;

  currentVolume = volume;
  Serial.print("Buzzer volume set to ");
  Serial.print(volume);
  Serial.print("/255 (");
  Serial.print((volume * 100) / 255);
  Serial.println("%)");
}

int getBuzzerVolume() {
  return currentVolume;
}

void buzzerOn() {
  ledcWrite(BUZZER_PWM_CHANNEL, currentVolume);
}

void buzzerOff() {
  ledcWrite(BUZZER_PWM_CHANNEL, 0);
}

// Volume helper functions
int getAppropriateVolume(bool isFeverAlert) {
  if (isFeverAlert) {
    // Fever alerts use fever volume setting regardless of time
    return convertPercentToVolume(settingsManager.getFeverVolume());
  } else {
    // Non-fever sounds use day/night volume based on current time
    if (isNightModeTime()) {
      return convertPercentToVolume(settingsManager.getNightVolume());
    } else {
      return convertPercentToVolume(settingsManager.getDayVolume());
    }
  }
}

int convertPercentToVolume(int percent) {
  // Convert percentage (0-100) to PWM value (0-255)
  // Clamp to valid range
  if (percent < 0) percent = 0;
  if (percent > 100) percent = 100;
  return (percent * 255) / 100;
}

// Test function to demonstrate volume control
void testBuzzerVolume() {
  if (!buzzerEnabled) {
    Serial.println("Buzzer is disabled - cannot test volume");
    return;
  }

  Serial.println("Testing buzzer volume control...");

  // Test full volume (100%)
  Serial.println("Testing full volume (100%)");
  setBuzzerVolume(255);  // 100%
  playTone(NOTE_C5, 500);
  delay(500);

  // Test night mode volume (25%)
  Serial.println("Testing night mode volume (25%)");
  setBuzzerVolume(64);   // 25%
  playTone(NOTE_C5, 500);
  delay(500);

  // Test 50% volume
  Serial.println("Testing 50% volume");
  setBuzzerVolume(128); // 50% of 255
  playTone(NOTE_C5, 500);
  delay(500);

  // Reset to full volume
  setBuzzerVolume(255);  // 100%
  Serial.println("Volume test complete - reset to full volume");
}
