#include "thermal_sensor.h"
#include "settings_manager.h"
#include "aht10_sensor.h"
#include "buzzer.h"
#include <Wire.h>

Adafruit_AMG88xx amg;
float pixels[AMG88xx_PIXEL_ARRAY_SIZE];
float compensatedPixels[AMG88xx_PIXEL_ARRAY_SIZE]; // Compensated pixel data for web display
float maxTemp = 0.0;
float minTemp = 0.0;
unsigned long lastPixelReadTime = 0;

// Fever detection variables
float baselineTemp = 0.0;
float currentBodyTemp = 0.0;
bool feverDetected = false;
unsigned long feverStartTime = 0;
float feverThreshold = 2.0; // 2°F above baseline
bool baselineEstablished = false;
float baselineMin = 90.0; // Minimum temp to start baseline learning
float tempHistory[100];
int historyIndex = 0;
unsigned long lastTempLog = 0;

// Baseline unlearning variables
unsigned long belowBaselineStartTime = 0;
bool wasBelowBaseline = false;

// Learning phase robustness variables
int consecutiveBelowMinCount = 0;
int maxConsecutiveBelowMin = 4; // Allow 4 consecutive readings below min before resetting learning

// Sustained fever monitoring variables
unsigned long feverConditionStartTime = 0;
bool wasFeverCondition = false;

// Distance compensation for 4 feet (AMG8833 loses ~3-5°F at 4 feet)
float distanceCompensation = 4.5; // Degrees F to add for 4-foot distance
float ambientTemp = 70.0; // Room temperature baseline
float personThreshold = 4.0; // Temperature above ambient to detect person presence

// I2C error tracking
unsigned long invalidReadingCount = 0;
unsigned long totalReadingCount = 0;

void initSensor() {
  Wire.begin(9, 4);  // << 🟢 Correct placement here!
  Wire.setClock(100000); // Set I2C clock to 100kHz for stability with multiple devices

  Serial.println("Initializing AMG8833 thermal sensor...");

  // Try to initialize AMG8833 with retries for shared I2C bus stability
  int retries = 3;
  bool sensorInitialized = false;

  while (retries > 0 && !sensorInitialized) {
    delay(100); // Allow I2C bus to stabilize

    if (amg.begin()) {
      sensorInitialized = true;
      Serial.println("AMG8833 sensor initialized successfully!");
    } else {
      retries--;
      Serial.print("AMG8833 initialization failed, retries left: ");
      Serial.println(retries);
      if (retries > 0) {
        delay(500); // Wait before retry
      }
    }
  }

  if (!sensorInitialized) {
    Serial.println("Could not find a valid AMG88xx sensor. Check wiring!");
    Serial.println("Expected I2C address: 0x69");
    Serial.println("Wiring: SDA=GPIO9, SCL=GPIO4");
    while (1);
  }

  // Initialize fever detection
  initFeverDetection();
}

void initFeverDetection() {
  Serial.println("Initializing fever detection system...");
  Serial.printf("Distance: 4 feet - Compensation: +%.1f°F\n", distanceCompensation);
  Serial.println("Learning baseline temperature...");

  // Initialize temperature history array
  for (int i = 0; i < 100; i++) {
    tempHistory[i] = 0.0;
  }

  // Get initial ambient temperature reading only if not already set from saved settings
  delay(1000);
  amg.readPixels(pixels);
  calculateTempRange();

  // Only update ambient temperature if it's still at default value (70.0)
  // This preserves the loaded settings value
  if (abs(ambientTemp - 70.0) < 0.1) {
    ambientTemp = minTemp * 9/5 + 32; // Convert to Fahrenheit
    Serial.print("Calculated ambient temperature: ");
  } else {
    Serial.print("Using saved ambient temperature: ");
  }
  Serial.print(ambientTemp);
  Serial.println("°F");
}

// Get current ambient temperature - automatically use AHT10 if available, otherwise use stored setting
float getCurrentAmbientTemp() {
  if (isAHT10Available()) {
    AHT10Data aht10Data = getAHT10Data();
    // Check if data is recent (within last 2 minutes)
    unsigned long dataAge = millis() - aht10Data.lastUpdate;
    if (dataAge < 120000) { // 2 minutes in milliseconds
      // Serial.print("Using AHT10 ambient temperature: ");
      // Serial.print(aht10Data.temperatureF, 1);
      // Serial.println("°F");
      return aht10Data.temperatureF;
    } else {
      Serial.println("AHT10 data is stale, falling back to stored ambient temperature");
    }
  }

  // Fallback to stored ambient temperature setting
  Serial.print("Using stored ambient temperature: ");
  Serial.print(ambientTemp, 1);
  Serial.println("°F");
  return ambientTemp;
}

void readPixels() {
  // Use configurable read interval from settings (default 100ms)
  int readInterval = settingsManager.getReadInterval();
  if (millis() - lastPixelReadTime < readInterval) {
    return;
  }

  // Read raw sensor data with validation
  amg.readPixels(pixels);
  totalReadingCount++;

  // Validate sensor readings - check for obviously invalid data
  bool validReading = true;
  for (int i = 0; i < AMG88xx_PIXEL_ARRAY_SIZE; i++) {
    // Check for reasonable temperature range (-40°C to 85°C)
    // AMG8833 operating range is -20°C to 80°C, but we add margin for safety
    if (pixels[i] < -40.0 || pixels[i] > 85.0 || isnan(pixels[i]) || isinf(pixels[i])) {
      validReading = false;
      break;
    }
  }

  // If invalid reading detected, skip this cycle and try again
  if (!validReading) {
    invalidReadingCount++;
    Serial.print("⚠️  Invalid sensor reading detected (");
    Serial.print(invalidReadingCount);
    Serial.print("/");
    Serial.print(totalReadingCount);
    Serial.println(") - skipping this cycle");

    // Print I2C health statistics every 50 invalid readings
    if (invalidReadingCount % 50 == 0) {
      float errorRate = (float)invalidReadingCount / totalReadingCount * 100.0;
      Serial.print("I2C Error Rate: ");
      Serial.print(errorRate, 1);
      Serial.println("% - Consider checking I2C bus stability");
    }
    return;
  }

  // Debug: Print raw sensor data occasionally for web debugging
  static unsigned long lastWebDebugPrint = 0;
  if (millis() - lastWebDebugPrint > 15000) { // Every 15 seconds
    Serial.println("=== Thermal Pixel Data for Web Debug ===");
    Serial.print("Raw pixels (first 8): ");
    for (int i = 0; i < 8; i++) {
      Serial.print(pixels[i], 2);
      Serial.print(" ");
    }
    Serial.println();
    lastWebDebugPrint = millis();
  }

  // orient pixels;
  orientPixels();
  calculateTempRange();

  // Debug: Print temperature range for web debugging
  if (millis() - lastWebDebugPrint < 1000) { // Same debug cycle
    Serial.print("After orientation - Temp range: ");
    Serial.print(minTemp, 2);
    Serial.print("°C to ");
    Serial.print(maxTemp, 2);
    Serial.print("°C");
    Serial.println();
  }

  // Apply distance compensation to all pixels for thermal visualization
  applyCompensationToPixels();

  // Calculate body temperature with distance compensation
  currentBodyTemp = calculateBodyTemperature();

  // Log temperature every 30 seconds
  if (millis() - lastTempLog > 30000) {
    logTemperature(currentBodyTemp);
    lastTempLog = millis();
  }

  // Update baseline if not established
  if (!baselineEstablished) {
    updateBaseline();
  } else {
    // Check if we should unlearn the baseline (person left)
    checkBaselineUnlearning();
  }

  // Check for fever
  bool currentFeverStatus = checkForFever();
  if (currentFeverStatus && !feverDetected) {
    feverStartTime = millis();
    Serial.println("🚨 FEVER DETECTED! 🚨");
    setBuzzerState(BUZZER_FEVER_ALERT);
  } else if (!currentFeverStatus && feverDetected) {
    // Fever cleared
    Serial.println("Fever cleared - returning to normal monitoring");
    setBuzzerState(BUZZER_OFF);
  }
  feverDetected = currentFeverStatus;

  lastPixelReadTime = millis();

  // Print status every 5 seconds
  static unsigned long lastStatusPrint = 0;
  if (millis() - lastStatusPrint > 5000) {
    Serial.print("Body Temp: ");
    Serial.print(currentBodyTemp, 1);
    Serial.print("°F | Baseline: ");
    Serial.print(baselineTemp, 1);
    Serial.print("°F | Status: ");
    Serial.println(getFeverStatus());
    lastStatusPrint = millis();
  }
}

void orientPixels() {
  // rotate pixels counter-clockwise 90 degrees
  for (int i = 0; i < 8; i++) {
    for (int j = 0; j < 4; j++) {
      float temp = pixels[i * 8 + j];
      pixels[i * 8 + j] = pixels[(7 - j) * 8 + i];
      pixels[(7 - j) * 8 + i] = temp;
    }
  }
  // flip vertically
  for (int i = 0; i < 4; i++) {
    for (int j = 0; j < 8; j++) {
      float temp = pixels[i * 8 + j];
      pixels[i * 8 + j] = pixels[(7 - i) * 8 + j];
      pixels[(7 - i) * 8 + j] = temp;
    }
  }
}

void calculateTempRange() {
  maxTemp = pixels[0];
  minTemp = pixels[0];

  for (int i = 1; i < AMG88xx_PIXEL_ARRAY_SIZE; i++) {
    if (pixels[i] > maxTemp) {
      maxTemp = pixels[i];
    }
    if (pixels[i] < minTemp) {
      minTemp = pixels[i];
    }
  }
}

// Centralized function to determine appropriate temperature compensation
float applySmartCompensation(float rawTempF) {
  // Get current ambient temperature (AHT10 if available, otherwise stored setting)
  float currentAmbient = getCurrentAmbientTemp();

  // Calculate dynamic compensation based on ambient temperature
  float dynamicCompensation = calculateDynamicCompensation(rawTempF, currentAmbient);

  // Apply the compensation
  return rawTempF + dynamicCompensation;
}

// Calculate dynamic compensation based on ambient temperature and thermal physics
float calculateDynamicCompensation(float rawTempF, float ambientTempF) {
  // Check if dynamic compensation is enabled
  if (!settingsManager.isDynamicCompensationEnabled()) {
    // Use static compensation
    return distanceCompensation;
  }

  // Base distance compensation (user-calibrated for reference conditions)
  float baseCompensation = distanceCompensation;

  // Reference ambient temperature (typically room temperature when calibrated)
  float referenceAmbient = ambientTemp; // This is the stored ambient temp from calibration

  // Get sensitivity setting (0-100%)
  float sensitivity = settingsManager.getCompensationSensitivity() / 100.0; // Convert to 0.0-1.0

  // Calculate temperature differences
  float rawToAmbientDiff = rawTempF - ambientTempF;
  float rawToReferenceDiff = rawTempF - referenceAmbient;

  // Physics-based compensation adjustment
  // Thermal radiation loss is proportional to temperature difference
  // Stefan-Boltzmann law approximation for small temperature differences
  float compensationAdjustment = 0.0;

  if (abs(rawToReferenceDiff) > 0.1) { // Avoid division by zero
    // Calculate the ratio of thermal losses
    float thermalLossRatio = rawToAmbientDiff / rawToReferenceDiff;

    // Adjust compensation based on thermal loss ratio
    // When ambient is cooler, more compensation needed
    // When ambient is warmer, less compensation needed
    compensationAdjustment = baseCompensation * (1.0 - thermalLossRatio) * sensitivity;

    // Limit adjustment to reasonable range (-3°F to +3°F)
    compensationAdjustment = constrain(compensationAdjustment, -3.0, 3.0);
  }

  float finalCompensation = baseCompensation + compensationAdjustment;

  // Debug output every 30 seconds to avoid spam
  static unsigned long lastDebugPrint = 0;
  if (millis() - lastDebugPrint > 30000) {
    Serial.printf("🌡️ Dynamic Compensation: Raw=%.1f°F, Ambient=%.1f°F (ref=%.1f°F), Base=%.1f°F, Sens=%.0f%%, Adj=%.1f°F, Final=%.1f°F\n",
                  rawTempF, ambientTempF, referenceAmbient, baseCompensation, sensitivity*100, compensationAdjustment, finalCompensation);
    lastDebugPrint = millis();
  }

  return finalCompensation;
}

// Calculate compensated body temperature for 4-foot distance
float calculateBodyTemperature() {
  // Find the hottest spot (likely the child's body)
  float maxTempF = maxTemp * 9/5 + 32; // Convert to Fahrenheit

  // Apply smart compensation logic
  float compensatedTemp = applySmartCompensation(maxTempF);

  // Apply very minimal safety filter only for extremely unreasonable readings
  // This prevents completely nonsensical readings but respects user's distance compensation
  float minSafetyTemp = 70.0; // Only filter out readings below 70°F (clearly not body temperature)

  if (compensatedTemp < minSafetyTemp) {
    Serial.printf("Warning: Compensated temp %.1f°F below safety minimum %.1f°F, applying filter\n",
                  compensatedTemp, minSafetyTemp);
    return minSafetyTemp;
  }

  // Trust the distance compensation for all reasonable temperatures
  return compensatedTemp;
}

// Apply distance compensation to all pixels for consistent thermal visualization
void applyCompensationToPixels() {
  for (int i = 0; i < AMG88xx_PIXEL_ARRAY_SIZE; i++) {
    // Convert from Celsius to Fahrenheit
    float tempF = pixels[i] * 9/5 + 32;

    // Apply centralized smart compensation logic
    compensatedPixels[i] = applySmartCompensation(tempF);
  }

  // Debug: Print compensated pixel data occasionally
  static unsigned long lastCompensatedDebugPrint = 0;
  if (millis() - lastCompensatedDebugPrint > 15000) { // Every 15 seconds
    Serial.println("=== Compensated Pixel Data for Web Debug ===");
    Serial.print("Compensated pixels (first 8): ");
    for (int i = 0; i < 8; i++) {
      Serial.print(compensatedPixels[i], 2);
      Serial.print("°F ");
    }
    Serial.println();

    // Show min/max of compensated data
    float minCompensated = compensatedPixels[0];
    float maxCompensated = compensatedPixels[0];
    for (int i = 1; i < AMG88xx_PIXEL_ARRAY_SIZE; i++) {
      if (compensatedPixels[i] < minCompensated) minCompensated = compensatedPixels[i];
      if (compensatedPixels[i] > maxCompensated) maxCompensated = compensatedPixels[i];
    }
    Serial.print("Compensated range: ");
    Serial.print(minCompensated, 2);
    Serial.print("°F to ");
    Serial.print(maxCompensated, 2);
    Serial.println("°F");

    lastCompensatedDebugPrint = millis();
  }
}

// Static variables for baseline learning (need to be accessible for reset)
static int baselineReadings = 0;
static float baselineSum = 0.0;
static float minTempSeen = 999.0;
static float maxTempSeen = 0.0;

// Reset baseline learning variables
void resetBaselineVariables() {
  baselineReadings = 0;
  baselineSum = 0.0;
  minTempSeen = 999.0;
  maxTempSeen = 0.0;

  // Reset learning robustness variables
  consecutiveBelowMinCount = 0;

  Serial.println("Baseline learning variables reset");
}

// Update baseline temperature (normal body temperature for the child)
void updateBaseline() {

  // Check for consecutive low temperature readings before resetting learning
  if (currentBodyTemp < baselineMin) {
    consecutiveBelowMinCount++;

    if (baselineReadings > 0) {
      if (consecutiveBelowMinCount == 1) {
        Serial.print("Temperature below minimum (");
        Serial.print(baselineMin, 1);
        Serial.print("°F) - count: ");
        Serial.print(consecutiveBelowMinCount);
        Serial.print("/");
        Serial.print(maxConsecutiveBelowMin);
        Serial.print(". Current temp: ");
        Serial.print(currentBodyTemp, 1);
        Serial.println("°F");
      } else if (consecutiveBelowMinCount < maxConsecutiveBelowMin) {
        Serial.print("Consecutive low temp reading ");
        Serial.print(consecutiveBelowMinCount);
        Serial.print("/");
        Serial.print(maxConsecutiveBelowMin);
        Serial.print(" (");
        Serial.print(currentBodyTemp, 1);
        Serial.println("°F)");
      } else {
        // Reset baseline learning after consecutive low readings
        Serial.print("Temperature below minimum (");
        Serial.print(baselineMin, 1);
        Serial.print("°F) for ");
        Serial.print(consecutiveBelowMinCount);
        Serial.print(" consecutive readings - resetting baseline learning. Current temp: ");
        Serial.print(currentBodyTemp, 1);
        Serial.println("°F");
        resetBaselineVariables();
      }
    }

    // Don't learn baseline when temperature is below minimum
    if (consecutiveBelowMinCount >= maxConsecutiveBelowMin) {
      return;
    }
  } else {
    // Temperature is above minimum - reset consecutive counter
    if (consecutiveBelowMinCount > 0 && baselineReadings > 0) {
      Serial.print("Temperature returned above minimum (");
      Serial.print(currentBodyTemp, 1);
      Serial.print("°F) - resetting consecutive count, continuing baseline learning");
      Serial.println();
    }
    consecutiveBelowMinCount = 0;
  }

  // Collect readings for 5 minutes to establish baseline
  if (baselineReadings < 150) { // 150 readings over 5 minutes

    // Only include "normal" temperatures in baseline (reject obvious fever readings)
    // Reject readings above 100°F during baseline learning
    if (currentBodyTemp <= 100.0) {
      baselineSum += currentBodyTemp;
      baselineReadings++;

      // Signal learning mode with beeps when first reading is taken
      if (baselineReadings == 1) {
        setBuzzerState(BUZZER_LEARNING);
      }

      // Track temperature range during baseline learning
      if (currentBodyTemp < minTempSeen) minTempSeen = currentBodyTemp;
      if (currentBodyTemp > maxTempSeen) maxTempSeen = currentBodyTemp;

      if (baselineReadings % 30 == 0) {
        Serial.print("Learning baseline... ");
        Serial.print(baselineReadings);
        Serial.print("/150 readings (Range: ");
        Serial.print(minTempSeen, 1);
        Serial.print("-");
        Serial.print(maxTempSeen, 1);
        Serial.print("°F) Min threshold: ");
        Serial.print(baselineMin, 1);
        Serial.println("°F");
      }
    } else {
      Serial.print("Rejecting high temp during baseline learning: ");
      Serial.print(currentBodyTemp, 1);
      Serial.println("°F");
    }
  } else {
    baselineTemp = baselineSum / baselineReadings;
    baselineEstablished = true;
    Serial.print("Baseline established: ");
    Serial.print(baselineTemp, 1);
    Serial.print("°F (Range: ");
    Serial.print(minTempSeen, 1);
    Serial.print("-");
    Serial.print(maxTempSeen, 1);
    Serial.println("°F)");

    // Safety check: ensure baseline is reasonable for human body temperature
    if (baselineTemp < 95.0 || baselineTemp > 100.0) {
      Serial.println("⚠️  WARNING: Baseline outside normal range! Check sensor positioning.");
    }

    // Reset learning robustness variables since learning is complete
    consecutiveBelowMinCount = 0;

    // Save the newly established baseline to persistent storage
    settingsManager.setBaselineTemp(baselineTemp);
    settingsManager.setBaselineEstablished(true);
    settingsManager.collectSettingsFromGlobals();
    settingsManager.saveSettings();
    Serial.println("Baseline saved to persistent storage");

    // Signal that learning is complete - buzzer goes to normal (off) mode
    setBuzzerState(BUZZER_OFF);
  }
}

// Check if baseline should be unlearned (person left the room)
void checkBaselineUnlearning() {
  // Only check if baseline is established
  if (!baselineEstablished) {
    return;
  }

  // Check if temperature is more than 0.5°F below baseline
  float tempDifference = baselineTemp - currentBodyTemp;
  bool currentlyBelowBaseline = tempDifference > 0.5;

  if (currentlyBelowBaseline && !wasBelowBaseline) {
    // Temperature just dropped below baseline - start timer
    belowBaselineStartTime = millis();
    wasBelowBaseline = true;
    Serial.print("Temperature dropped below baseline by ");
    Serial.print(tempDifference, 1);
    Serial.print("°F - monitoring for 30 seconds. Current: ");
    Serial.print(currentBodyTemp, 1);
    Serial.print("°F, Baseline: ");
    Serial.print(baselineTemp, 1);
    Serial.println("°F");
  } else if (!currentlyBelowBaseline && wasBelowBaseline) {
    // Temperature came back up - reset timer
    wasBelowBaseline = false;
    belowBaselineStartTime = 0;
    Serial.println("Temperature returned above baseline threshold");
  } else if (currentlyBelowBaseline && wasBelowBaseline) {
    // Still below baseline - check if 30 seconds have passed
    unsigned long belowDuration = (millis() - belowBaselineStartTime) / 1000; // seconds

    if (belowDuration >= 30) {
      // Unlearn the baseline
      Serial.println("🔄 UNLEARNING BASELINE - Person appears to have left");
      Serial.print("Temperature was ");
      Serial.print(tempDifference, 1);
      Serial.print("°F below baseline for ");
      Serial.print(belowDuration);
      Serial.println(" seconds");

      // Reset baseline
      baselineTemp = 0.0;
      baselineEstablished = false;
      wasBelowBaseline = false;
      belowBaselineStartTime = 0;

      // Reset baseline learning variables
      resetBaselineVariables();

      // Update settings
      settingsManager.setBaselineTemp(0.0);
      settingsManager.setBaselineEstablished(false);
      settingsManager.collectSettingsFromGlobals();
      settingsManager.saveSettings();

      // Reset buzzer to off state since we're waiting for presence again
      setBuzzerState(BUZZER_OFF);

      Serial.println("Baseline reset to 0 and saved to persistent storage");
      Serial.println("System will wait for presence to re-establish baseline");
    }
  }
}

// Check if current temperature indicates fever
bool checkForFever() {
  if (!baselineEstablished) {
    return false; // Can't detect fever without baseline
  }

  // Multiple fever detection methods for robustness
  float tempDifference = currentBodyTemp - baselineTemp;

  // Method 1: Medical fever threshold (absolute)
  bool medicalFever = currentBodyTemp >= 100.4;

  // Method 2: Baseline deviation (relative)
  bool baselineFever = tempDifference >= feverThreshold;

  // Method 3: Sustained elevation check (prevents gradual drift)
  // If temp has been elevated (>99°F) for more than 10 minutes, consider it fever
  static unsigned long elevatedStartTime = 0;
  static bool wasElevated = false;

  bool currentlyElevated = currentBodyTemp >= 99.0;

  if (currentlyElevated && !wasElevated) {
    elevatedStartTime = millis();
    Serial.println("Temperature elevated above 99°F - monitoring...");
  } else if (!currentlyElevated) {
    elevatedStartTime = 0;
  }

  wasElevated = currentlyElevated;

  bool sustainedElevation = false;
  if (elevatedStartTime > 0) {
    unsigned long elevatedDuration = (millis() - elevatedStartTime) / 1000 / 60; // minutes
    sustainedElevation = (elevatedDuration >= 10) && (currentBodyTemp >= 99.0);

    if (sustainedElevation) {
      Serial.print("Sustained elevation detected: ");
      Serial.print(elevatedDuration);
      Serial.println(" minutes above 99°F");
    }
  }

  // Check if any fever condition is met (but don't return true yet)
  bool anyFeverCondition = medicalFever || baselineFever || sustainedElevation;

  // Sustained fever alert logic - require 10 seconds of elevated temperature
  // Using global variables so getFeverStatus() can access them

  if (anyFeverCondition && !wasFeverCondition) {
    // Fever condition just started - begin timing
    feverConditionStartTime = millis();
    wasFeverCondition = true;
    Serial.print("Fever condition detected - monitoring for 10 seconds. Temp: ");
    Serial.print(currentBodyTemp, 1);
    Serial.print("°F (Baseline: ");
    Serial.print(baselineTemp, 1);
    Serial.print("°F, Diff: +");
    Serial.print(tempDifference, 1);
    Serial.println("°F)");
  } else if (!anyFeverCondition && wasFeverCondition) {
    // Fever condition ended - reset timer
    wasFeverCondition = false;
    feverConditionStartTime = 0;
    Serial.println("Fever condition ended - resetting timer");
  }

  // Only return true if fever condition has been sustained for 10+ seconds
  bool sustainedFeverAlert = false;
  if (anyFeverCondition && wasFeverCondition && feverConditionStartTime > 0) {
    unsigned long feverDuration = (millis() - feverConditionStartTime) / 1000; // seconds
    sustainedFeverAlert = feverDuration >= 10;

    if (sustainedFeverAlert) {
      // Debug output only when fever is actually triggered
      Serial.print("SUSTAINED FEVER ALERT - Duration: ");
      Serial.print(feverDuration);
      Serial.print("s | Medical: ");
      Serial.print(medicalFever ? "YES" : "NO");
      Serial.print(" | Baseline: ");
      Serial.print(baselineFever ? "YES" : "NO");
      Serial.print(" | Sustained: ");
      Serial.println(sustainedElevation ? "YES" : "NO");
    } else {
      // Show countdown during monitoring period
      static unsigned long lastCountdownPrint = 0;
      if (millis() - lastCountdownPrint > 1000) { // Print every second
        unsigned long remaining = 10 - feverDuration;
        Serial.print("Fever monitoring: ");
        Serial.print(remaining);
        Serial.print("s remaining (Temp: ");
        Serial.print(currentBodyTemp, 1);
        Serial.println("°F)");
        lastCountdownPrint = millis();
      }
    }
  }

  return sustainedFeverAlert;
}

// Log temperature to history array
void logTemperature(float temp) {
  tempHistory[historyIndex] = temp;
  historyIndex = (historyIndex + 1) % 100; // Circular buffer
}

// Get average temperature from recent history
float getAverageTemp(int samples) {
  if (samples > 100) samples = 100;

  float sum = 0.0;
  int count = 0;

  for (int i = 0; i < samples; i++) {
    int index = (historyIndex - 1 - i + 100) % 100;
    if (tempHistory[index] > 0) {
      sum += tempHistory[index];
      count++;
    }
  }

  return count > 0 ? sum / count : 0.0;
}

// Get current fever status as string
String getFeverStatus() {
  if (!baselineEstablished) {
    // Check if we're monitoring consecutive low temperature readings during learning
    if (consecutiveBelowMinCount > 0 && consecutiveBelowMinCount < maxConsecutiveBelowMin) {
      return "Learning paused - low temp (" + String(consecutiveBelowMinCount) + "/" + String(maxConsecutiveBelowMin) + ")";
    }

    // Check if we're waiting for someone to be present
    if (currentBodyTemp < baselineMin) {
      return "Waiting for presence (temp < " + String(baselineMin, 1) + "°F)";
    } else {
      return "Learning baseline...";
    }
  }

  if (feverDetected) {
    unsigned long feverDuration = (millis() - feverStartTime) / 1000 / 60; // minutes
    return "FEVER DETECTED (" + String(feverDuration) + " min)";
  }

  // Check if we're monitoring for sustained fever (10-second requirement)
  if (wasFeverCondition && feverConditionStartTime > 0) {
    unsigned long feverDuration = (millis() - feverConditionStartTime) / 1000; // seconds
    if (feverDuration < 10) {
      unsigned long remaining = 10 - feverDuration;
      float tempDiff = currentBodyTemp - baselineTemp;
      return "Monitoring fever (" + String(remaining) + "s, +" + String(tempDiff, 1) + "°F)";
    }
  }

  // Check if we're monitoring for baseline unlearning
  if (wasBelowBaseline && belowBaselineStartTime > 0) {
    unsigned long belowDuration = (millis() - belowBaselineStartTime) / 1000; // seconds
    unsigned long remaining = 30 - belowDuration;
    if (remaining > 0) {
      float tempDiff = baselineTemp - currentBodyTemp;
      return "Monitoring departure (" + String(remaining) + "s, -" + String(tempDiff, 1) + "°F)";
    }
  }

  float tempDiff = currentBodyTemp - baselineTemp;
  if (tempDiff > 1.0) {
    return "Elevated (+" + String(tempDiff, 1) + "°F)";
  } else if (tempDiff > 0.5) {
    return "Slightly elevated";
  } else {
    return "Normal";
  }
}
