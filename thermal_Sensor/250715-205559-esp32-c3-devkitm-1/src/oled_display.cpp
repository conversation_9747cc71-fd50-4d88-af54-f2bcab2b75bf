#include "oled_display.h"
#include "thermal_sensor.h"
#include "settings_manager.h"
#include "aht10_sensor.h"
#include "time_manager.h"
#include <Wire.h>

// Last refresh millis
unsigned long lastRefresh = 0;
// use lastPixelReadTime from thermal_sensor.h

// Use the same I2C bus but different address
Adafruit_SSD1306 display(SCREEN_WIDTH, SCREEN_HEIGHT, &Wire, OLED_RESET);

// Animation task control variables - Singleton pattern
TaskHandle_t animationTaskHandle = NULL;
bool isWaitingForPresence = false;
bool animationTaskCreating = false; // Prevent multiple creation attempts
unsigned long lastAnimationStart = 0; // Track when animation was last started
const unsigned long ANIMATION_RESTART_DELAY = 1000; // Minimum delay between restarts (ms)

void initDisplay() {
  Serial.println("Initializing OLED display...");

  // The I2C bus is already initialized in thermal_sensor.cpp
  // Just initialize the OLED display on the existing bus
  if (!display.begin(SSD1306_SWITCHCAPVCC, SCREEN_ADDRESS)) {
    Serial.println(F("SSD1306 allocation failed"));
    // Try alternative I2C address
    if (!display.begin(SSD1306_SWITCHCAPVCC, 0x3D)) {
      Serial.println(F("SSD1306 allocation failed on both addresses"));
      return;
    } else {
      Serial.println("OLED found at address 0x3D");
    }
  } else {
    Serial.println("OLED found at address 0x3C");
  }
  
  // Clear the display buffer
  display.clearDisplay();
  
  // Set text properties
  display.setTextSize(1);
  display.setTextColor(SSD1306_WHITE);
  
  // Set initial brightness from settings
  ThermalSettings settings = settingsManager.getSettings();
  setDisplayBrightness(settings.displayBrightness);
  Serial.print("OLED brightness set to: ");
  Serial.println(settings.displayBrightness);

  // Show initial message
  display.setCursor(0, 0);
  display.println(F("Thermal Sensor Ready!"));
  display.println(F("OLED Ready!, initializing WIFI.."));
  display.display();

  delay(2000);
  display.clearDisplay();
}

void displayThermalData(const char* url) {
  // Check if we're waiting for presence (no one detected)
  if (!baselineEstablished && currentBodyTemp < baselineMin) {
    // During night mode, turn off display completely when no presence detected
    if (settingsManager.isNightModeEnabled() &&
        settingsManager.isDisplayOffAtNight() &&
        isNightModeTime()) {
      // Turn off display during night hours when no presence detected
      display.clearDisplay();
      display.display();

      // Stop any running animation during night mode
      if (isWaitingForPresence) {
        stopWaitingAnimation();
      }
      return;
    }

    // Not night mode or night mode disabled - show eye animation
    // Start animation task if not already running
    if (!isWaitingForPresence) {
      startWaitingAnimation();
    }
    return;
  } else if (!baselineEstablished && currentBodyTemp >= baselineMin) {
    // Presence detected but baseline not yet established
    // During night mode, keep display off until baseline is established
    if (settingsManager.isNightModeEnabled() &&
        settingsManager.isDisplayOffAtNight() &&
        isNightModeTime()) {
      // Turn off display during night hours while learning baseline
      display.clearDisplay();
      display.display();

      // Stop any running animation during night mode
      if (isWaitingForPresence) {
        stopWaitingAnimation();
      }
      return;
    }

    // Not night mode or night mode disabled - show learning display
    // Stop animation task if running
    if (isWaitingForPresence) {
      stopWaitingAnimation();
    }
  } else {
    // Baseline established - always show display even during night mode
    // Stop animation task if running
    if (isWaitingForPresence) {
      stopWaitingAnimation();
    }
  }

  // return if last refresh was less than 1000ms ago
  if (millis() - lastRefresh < 500) {
    return;
  }
  lastRefresh = millis();

  // Clear the display buffer
  display.clearDisplay();

  // Line 1: Display status at the top
  display.setCursor(0, 0);
  display.setTextSize(1);

  if (feverDetected) {
    display.print(F("FEVER!"));
  } else if (baselineEstablished) {
    display.print(F("Normal"));
  } else {
    display.print(F("Learning..."));
  }

  // Lines 2-3: Display current body temperature prominently (large 2-line font)
  display.setCursor(0, 8);
  display.setTextSize(2);  // Large text for temperature
  if (feverDetected) {
    display.print(F("!"));
  }
  display.print(currentBodyTemp, 1);
  display.print(F("F"));
  display.setTextSize(1);

  // Line 4: Display ambient temp and humidity in compact format
  if (isAHT10Available()) {
    AHT10Data aht10Data = getAHT10Data();
    display.setCursor(0, 24);
    display.print(F("A:"));
    display.print(aht10Data.temperatureF, 0);
    display.print(F("F H:"));
    display.print(aht10Data.humidity, 0);
    display.print(F("%"));
  } else {
    display.setCursor(0, 24);
    display.print(F("A:-- H:--"));
  }

  // Simple thermal visualization (8x8 grid represented as dots)
  // Position on right side covering lines 2-4
  int startX = 85;
  int startY = 8;
  int dotSpacing = 5;

  for (int i = 0; i < 8; i++) {
    for (int j = 0; j < 8; j++) {
      int pixelIndex = i * 8 + j;
      float temp = pixels[pixelIndex];

      // Map temperature to display intensity (0-2 for dot size)
      int intensity = map(temp * 10, minTemp * 10, maxTemp * 10, 0, 2);
      intensity = constrain(intensity, 0, 2);

      int x = startX + j * dotSpacing;
      int y = startY + i * 2;  // Compact spacing to fit in 24 pixels (lines 2-4)

      // Draw dots based on intensity, but keep them within screen bounds
      if (intensity > 0 && x < SCREEN_WIDTH && y < SCREEN_HEIGHT) {
        display.drawPixel(x, y, SSD1306_WHITE);
        if (intensity > 1 && x+1 < SCREEN_WIDTH && y+1 < SCREEN_HEIGHT) {
          display.drawPixel(x+1, y, SSD1306_WHITE);
          display.drawPixel(x, y+1, SSD1306_WHITE);
          display.drawPixel(x+1, y+1, SSD1306_WHITE);
        }
      }
    }
  }
  
  display.display();
}

void displayMessage(const char* message) {
  display.clearDisplay();
  display.setCursor(0, 0);
  display.setTextSize(1);
  display.println(message);
  display.display();
}

void clearDisplay() {
  display.clearDisplay();
  display.display();
}

void displayWaitingForPresence() {
  // Reduced refresh rate to prevent overheating - increased from 50ms to 200ms
  if (millis() - lastRefresh < 20) {
    return;
  }
  lastRefresh = millis();

  // Clear the display buffer
  display.clearDisplay();

  // Animation variables - use millis for smooth animation
  static unsigned long animationStart = 0;
  static unsigned long lastLedToggle = 0;
  static bool ledState = false;

  // Blink animation variables
  static unsigned long lastBlink = 0;
  static unsigned long nextBlinkDelay = 2000; // Start with 2 second delay
  static bool isBlinking = false;
  static unsigned long blinkStart = 0;
  static int blinkPhase = 0; // 0=closing, 1=closed, 2=opening

  if (animationStart == 0) {
    animationStart = millis();
    // Initialize LED pin (pin 8 is the onboard LED)
    pinMode(8, OUTPUT);
  }

  unsigned long elapsed = millis() - animationStart;

  // Flash LED every 400ms (faster blinking)
  if (millis() - lastLedToggle > 400) {
    ledState = !ledState;
    digitalWrite(8, ledState ? HIGH : LOW);
    lastLedToggle = millis();
  }

  // Create scanning movement pattern (4 second cycle with pauses)
  float cycle = (elapsed % 4000) / 4000.0; // 0.0 to 1.0 over 4 seconds

  // Create scanning pattern: fast movement with pauses at ends
  float lookAngleX = 0.0;
  float lookAngleY = 0.0;

  if (cycle < 0.1) {
    // Pause at left (0-0.4 seconds)
    lookAngleX = -0.9;
  } else if (cycle < 0.35) {
    // Fast movement left to right (0.4-1.4 seconds = 1 second)
    float moveProgress = (cycle - 0.1) / 0.25; // 0.0 to 1.0
    lookAngleX = -0.9 + (moveProgress * 1.8); // -0.9 to +0.9
  } else if (cycle < 0.6) {
    // Pause at right (1.4-2.4 seconds)
    lookAngleX = 0.9;
  } else if (cycle < 0.85) {
    // Fast movement right to left (2.4-3.4 seconds = 1 second)
    float moveProgress = (cycle - 0.6) / 0.25; // 0.0 to 1.0
    lookAngleX = 0.9 - (moveProgress * 1.8); // +0.9 to -0.9
  } else {
    // Final pause at left (3.4-4.0 seconds)
    lookAngleX = -0.9;
  }

  // Add slight vertical movement for more natural look
  lookAngleY = sin(cycle * 4 * PI) * 0.1;

  // Full screen eye parameters - use maximum available space
  int eyeRadius = 14; // Much larger eyes
  int eyeSpacing = 64; // Spread eyes across full width
  int eyeCenterY = SCREEN_HEIGHT / 2; // Center vertically

  // Left eye center - positioned for full screen usage
  int leftEyeX = SCREEN_WIDTH / 4; // 1/4 from left
  // Right eye center
  int rightEyeX = (SCREEN_WIDTH * 3) / 4; // 3/4 from left

  // Calculate disc positions (moving within the eye circles)
  int leftDiscX = leftEyeX + (int)(lookAngleX * 8);
  int leftDiscY = eyeCenterY + (int)(lookAngleY * 6);
  int rightDiscX = rightEyeX + (int)(lookAngleX * 8);
  int rightDiscY = eyeCenterY + (int)(lookAngleY * 6);

  // Only draw normal eyes if not blinking
  if (!isBlinking) {
    // Draw left disc (no outline)
    display.fillCircle(leftDiscX, leftDiscY, eyeRadius, SSD1306_WHITE);

    // Draw right disc (no outline)
    display.fillCircle(rightDiscX, rightDiscY, eyeRadius, SSD1306_WHITE);
  }

  // Handle blink animation timing

  // Check if it's time for a new blink
  if (!isBlinking && (millis() - lastBlink > nextBlinkDelay)) {
    isBlinking = true;
    blinkStart = millis();
    lastBlink = millis();
    blinkPhase = 0; // Start closing
    // Randomize next blink delay between 2-6 seconds for more natural feel
    nextBlinkDelay = 2000 + (millis() % 4000);
  }

  // Handle blink animation phases with more naturalistic timing
  if (isBlinking) {
    unsigned long blinkElapsed = millis() - blinkStart;

    if (blinkPhase == 0 && blinkElapsed > 150) { // Closing phase (150ms) - slower close
      blinkPhase = 1; // Move to closed phase
    } else if (blinkPhase == 1 && blinkElapsed > 200) { // Closed phase (50ms total) - brief pause
      blinkPhase = 2; // Move to opening phase
    } else if (blinkPhase == 2 && blinkElapsed > 380) { // Opening phase (180ms) - slower open
      isBlinking = false; // Blink complete
      blinkPhase = 0;
    }
  }

  // Draw robotic blink animation with compressed circle effect
  if (isBlinking) {
    unsigned long blinkElapsed = millis() - blinkStart;
    float blinkProgress = 0.0;

    if (blinkPhase == 0) { // Closing - slower, more natural
      blinkProgress = (float)blinkElapsed / 150.0; // 0.0 to 1.0 over 150ms
      // Add easing for more natural movement (ease-in)
      blinkProgress = blinkProgress * blinkProgress;
    } else if (blinkPhase == 1) { // Fully closed
      blinkProgress = 1.0;
    } else if (blinkPhase == 2) { // Opening - slower, more natural
      float openProgress = ((float)(blinkElapsed - 200) / 180.0); // 0.0 to 1.0 over 180ms
      // Add easing for more natural movement (ease-out)
      openProgress = 1.0 - (1.0 - openProgress) * (1.0 - openProgress);
      blinkProgress = 1.0 - openProgress; // 1.0 to 0.0
    }

    // Constrain progress
    blinkProgress = constrain(blinkProgress, 0.0, 1.0);

    // Calculate compressed eye dimensions
    // As blink progresses, vertical radius shrinks and horizontal radius expands slightly
    int verticalRadius = (int)(eyeRadius * (1.0 - blinkProgress * 0.9)); // Compress to 10% of original height
    int horizontalRadius = (int)(eyeRadius * (1.0 + blinkProgress * 0.2)); // Expand horizontally by 20%

    // Draw compressed eye circles (ellipses) for both eyes
    // Left eye - draw compressed ellipse
    for (int y = -verticalRadius; y <= verticalRadius; y++) {
      // Calculate ellipse width at this y position
      float normalizedY = (float)y / verticalRadius;
      int halfWidth = (int)(horizontalRadius * sqrt(1.0 - normalizedY * normalizedY));

      // Draw horizontal line for this y position
      if (halfWidth > 0) {
        display.drawLine(leftEyeX - halfWidth, eyeCenterY + y,
                        leftEyeX + halfWidth, eyeCenterY + y, SSD1306_WHITE);
      }
    }

    // Right eye - draw compressed ellipse
    for (int y = -verticalRadius; y <= verticalRadius; y++) {
      // Calculate ellipse width at this y position
      float normalizedY = (float)y / verticalRadius;
      int halfWidth = (int)(horizontalRadius * sqrt(1.0 - normalizedY * normalizedY));

      // Draw horizontal line for this y position
      if (halfWidth > 0) {
        display.drawLine(rightEyeX - halfWidth, eyeCenterY + y,
                        rightEyeX + halfWidth, eyeCenterY + y, SSD1306_WHITE);
      }
    }
  }

  display.display();
}

// Start the animation task - Singleton pattern with safeguards
void startWaitingAnimation() {
  // Prevent multiple creation attempts
  if (animationTaskCreating) {
    Serial.println("Animation task creation already in progress, skipping");
    return;
  }

  // Check if task already exists
  if (animationTaskHandle != NULL) {
    Serial.println("Animation task already running, skipping creation");
    return;
  }

  // Prevent rapid restarts that could cause issues
  unsigned long currentTime = millis();
  if (currentTime - lastAnimationStart < ANIMATION_RESTART_DELAY) {
    Serial.println("Animation restart too soon, waiting...");
    return;
  }

  // Set creation flag to prevent concurrent attempts
  animationTaskCreating = true;

  // Create the task
  BaseType_t result = xTaskCreatePinnedToCore(
    animationTask,           // Function to run
    "AnimationTask",         // Name
    4096,                    // Stack size
    NULL,                    // Parameter
    1,                       // Priority (low priority)
    &animationTaskHandle,    // Task handle
    0                        // Core 0 (opposite of main loop)
  );

  if (result == pdPASS && animationTaskHandle != NULL) {
    isWaitingForPresence = true;
    lastAnimationStart = currentTime;
    Serial.println("Animation task started successfully");
  } else {
    Serial.println("Failed to create animation task");
    animationTaskHandle = NULL;
    isWaitingForPresence = false;
  }

  // Clear creation flag
  animationTaskCreating = false;
}

// Stop the animation task - Enhanced with better cleanup
void stopWaitingAnimation() {
  if (animationTaskHandle != NULL) {
    Serial.println("Stopping animation task...");

    // Signal the task to stop
    isWaitingForPresence = false;

    // Give the task a moment to exit gracefully
    vTaskDelay(100 / portTICK_PERIOD_MS);

    // Force delete if still running
    if (animationTaskHandle != NULL) {
      vTaskDelete(animationTaskHandle);
      animationTaskHandle = NULL;
      Serial.println("Animation task force-deleted");
    }

    // Reset all animation state
    animationTaskCreating = false;

    // Clear the display when stopping animation
    display.clearDisplay();
    display.display();

    // Turn off LED if it was on
    digitalWrite(8, LOW);

    Serial.println("Animation task stopped and cleaned up");
  }
}

// FreeRTOS task function for animation - Enhanced with better error handling
void animationTask(void *pvParameters) {
  Serial.println("Animation task running");

  // Initialize LED pin safely
  pinMode(8, OUTPUT);
  digitalWrite(8, LOW);

  unsigned long taskStartTime = millis();
  unsigned long lastYield = 0;
  const unsigned long MAX_TASK_RUNTIME = 300000; // 5 minutes max runtime as safety

  while (isWaitingForPresence) {
    // Safety check: prevent runaway task
    if (millis() - taskStartTime > MAX_TASK_RUNTIME) {
      Serial.println("Animation task safety timeout - stopping");
      break;
    }

    // Ensure we yield regularly to prevent watchdog issues
    if (millis() - lastYield > 100) {
      yield();
      lastYield = millis();
    }

    // Run the animation
    displayWaitingForPresence();

    // Longer delay to reduce CPU usage and prevent overheating
    vTaskDelay(100 / portTICK_PERIOD_MS); // Increased from 50ms to 100ms
  }

  // Cleanup before exit
  digitalWrite(8, LOW); // Turn off LED
  isWaitingForPresence = false;
  animationTaskHandle = NULL;

  Serial.println("Animation task ending gracefully");
  vTaskDelete(NULL); // Delete self
}

// Check if animation task is running and healthy
bool isAnimationTaskRunning() {
  return (animationTaskHandle != NULL && isWaitingForPresence && !animationTaskCreating);
}

// Force stop animation if it gets stuck
void forceStopAnimation() {
  Serial.println("Force stopping animation task...");

  if (animationTaskHandle != NULL) {
    vTaskDelete(animationTaskHandle);
    animationTaskHandle = NULL;
  }

  isWaitingForPresence = false;
  animationTaskCreating = false;

  // Clean up display and LED
  display.clearDisplay();
  display.display();
  digitalWrite(8, LOW);

  Serial.println("Animation task force stopped");
}

// Set display brightness (0-255)
void setDisplayBrightness(uint8_t brightness) {
  Serial.print("Setting OLED brightness to: ");
  Serial.println(brightness);

  // Use SSD1306 contrast command to control brightness
  // The SSD1306 uses contrast values from 0-255
  display.ssd1306_command(SSD1306_SETCONTRAST);
  display.ssd1306_command(brightness);
}
