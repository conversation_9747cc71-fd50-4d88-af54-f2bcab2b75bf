#include "settings_manager.h"
#include "thermal_sensor.h"
#include "buzzer.h"

// Constants
const char* SettingsManager::NAMESPACE = "thermal_cfg";
const int SettingsManager::CURRENT_VERSION = 3;

// Initialize default settings
void initializeDefaultSettings() {
  // Fever detection settings
  DEFAULT_SETTINGS.feverThreshold = 2.0;        // 2°F above baseline
  DEFAULT_SETTINGS.distanceCompensation = 4.5;  // 4.5°F compensation for 4 feet
  DEFAULT_SETTINGS.ambientTemp = 70.0;          // 70°F room temperature
  DEFAULT_SETTINGS.personThreshold = 4.0;       // 4°F above ambient to detect person presence
  DEFAULT_SETTINGS.dynamicCompensation = true;  // Enable dynamic ambient-based compensation
  DEFAULT_SETTINGS.compensationSensitivity = 30.0; // 30% sensitivity for ambient adjustments

  // Baseline settings
  DEFAULT_SETTINGS.baselineTemp = 0.0;          // Always start at 0 after restart
  DEFAULT_SETTINGS.baselineEstablished = false; // Always start not established

  // Advanced settings
  DEFAULT_SETTINGS.medicalThreshold = 100.4;    // Medical fever threshold
  DEFAULT_SETTINGS.sustainedTime = 10;          // 10 minutes sustained
  DEFAULT_SETTINGS.baselineDuration = 5;        // 5 minutes to establish baseline
  DEFAULT_SETTINGS.baselineMax = 100.0;         // Maximum baseline temp
  DEFAULT_SETTINGS.baselineMin = 90.0;          // Minimum temp to start baseline learning

  // System settings
  DEFAULT_SETTINGS.debugMode = false;           // Debug disabled by default
  DEFAULT_SETTINGS.readInterval = 100;          // 100ms read interval
  DEFAULT_SETTINGS.logInterval = 30;            // 30 second logging

  // Display settings
  DEFAULT_SETTINGS.displayBrightness = 255;     // Full brightness by default (0-255)

  // Audio settings
  DEFAULT_SETTINGS.buzzerEnabled = true;        // Buzzer enabled by default

  // Time and schedule settings
  strcpy(DEFAULT_SETTINGS.ntpServer, "pool.ntp.org");  // Default NTP server
  strcpy(DEFAULT_SETTINGS.timezone, "EST5EDT");        // Default to Eastern Time
  DEFAULT_SETTINGS.gmtOffsetSec = -18000;       // EST: UTC-5 hours (-5 * 3600)
  DEFAULT_SETTINGS.daylightOffsetSec = 3600;    // EDT: +1 hour for daylight saving
  DEFAULT_SETTINGS.autoTimeSync = true;         // Enable automatic time sync
  DEFAULT_SETTINGS.timeSyncInterval = 24;       // Sync every 24 hours

  // Night mode settings
  DEFAULT_SETTINGS.nightModeEnabled = true;     // Enable night mode by default
  DEFAULT_SETTINGS.nightStartHour = 23;         // 11 PM
  DEFAULT_SETTINGS.nightStartMinute = 0;        // 11:00 PM
  DEFAULT_SETTINGS.nightEndHour = 8;            // 8 AM
  DEFAULT_SETTINGS.nightEndMinute = 0;          // 8:00 AM
  DEFAULT_SETTINGS.displayOffAtNight = true;    // Turn off display at night

  // Volume settings (0-100%)
  DEFAULT_SETTINGS.dayVolume = 100;             // 100% volume during day
  DEFAULT_SETTINGS.nightVolume = 25;            // 25% volume during night (non-fever)
  DEFAULT_SETTINGS.feverVolume = 75;            // 75% volume for fever alerts

  // Version
  DEFAULT_SETTINGS.version = 3;                 // version - Increment version for new volume settings
}

// Default settings values
ThermalSettings DEFAULT_SETTINGS;

// Global instance
SettingsManager settingsManager;

SettingsManager::SettingsManager() {
  initializeDefaultSettings();
  currentSettings = DEFAULT_SETTINGS;
}

SettingsManager::~SettingsManager() {
  end();
}

bool SettingsManager::begin() {
  bool success = preferences.begin(NAMESPACE, false);
  if (success) {
    Serial.println("Settings manager initialized");
  } else {
    Serial.println("Failed to initialize settings manager");
  }
  return success;
}

void SettingsManager::end() {
  preferences.end();
}

bool SettingsManager::loadSettings() {
  if (!preferences.isKey("version")) {
    Serial.println("No stored settings found, using defaults");
    currentSettings = DEFAULT_SETTINGS;
    // Ensure baseline starts at 0
    currentSettings.baselineTemp = 0.0;
    currentSettings.baselineEstablished = false;
    Serial.println("🔄 Baseline set to 0 - fresh calibration required");
    return false;
  }
  
  int storedVersion = preferences.getInt("version", 0);
  Serial.printf("Loading settings version %d\n", storedVersion);
  
  // Load all settings
  currentSettings.feverThreshold = preferences.getFloat("feverThresh", DEFAULT_SETTINGS.feverThreshold);
  currentSettings.distanceCompensation = preferences.getFloat("distComp", DEFAULT_SETTINGS.distanceCompensation);
  currentSettings.ambientTemp = preferences.getFloat("ambientTemp", DEFAULT_SETTINGS.ambientTemp);
  currentSettings.personThreshold = preferences.getFloat("personThresh", DEFAULT_SETTINGS.personThreshold);
  currentSettings.dynamicCompensation = preferences.getBool("dynComp", DEFAULT_SETTINGS.dynamicCompensation);
  currentSettings.compensationSensitivity = preferences.getFloat("compSens", DEFAULT_SETTINGS.compensationSensitivity);
  // Always reset baseline to 0 on startup (force fresh calibration)
  currentSettings.baselineTemp = 0.0;
  currentSettings.baselineEstablished = false;
  Serial.println("🔄 Baseline reset to 0 on startup - fresh calibration required");
  currentSettings.medicalThreshold = preferences.getFloat("medThresh", DEFAULT_SETTINGS.medicalThreshold);
  currentSettings.sustainedTime = preferences.getInt("sustainTime", DEFAULT_SETTINGS.sustainedTime);
  currentSettings.baselineDuration = preferences.getInt("baseDuration", DEFAULT_SETTINGS.baselineDuration);
  currentSettings.baselineMax = preferences.getFloat("baselineMax", DEFAULT_SETTINGS.baselineMax);
  currentSettings.baselineMin = preferences.getFloat("baselineMin", DEFAULT_SETTINGS.baselineMin);
  currentSettings.debugMode = preferences.getBool("debugMode", DEFAULT_SETTINGS.debugMode);
  currentSettings.readInterval = preferences.getInt("readInterval", DEFAULT_SETTINGS.readInterval);
  currentSettings.logInterval = preferences.getInt("logInterval", DEFAULT_SETTINGS.logInterval);
  currentSettings.displayBrightness = preferences.getInt("displayBright", DEFAULT_SETTINGS.displayBrightness);
  currentSettings.buzzerEnabled = preferences.getBool("buzzerEnabled", DEFAULT_SETTINGS.buzzerEnabled);

  // Load time settings
  preferences.getString("ntpServer", currentSettings.ntpServer, sizeof(currentSettings.ntpServer));
  if (strlen(currentSettings.ntpServer) == 0) {
    strcpy(currentSettings.ntpServer, DEFAULT_SETTINGS.ntpServer);
  }
  preferences.getString("timezone", currentSettings.timezone, sizeof(currentSettings.timezone));
  if (strlen(currentSettings.timezone) == 0) {
    strcpy(currentSettings.timezone, DEFAULT_SETTINGS.timezone);
  }
  currentSettings.gmtOffsetSec = preferences.getInt("gmtOffset", DEFAULT_SETTINGS.gmtOffsetSec);
  currentSettings.daylightOffsetSec = preferences.getInt("daylightOffset", DEFAULT_SETTINGS.daylightOffsetSec);
  currentSettings.autoTimeSync = preferences.getBool("autoTimeSync", DEFAULT_SETTINGS.autoTimeSync);
  currentSettings.timeSyncInterval = preferences.getInt("timeSyncInt", DEFAULT_SETTINGS.timeSyncInterval);
  currentSettings.nightModeEnabled = preferences.getBool("nightMode", DEFAULT_SETTINGS.nightModeEnabled);
  currentSettings.nightStartHour = preferences.getInt("nightStartH", DEFAULT_SETTINGS.nightStartHour);
  currentSettings.nightStartMinute = preferences.getInt("nightStartM", DEFAULT_SETTINGS.nightStartMinute);
  currentSettings.nightEndHour = preferences.getInt("nightEndH", DEFAULT_SETTINGS.nightEndHour);
  currentSettings.nightEndMinute = preferences.getInt("nightEndM", DEFAULT_SETTINGS.nightEndMinute);
  currentSettings.displayOffAtNight = preferences.getBool("displayOffNight", DEFAULT_SETTINGS.displayOffAtNight);

  // Load volume settings
  currentSettings.dayVolume = preferences.getInt("dayVolume", DEFAULT_SETTINGS.dayVolume);
  currentSettings.nightVolume = preferences.getInt("nightVolume", DEFAULT_SETTINGS.nightVolume);
  currentSettings.feverVolume = preferences.getInt("feverVolume", DEFAULT_SETTINGS.feverVolume);

  currentSettings.version = storedVersion;
  
  // Handle version migration if needed
  if (storedVersion < CURRENT_VERSION) {
    Serial.printf("Migrating settings from version %d to %d\n", storedVersion, CURRENT_VERSION);
    migrateSettings(storedVersion);
    currentSettings.version = CURRENT_VERSION;
    saveSettings(); // Save migrated settings
  }
  
  Serial.println("Settings loaded successfully");
  printSettings();
  return true;
}

bool SettingsManager::saveSettings() {
  Serial.println("Saving settings to flash...");
  
  // Save all settings
  preferences.putFloat("feverThresh", currentSettings.feverThreshold);
  preferences.putFloat("distComp", currentSettings.distanceCompensation);
  preferences.putFloat("ambientTemp", currentSettings.ambientTemp);
  preferences.putFloat("personThresh", currentSettings.personThreshold);
  preferences.putBool("dynComp", currentSettings.dynamicCompensation);
  preferences.putFloat("compSens", currentSettings.compensationSensitivity);
  preferences.putFloat("baselineTemp", currentSettings.baselineTemp);
  preferences.putBool("baselineEst", currentSettings.baselineEstablished);
  preferences.putFloat("medThresh", currentSettings.medicalThreshold);
  preferences.putInt("sustainTime", currentSettings.sustainedTime);
  preferences.putInt("baseDuration", currentSettings.baselineDuration);
  preferences.putFloat("baselineMax", currentSettings.baselineMax);
  preferences.putFloat("baselineMin", currentSettings.baselineMin);
  preferences.putBool("debugMode", currentSettings.debugMode);
  preferences.putInt("readInterval", currentSettings.readInterval);
  preferences.putInt("logInterval", currentSettings.logInterval);
  preferences.putInt("displayBright", currentSettings.displayBrightness);
  preferences.putBool("buzzerEnabled", currentSettings.buzzerEnabled);

  // Save time settings
  preferences.putString("ntpServer", currentSettings.ntpServer);
  preferences.putString("timezone", currentSettings.timezone);
  preferences.putInt("gmtOffset", currentSettings.gmtOffsetSec);
  preferences.putInt("daylightOffset", currentSettings.daylightOffsetSec);
  preferences.putBool("autoTimeSync", currentSettings.autoTimeSync);
  preferences.putInt("timeSyncInt", currentSettings.timeSyncInterval);
  preferences.putBool("nightMode", currentSettings.nightModeEnabled);
  preferences.putInt("nightStartH", currentSettings.nightStartHour);
  preferences.putInt("nightStartM", currentSettings.nightStartMinute);
  preferences.putInt("nightEndH", currentSettings.nightEndHour);
  preferences.putInt("nightEndM", currentSettings.nightEndMinute);
  preferences.putBool("displayOffNight", currentSettings.displayOffAtNight);

  // Save volume settings
  preferences.putInt("dayVolume", currentSettings.dayVolume);
  preferences.putInt("nightVolume", currentSettings.nightVolume);
  preferences.putInt("feverVolume", currentSettings.feverVolume);

  preferences.putInt("version", CURRENT_VERSION);
  
  Serial.println("Settings saved successfully");
  return true;
}

bool SettingsManager::resetToDefaults() {
  Serial.println("Resetting settings to defaults...");
  
  // Clear all stored settings
  preferences.clear();
  
  // Reset to defaults
  currentSettings = DEFAULT_SETTINGS;
  currentSettings.version = CURRENT_VERSION;
  
  // Save defaults
  bool success = saveSettings();
  
  if (success) {
    Serial.println("Settings reset to defaults successfully");
    printSettings();
  } else {
    Serial.println("Failed to reset settings");
  }
  
  return success;
}

ThermalSettings SettingsManager::getSettings() const {
  return currentSettings;
}

// Getter methods
float SettingsManager::getFeverThreshold() const { return currentSettings.feverThreshold; }
float SettingsManager::getDistanceCompensation() const { return currentSettings.distanceCompensation; }
float SettingsManager::getAmbientTemp() const { return currentSettings.ambientTemp; }
float SettingsManager::getPersonThreshold() const { return currentSettings.personThreshold; }
float SettingsManager::getBaselineTemp() const { return currentSettings.baselineTemp; }
bool SettingsManager::isBaselineEstablished() const { return currentSettings.baselineEstablished; }
float SettingsManager::getBaselineMin() const { return currentSettings.baselineMin; }
bool SettingsManager::isDynamicCompensationEnabled() const { return currentSettings.dynamicCompensation; }
float SettingsManager::getCompensationSensitivity() const { return currentSettings.compensationSensitivity; }

// Setter methods
void SettingsManager::setFeverThreshold(float value) { 
  currentSettings.feverThreshold = value; 
  Serial.printf("Updated fever threshold to %.2f°F\n", value);
}

void SettingsManager::setDistanceCompensation(float value) { 
  currentSettings.distanceCompensation = value; 
  Serial.printf("Updated distance compensation to %.2f°F\n", value);
}

void SettingsManager::setAmbientTemp(float value) {
  currentSettings.ambientTemp = value;
  Serial.printf("Updated ambient temperature to %.2f°F\n", value);
}

void SettingsManager::setPersonThreshold(float value) {
  currentSettings.personThreshold = value;
  Serial.printf("Updated person detection threshold to %.2f°F\n", value);
}

void SettingsManager::setBaselineTemp(float value) {
  currentSettings.baselineTemp = value;
  Serial.printf("Updated baseline temperature to %.2f°F\n", value);
}

void SettingsManager::setDynamicCompensation(bool enabled) {
  currentSettings.dynamicCompensation = enabled;
  Serial.printf("Dynamic compensation %s\n", enabled ? "enabled" : "disabled");
}

void SettingsManager::setCompensationSensitivity(float value) {
  currentSettings.compensationSensitivity = constrain(value, 0.0, 100.0);
  Serial.printf("Updated compensation sensitivity to %.1f%%\n", currentSettings.compensationSensitivity);
}

void SettingsManager::setBaselineEstablished(bool value) { 
  currentSettings.baselineEstablished = value; 
  Serial.printf("Baseline established: %s\n", value ? "YES" : "NO");
}

void SettingsManager::setMedicalThreshold(float value) { 
  currentSettings.medicalThreshold = value; 
  Serial.printf("Updated medical threshold to %.2f°F\n", value);
}

void SettingsManager::setSustainedTime(int value) { 
  currentSettings.sustainedTime = value; 
  Serial.printf("Updated sustained time to %d minutes\n", value);
}

void SettingsManager::setDebugMode(bool value) {
  currentSettings.debugMode = value;
  Serial.printf("Debug mode: %s\n", value ? "ENABLED" : "DISABLED");
}

void SettingsManager::setBaselineMin(float value) {
  currentSettings.baselineMin = value;
  Serial.printf("Updated baseline minimum to %.2f°F\n", value);
}

void SettingsManager::setDisplayBrightness(int value) {
  currentSettings.displayBrightness = value;
  Serial.printf("Updated display brightness to %d\n", value);
}

bool SettingsManager::isBuzzerEnabled() const {
  return currentSettings.buzzerEnabled;
}

void SettingsManager::setBuzzerEnabled(bool enabled) {
  currentSettings.buzzerEnabled = enabled;
  Serial.printf("Updated buzzer enabled to %s\n", enabled ? "true" : "false");
}

int SettingsManager::getReadInterval() const {
  return currentSettings.readInterval;
}

void SettingsManager::setReadInterval(int value) {
  currentSettings.readInterval = value;
  Serial.printf("Updated read interval to %d ms\n", value);
}

int SettingsManager::getLogInterval() const {
  return currentSettings.logInterval;
}

void SettingsManager::setLogInterval(int value) {
  currentSettings.logInterval = value;
  Serial.printf("Updated log interval to %d seconds\n", value);
}

bool SettingsManager::isDebugMode() const {
  return currentSettings.debugMode;
}

int SettingsManager::getDisplayBrightness() const {
  return currentSettings.displayBrightness;
}

// Time settings getters
const char* SettingsManager::getNtpServer() const { return currentSettings.ntpServer; }
const char* SettingsManager::getTimezone() const { return currentSettings.timezone; }
int SettingsManager::getGmtOffsetSec() const { return currentSettings.gmtOffsetSec; }
int SettingsManager::getDaylightOffsetSec() const { return currentSettings.daylightOffsetSec; }
bool SettingsManager::isAutoTimeSyncEnabled() const { return currentSettings.autoTimeSync; }
int SettingsManager::getTimeSyncInterval() const { return currentSettings.timeSyncInterval; }
bool SettingsManager::isNightModeEnabled() const { return currentSettings.nightModeEnabled; }
int SettingsManager::getNightStartHour() const { return currentSettings.nightStartHour; }
int SettingsManager::getNightStartMinute() const { return currentSettings.nightStartMinute; }
int SettingsManager::getNightEndHour() const { return currentSettings.nightEndHour; }
int SettingsManager::getNightEndMinute() const { return currentSettings.nightEndMinute; }
bool SettingsManager::isDisplayOffAtNight() const { return currentSettings.displayOffAtNight; }

// Time settings setters
void SettingsManager::setNtpServer(const char* server) {
  strncpy(currentSettings.ntpServer, server, sizeof(currentSettings.ntpServer) - 1);
  currentSettings.ntpServer[sizeof(currentSettings.ntpServer) - 1] = '\0';
  Serial.printf("Updated NTP server to %s\n", server);
}

void SettingsManager::setTimezone(const char* tz) {
  strncpy(currentSettings.timezone, tz, sizeof(currentSettings.timezone) - 1);
  currentSettings.timezone[sizeof(currentSettings.timezone) - 1] = '\0';
  Serial.printf("Updated timezone to %s\n", tz);
}

void SettingsManager::setGmtOffsetSec(int offset) {
  currentSettings.gmtOffsetSec = offset;
  Serial.printf("Updated GMT offset to %d seconds\n", offset);
}

void SettingsManager::setDaylightOffsetSec(int offset) {
  currentSettings.daylightOffsetSec = offset;
  Serial.printf("Updated daylight offset to %d seconds\n", offset);
}

void SettingsManager::setAutoTimeSync(bool enabled) {
  currentSettings.autoTimeSync = enabled;
  Serial.printf("Auto time sync: %s\n", enabled ? "ENABLED" : "DISABLED");
}

void SettingsManager::setTimeSyncInterval(int hours) {
  currentSettings.timeSyncInterval = hours;
  Serial.printf("Updated time sync interval to %d hours\n", hours);
}

void SettingsManager::setNightModeEnabled(bool enabled) {
  currentSettings.nightModeEnabled = enabled;
  Serial.printf("Night mode: %s\n", enabled ? "ENABLED" : "DISABLED");
}

void SettingsManager::setNightStartTime(int hour, int minute) {
  currentSettings.nightStartHour = hour;
  currentSettings.nightStartMinute = minute;
  Serial.printf("Updated night start time to %02d:%02d\n", hour, minute);
}

void SettingsManager::setNightEndTime(int hour, int minute) {
  currentSettings.nightEndHour = hour;
  currentSettings.nightEndMinute = minute;
  Serial.printf("Updated night end time to %02d:%02d\n", hour, minute);
}

void SettingsManager::setDisplayOffAtNight(bool enabled) {
  currentSettings.displayOffAtNight = enabled;
  Serial.printf("Display off at night: %s\n", enabled ? "ENABLED" : "DISABLED");
}

// Volume settings getters
int SettingsManager::getDayVolume() const {
  return currentSettings.dayVolume;
}

int SettingsManager::getNightVolume() const {
  return currentSettings.nightVolume;
}

int SettingsManager::getFeverVolume() const {
  return currentSettings.feverVolume;
}

// Volume settings setters
void SettingsManager::setDayVolume(int volume) {
  // Clamp volume to valid range (0-100)
  if (volume < 0) volume = 0;
  if (volume > 100) volume = 100;
  currentSettings.dayVolume = volume;
  Serial.printf("Updated day volume to %d%%\n", volume);
}

void SettingsManager::setNightVolume(int volume) {
  // Clamp volume to valid range (0-100)
  if (volume < 0) volume = 0;
  if (volume > 100) volume = 100;
  currentSettings.nightVolume = volume;
  Serial.printf("Updated night volume to %d%%\n", volume);
}

void SettingsManager::setFeverVolume(int volume) {
  // Clamp volume to valid range (0-100)
  if (volume < 0) volume = 0;
  if (volume > 100) volume = 100;
  currentSettings.feverVolume = volume;
  Serial.printf("Updated fever volume to %d%%\n", volume);
}

void SettingsManager::updateSettings(const ThermalSettings& settings) {
  currentSettings = settings;
  currentSettings.version = CURRENT_VERSION;
  Serial.println("Settings updated in memory");
}

void SettingsManager::applySettingsToGlobals() {
  // Apply settings to global variables in thermal_sensor.cpp
  feverThreshold = currentSettings.feverThreshold;
  distanceCompensation = currentSettings.distanceCompensation;
  ambientTemp = currentSettings.ambientTemp;
  personThreshold = currentSettings.personThreshold;
  baselineTemp = currentSettings.baselineTemp;
  baselineEstablished = currentSettings.baselineEstablished;
  baselineMin = currentSettings.baselineMin;

  // Apply buzzer setting
  enableBuzzer(currentSettings.buzzerEnabled);

  Serial.println("Settings applied to global variables");
}

void SettingsManager::collectSettingsFromGlobals() {
  // Collect current values from global variables
  currentSettings.feverThreshold = feverThreshold;
  currentSettings.distanceCompensation = distanceCompensation;
  currentSettings.ambientTemp = ambientTemp;
  currentSettings.personThreshold = personThreshold;
  currentSettings.baselineTemp = baselineTemp;
  currentSettings.baselineEstablished = baselineEstablished;
  currentSettings.baselineMin = baselineMin;

  Serial.println("Settings collected from global variables");
}

bool SettingsManager::hasStoredSettings() {
  return preferences.isKey("version");
}

void SettingsManager::printSettings() {
  Serial.println("=== Current Settings ===");
  Serial.printf("Fever Threshold: %.2f°F\n", currentSettings.feverThreshold);
  Serial.printf("Distance Compensation: %.2f°F\n", currentSettings.distanceCompensation);
  Serial.printf("Ambient Temperature: %.2f°F\n", currentSettings.ambientTemp);
  Serial.printf("Baseline Temperature: %.2f°F\n", currentSettings.baselineTemp);
  Serial.printf("Baseline Established: %s\n", currentSettings.baselineEstablished ? "YES" : "NO");
  Serial.printf("Baseline Minimum: %.2f°F\n", currentSettings.baselineMin);
  Serial.printf("Medical Threshold: %.2f°F\n", currentSettings.medicalThreshold);
  Serial.printf("Sustained Time: %d minutes\n", currentSettings.sustainedTime);
  Serial.printf("Debug Mode: %s\n", currentSettings.debugMode ? "ENABLED" : "DISABLED");
  Serial.printf("Version: %d\n", currentSettings.version);
  Serial.println("========================");
}

bool SettingsManager::migrateSettings(int fromVersion) {
  Serial.printf("Migrating settings from version %d\n", fromVersion);
  
  // Future migration logic would go here
  // For now, just ensure we have valid defaults for any new settings
  
  return true;
}

// Convenience functions
void initSettingsManager() {
  Serial.println("Starting settings manager initialization...");

  if (settingsManager.begin()) {
    Serial.println("Settings manager NVS initialized successfully");

    bool hasSettings = settingsManager.hasStoredSettings();
    Serial.printf("Stored settings found: %s\n", hasSettings ? "YES" : "NO");

    bool loadSuccess = settingsManager.loadSettings();
    Serial.printf("Settings load result: %s\n", loadSuccess ? "SUCCESS" : "USING_DEFAULTS");

    settingsManager.applySettingsToGlobals();
    Serial.println("Settings applied to global variables");

    // Print current settings for verification
    settingsManager.printSettings();
  } else {
    Serial.println("❌ Failed to initialize settings manager NVS, using defaults");
  }
}

void saveCurrentSettings() {
  settingsManager.collectSettingsFromGlobals();
  settingsManager.saveSettings();
}

void loadStoredSettings() {
  if (settingsManager.loadSettings()) {
    settingsManager.applySettingsToGlobals();
  }
}

void resetSettingsToDefaults() {
  settingsManager.resetToDefaults();
  settingsManager.applySettingsToGlobals();
}
