#ifndef TIME_MANAGER_H
#define TIME_MANAGER_H

#include <WiFi.h>
#include <time.h>
#include <sys/time.h>

// Time status enumeration
enum TimeStatus {
  TIME_NOT_SET,
  TIME_SYNCING,
  TIME_SYNCED,
  TIME_SYNC_FAILED
};

// Time manager class for NTP synchronization and time utilities
class TimeManager {
private:
  static TimeStatus currentStatus;
  static unsigned long lastSyncTime;
  static unsigned long lastSyncAttempt;
  static bool initialized;
  static char currentTimeString[32];
  static char currentDateString[32];
  
  // Internal helper methods
  static bool performNTPSync();
  static void updateTimeStrings();
  static bool isValidTime(struct tm* timeinfo);
  
public:
  // Initialization and configuration
  static bool begin();
  static bool begin(const char* ntpServer, int gmtOffsetSec, int daylightOffsetSec);
  static void end();
  
  // Time synchronization
  static bool syncTime();
  static bool syncTime(const char* ntpServer, int gmtOffsetSec, int daylightOffsetSec);
  static void setTimezone(const char* timezone);
  static void setTimezone(int gmtOffsetSec, int daylightOffsetSec);
  
  // Manual time setting
  static bool setTime(int year, int month, int day, int hour, int minute, int second);
  static bool setTime(time_t epochTime);
  
  // Time retrieval
  static time_t getCurrentTime();
  static struct tm getCurrentTimeStruct();
  static const char* getCurrentTimeString(bool includeSeconds = true);
  static const char* getCurrentDateString();
  static const char* getFormattedTime(const char* format);
  
  // Status and validation
  static TimeStatus getStatus();
  static bool isTimeSet();
  static bool isTimeSynced();
  static unsigned long getTimeSinceLastSync();
  static bool needsSync(int intervalHours = 24);
  
  // Night mode utilities
  static bool isNightTime(int startHour, int startMinute, int endHour, int endMinute);
  static bool isCurrentlyNightTime(int startHour, int startMinute, int endHour, int endMinute);
  static int getMinutesUntilTime(int targetHour, int targetMinute);
  static int getMinutesUntilNightEnd(int endHour, int endMinute);
  
  // Utility functions
  static String formatDuration(unsigned long seconds);
  static String formatTime(struct tm* timeinfo, bool includeSeconds = true);
  static String formatDate(struct tm* timeinfo);
  static bool parseTimeString(const char* timeStr, int& hour, int& minute, int& second);
  
  // Debug and logging
  static void printStatus();
  static void printCurrentTime();
  static String getStatusString();
};

// Global time manager instance
extern TimeManager timeManager;

// Convenience functions for global access
bool initTimeManager();
bool syncTimeNow();
bool isNightModeTime();
const char* getCurrentTimeStr();
TimeStatus getTimeStatus();
void handleTimeManager();

#endif
