#ifndef OLED_DISPLAY_H
#define OLED_DISPLAY_H

#include <Adafruit_GFX.h>
#include <Adafruit_SSD1306.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>

// OLED display configuration
#define SCREEN_WIDTH 128
#define SCREEN_HEIGHT 32
#define OLED_RESET -1  // Reset pin (not used)
#define SCREEN_ADDRESS 0x3C  // Common I2C address for 128x32 OLED (try 0x3D if this fails)

extern Adafruit_SSD1306 display;

// Animation task control
extern TaskHandle_t animationTaskHandle;
extern bool isWaitingForPresence;

void initDisplay();
void displayThermalData(const char* url);
void displayMessage(const char* message);
void clearDisplay();
void displayWaitingForPresence(); // New function for animated eyeballs
void startWaitingAnimation(); // Start the animation task (singleton pattern)
void stopWaitingAnimation(); // Stop the animation task
void animationTask(void *pvParameters); // FreeRTOS task function
bool isAnimationTaskRunning(); // Check if animation task is running and healthy
void forceStopAnimation(); // Force stop animation if it gets stuck
void setDisplayBrightness(uint8_t brightness); // Set display brightness (0-255)

#endif
