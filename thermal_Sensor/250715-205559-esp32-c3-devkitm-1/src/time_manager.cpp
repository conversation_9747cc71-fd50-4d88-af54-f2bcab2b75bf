#include "time_manager.h"
#include "settings_manager.h"
#include <WiFi.h>

// Static member initialization
TimeStatus TimeManager::currentStatus = TIME_NOT_SET;
unsigned long TimeManager::lastSyncTime = 0;
unsigned long TimeManager::lastSyncAttempt = 0;
bool TimeManager::initialized = false;
char TimeManager::currentTimeString[32] = "";
char TimeManager::currentDateString[32] = "";

// Global instance
TimeManager timeManager;

bool TimeManager::begin() {
  if (initialized) {
    return true;
  }
  
  Serial.println("Initializing Time Manager...");
  
  // Get settings from settings manager
  const char* ntpServer = settingsManager.getNtpServer();
  int gmtOffset = settingsManager.getGmtOffsetSec();
  int daylightOffset = settingsManager.getDaylightOffsetSec();
  
  return begin(ntpServer, gmtOffset, daylightOffset);
}

bool TimeManager::begin(const char* ntpServer, int gmtOffsetSec, int daylightOffsetSec) {
  if (initialized) {
    return true;
  }
  
  Serial.printf("Configuring time with NTP server: %s\n", ntpServer);
  Serial.printf("GMT Offset: %d seconds, Daylight Offset: %d seconds\n", gmtOffsetSec, daylightOffsetSec);
  
  // Configure time with NTP
  configTime(gmtOffsetSec, daylightOffsetSec, ntpServer);
  
  initialized = true;
  currentStatus = TIME_SYNCING;
  
  // Attempt initial sync
  if (syncTime()) {
    Serial.println("Time Manager initialized successfully");
    return true;
  } else {
    Serial.println("Time Manager initialized, but initial sync failed");
    return false;
  }
}

void TimeManager::end() {
  initialized = false;
  currentStatus = TIME_NOT_SET;
  Serial.println("Time Manager stopped");
}

bool TimeManager::performNTPSync() {
  if (!WiFi.isConnected()) {
    Serial.println("WiFi not connected - cannot sync time");
    return false;
  }
  
  Serial.println("Attempting NTP time synchronization...");
  currentStatus = TIME_SYNCING;
  lastSyncAttempt = millis();
  
  // Wait for time to be set (up to 10 seconds)
  int timeout = 10000;
  int elapsed = 0;
  
  while (elapsed < timeout) {
    struct tm timeinfo;
    if (getLocalTime(&timeinfo)) {
      if (isValidTime(&timeinfo)) {
        lastSyncTime = millis();
        currentStatus = TIME_SYNCED;
        updateTimeStrings();
        Serial.printf("Time synchronized: %s\n", getCurrentTimeString());
        return true;
      }
    }
    delay(500);
    elapsed += 500;
  }
  
  currentStatus = TIME_SYNC_FAILED;
  Serial.println("NTP synchronization failed - timeout");
  return false;
}

bool TimeManager::syncTime() {
  if (!initialized) {
    Serial.println("Time Manager not initialized");
    return false;
  }
  
  return performNTPSync();
}

bool TimeManager::syncTime(const char* ntpServer, int gmtOffsetSec, int daylightOffsetSec) {
  Serial.printf("Syncing time with server: %s\n", ntpServer);
  configTime(gmtOffsetSec, daylightOffsetSec, ntpServer);
  return performNTPSync();
}

void TimeManager::setTimezone(const char* timezone) {
  Serial.printf("Setting timezone: %s\n", timezone);
  setenv("TZ", timezone, 1);
  tzset();
  updateTimeStrings();
}

void TimeManager::setTimezone(int gmtOffsetSec, int daylightOffsetSec) {
  Serial.printf("Setting timezone offsets: GMT=%d, DST=%d\n", gmtOffsetSec, daylightOffsetSec);
  configTime(gmtOffsetSec, daylightOffsetSec, settingsManager.getNtpServer());
  updateTimeStrings();
}

bool TimeManager::setTime(int year, int month, int day, int hour, int minute, int second) {
  struct tm timeinfo = {0};
  timeinfo.tm_year = year - 1900;  // Years since 1900
  timeinfo.tm_mon = month - 1;     // Months since January (0-11)
  timeinfo.tm_mday = day;
  timeinfo.tm_hour = hour;
  timeinfo.tm_min = minute;
  timeinfo.tm_sec = second;
  
  time_t epochTime = mktime(&timeinfo);
  return setTime(epochTime);
}

bool TimeManager::setTime(time_t epochTime) {
  struct timeval tv;
  tv.tv_sec = epochTime;
  tv.tv_usec = 0;
  
  if (settimeofday(&tv, NULL) == 0) {
    currentStatus = TIME_SYNCED;
    lastSyncTime = millis();
    updateTimeStrings();
    Serial.printf("Time set manually: %s\n", getCurrentTimeString());
    return true;
  } else {
    Serial.println("Failed to set time manually");
    return false;
  }
}

time_t TimeManager::getCurrentTime() {
  return time(nullptr);
}

struct tm TimeManager::getCurrentTimeStruct() {
  struct tm timeinfo;
  getLocalTime(&timeinfo);
  return timeinfo;
}

const char* TimeManager::getCurrentTimeString(bool includeSeconds) {
  updateTimeStrings();
  if (includeSeconds) {
    return currentTimeString;
  } else {
    // Return time without seconds (HH:MM format)
    static char shortTimeString[16];
    struct tm timeinfo = getCurrentTimeStruct();
    strftime(shortTimeString, sizeof(shortTimeString), "%H:%M", &timeinfo);
    return shortTimeString;
  }
}

const char* TimeManager::getCurrentDateString() {
  updateTimeStrings();
  return currentDateString;
}

const char* TimeManager::getFormattedTime(const char* format) {
  static char formattedTime[64];
  struct tm timeinfo = getCurrentTimeStruct();
  strftime(formattedTime, sizeof(formattedTime), format, &timeinfo);
  return formattedTime;
}

TimeStatus TimeManager::getStatus() {
  return currentStatus;
}

bool TimeManager::isTimeSet() {
  return currentStatus == TIME_SYNCED;
}

bool TimeManager::isTimeSynced() {
  return currentStatus == TIME_SYNCED;
}

unsigned long TimeManager::getTimeSinceLastSync() {
  if (lastSyncTime == 0) {
    return 0;
  }
  return (millis() - lastSyncTime) / 1000; // Return in seconds
}

bool TimeManager::needsSync(int intervalHours) {
  if (currentStatus != TIME_SYNCED) {
    return true;
  }
  
  unsigned long intervalSeconds = intervalHours * 3600;
  return getTimeSinceLastSync() > intervalSeconds;
}

void TimeManager::updateTimeStrings() {
  struct tm timeinfo = getCurrentTimeStruct();
  strftime(currentTimeString, sizeof(currentTimeString), "%H:%M:%S", &timeinfo);
  strftime(currentDateString, sizeof(currentDateString), "%Y-%m-%d", &timeinfo);
}

bool TimeManager::isValidTime(struct tm* timeinfo) {
  // Check if time is reasonable (after year 2020)
  return timeinfo->tm_year >= 120; // 2020 = 1900 + 120
}

bool TimeManager::isNightTime(int startHour, int startMinute, int endHour, int endMinute) {
  struct tm timeinfo = getCurrentTimeStruct();
  int currentMinutes = timeinfo.tm_hour * 60 + timeinfo.tm_min;
  int startMinutes = startHour * 60 + startMinute;
  int endMinutes = endHour * 60 + endMinute;

  // Handle overnight periods (e.g., 23:00 to 08:00)
  if (startMinutes > endMinutes) {
    return (currentMinutes >= startMinutes) || (currentMinutes < endMinutes);
  } else {
    return (currentMinutes >= startMinutes) && (currentMinutes < endMinutes);
  }
}

bool TimeManager::isCurrentlyNightTime(int startHour, int startMinute, int endHour, int endMinute) {
  if (!isTimeSet()) {
    return false; // If time not set, assume it's not night time
  }
  return isNightTime(startHour, startMinute, endHour, endMinute);
}

int TimeManager::getMinutesUntilTime(int targetHour, int targetMinute) {
  struct tm timeinfo = getCurrentTimeStruct();
  int currentMinutes = timeinfo.tm_hour * 60 + timeinfo.tm_min;
  int targetMinutes = targetHour * 60 + targetMinute;

  if (targetMinutes > currentMinutes) {
    return targetMinutes - currentMinutes;
  } else {
    // Target time is tomorrow
    return (24 * 60) - currentMinutes + targetMinutes;
  }
}

int TimeManager::getMinutesUntilNightEnd(int endHour, int endMinute) {
  return getMinutesUntilTime(endHour, endMinute);
}

String TimeManager::formatDuration(unsigned long seconds) {
  unsigned long hours = seconds / 3600;
  unsigned long minutes = (seconds % 3600) / 60;
  unsigned long secs = seconds % 60;

  if (hours > 0) {
    return String(hours) + "h " + String(minutes) + "m " + String(secs) + "s";
  } else if (minutes > 0) {
    return String(minutes) + "m " + String(secs) + "s";
  } else {
    return String(secs) + "s";
  }
}

String TimeManager::formatTime(struct tm* timeinfo, bool includeSeconds) {
  char buffer[32];
  if (includeSeconds) {
    strftime(buffer, sizeof(buffer), "%H:%M:%S", timeinfo);
  } else {
    strftime(buffer, sizeof(buffer), "%H:%M", timeinfo);
  }
  return String(buffer);
}

String TimeManager::formatDate(struct tm* timeinfo) {
  char buffer[32];
  strftime(buffer, sizeof(buffer), "%Y-%m-%d", timeinfo);
  return String(buffer);
}

bool TimeManager::parseTimeString(const char* timeStr, int& hour, int& minute, int& second) {
  return sscanf(timeStr, "%d:%d:%d", &hour, &minute, &second) == 3;
}

void TimeManager::printStatus() {
  Serial.println("=== Time Manager Status ===");
  Serial.printf("Status: %s\n", getStatusString().c_str());
  Serial.printf("Initialized: %s\n", initialized ? "YES" : "NO");
  if (isTimeSet()) {
    Serial.printf("Current Time: %s\n", getCurrentTimeString());
    Serial.printf("Current Date: %s\n", getCurrentDateString());
    Serial.printf("Time since last sync: %s\n", formatDuration(getTimeSinceLastSync()).c_str());
  }
  Serial.println("===========================");
}

void TimeManager::printCurrentTime() {
  if (isTimeSet()) {
    Serial.printf("Current time: %s %s\n", getCurrentDateString(), getCurrentTimeString());
  } else {
    Serial.println("Time not set");
  }
}

String TimeManager::getStatusString() {
  switch (currentStatus) {
    case TIME_NOT_SET: return "Not Set";
    case TIME_SYNCING: return "Syncing";
    case TIME_SYNCED: return "Synced";
    case TIME_SYNC_FAILED: return "Sync Failed";
    default: return "Unknown";
  }
}

// Global convenience functions
bool initTimeManager() {
  return timeManager.begin();
}

bool syncTimeNow() {
  return timeManager.syncTime();
}

bool isNightModeTime() {
  if (!settingsManager.isNightModeEnabled()) {
    return false;
  }

  return timeManager.isCurrentlyNightTime(
    settingsManager.getNightStartHour(),
    settingsManager.getNightStartMinute(),
    settingsManager.getNightEndHour(),
    settingsManager.getNightEndMinute()
  );
}

const char* getCurrentTimeStr() {
  return timeManager.getCurrentTimeString();
}

TimeStatus getTimeStatus() {
  return timeManager.getStatus();
}

void handleTimeManager() {
  // Check for periodic time sync (only if auto sync is enabled)
  static unsigned long lastSyncCheck = 0;
  if (millis() - lastSyncCheck > 60000) { // Check every minute
    if (settingsManager.isAutoTimeSyncEnabled()) {
      int syncInterval = settingsManager.getTimeSyncInterval();
      if (timeManager.needsSync(syncInterval)) {
        Serial.println("Periodic time sync needed - attempting sync...");
        if (timeManager.syncTime()) {
          Serial.println("Periodic time sync successful");
        } else {
          Serial.println("Periodic time sync failed - will retry later");
        }
      }
    }
    lastSyncCheck = millis();
  }
}
