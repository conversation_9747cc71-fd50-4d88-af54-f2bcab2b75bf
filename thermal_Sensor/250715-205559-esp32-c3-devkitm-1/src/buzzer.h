#ifndef BUZZER_H
#define BUZZER_H

#include <Arduino.h>

// Buzzer configuration - 2-pin active buzzer (5V, works with 3.3V)
#define BUZZER_PIN 7  // GPIO 7 - connects to VC<PERSON> of buzzer, GND to ESP32 GND

// PWM configuration for volume control
#define BUZZER_PWM_CHANNEL 1     // PWM channel (0-15 for ESP32)
#define BUZZER_PWM_FREQ 1000     // PWM frequency in Hz
#define BUZZER_PWM_RES 8         // PWM resolution in bits (0-255)

// Buzzer states
enum BuzzerState {
  BUZZER_OFF,
  BUZZER_STARTUP,
  BUZZER_LEARNING,
  BUZZER_FEVER_ALERT
};

// Note frequencies for melodies (in Hz)
#define NOTE_C4  262
#define NOTE_D4  294
#define NOTE_E4  330
#define NOTE_F4  349
#define NOTE_G4  392
#define NOTE_A4  440
#define NOTE_B4  494
#define NOTE_C5  523
#define NOTE_D5  587
#define NOTE_E5  659
#define NOTE_F5  698
#define NOTE_G5  784
#define NOTE_A5  880
#define NOTE_B5  988
#define NOTE_C6  1047

// Rest note
#define NOTE_REST 0

// Note durations (in milliseconds)
#define WHOLE_NOTE    1000
#define HALF_NOTE     500
#define QUARTER_NOTE  250
#define EIGHTH_NOTE   125
#define SIXTEENTH_NOTE 62

// Global variables
extern BuzzerState currentBuzzerState;
extern unsigned long lastBuzzerAction;
extern bool buzzerEnabled;

// Function declarations
void initBuzzer();
void setBuzzerState(BuzzerState state);
void handleBuzzer();
void playStartupMelody();
void playLearningBeeps();
void playFeverAlert();
void stopBuzzer();
void playTone(int frequency, int duration);
void playMelody(const int melody[], const int durations[], int length);
bool isBuzzerPlaying();

// PWM volume control functions
void setBuzzerVolume(int volume);
int getBuzzerVolume();
void buzzerOn();
void buzzerOff();

// Settings functions
void enableBuzzer(bool enable);
bool isBuzzerEnabled();

// Volume helper functions
int getAppropriateVolume(bool isFeverAlert = false);
int convertPercentToVolume(int percent);

// Test functions
void testBuzzerVolume();

#endif
