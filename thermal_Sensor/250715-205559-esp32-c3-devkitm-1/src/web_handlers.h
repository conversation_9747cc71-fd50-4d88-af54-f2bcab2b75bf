#ifndef WEB_HANDLERS_H
#define WEB_HANDLERS_H

#include <WebServer.h>

// External web server instance
extern WebServer server;

// Static file handlers
void handleRoot();
void handleCSS();
void handleJS();
void handleServiceWorker();
void handleDebug();
void handleNotFound();

// API handlers
void handlePixels();
void handleFeverStatus();
void handleAmbientData();
void handleGetSettings();
void handleResetBaseline();
void handleSaveSettings();
void handleCalibrate();
void handleAutoCalibrate();
void handleResetSettings();
void handleSettingsDebug();
void handleTestSave();
void handleTestLoad();
void handleTestBuzzer();
void handleSSEEvents();
void handleSetDisplayBrightness();

// Time management handlers
void handleGetTimeStatus();
void handleSyncTime();
void handleSetTime();
void handleSaveTimeSettings();
void handleResetTimeSettings();

// Utility functions
String getContentType(String filename);
bool handleFileRead(String path);

#endif
