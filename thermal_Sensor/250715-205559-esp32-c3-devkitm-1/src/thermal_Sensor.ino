#include "wifi_manager.h"
#include "thermal_sensor.h"
#include "web_server.h"
#include "oled_display.h"
#include "settings_manager.h"
#include "aht10_sensor.h"
#include "time_manager.h"
#include "buzzer.h"
#include <ArduinoOTA.h>

void setup() {
  Serial.begin(115200);
  delay(500);

  // Initialize settings manager first to load stored configuration
  Serial.println("Initializing settings manager...");
  initSettingsManager();

  initBuzzer();  // Initialize buzzer first

  initWiFi();
  initSensor();
  initAHT10();  // Initialize AHT10 on separate I2C bus
  initDisplay();
  initTimeManager();  // Initialize time synchronization after WiFi
  initWebServer();
  ArduinoOTA.setHostname("esp32c3-ota");
  ArduinoOTA.begin();
  Serial.println("Setup complete");

  // Play startup melody after all setup is complete (silent during night mode)
  setBuzzerState(BUZZER_STARTUP);
}

void loop() {
  ArduinoOTA.handle();
  readPixels();
  String ipAddress = getWiFiIP();
  displayThermalData(ipAddress.c_str());
  handleWebServer();
  handleTimeManager();  // Handle periodic time sync
  handleBuzzer();  // Handle buzzer state and continuous alerts
  yield(); // Allow other tasks to run
}
